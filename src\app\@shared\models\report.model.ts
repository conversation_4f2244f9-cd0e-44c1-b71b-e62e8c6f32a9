import * as uuid from 'uuid';
import { AppConstants } from '../constants/app.constant';

export class ReportModel {
  public id: number;
  public CustomerName: string;
  public PortflioName: string;
  public siteName: string;
  public WorkorderName: string;
  public ReportCount: string;
  public siteId: number;
  public isSelected = false;
  public isCompleted: boolean;
  public reportId: string;
}

export class ReportModelDTO {
  public totalReport: number;
  public siteVisitReports: ReportModel[];
}

export class ImageGalleryDto {
  public customerName: string;
  public portfolioName: string;
  public siteName: string;
  public workOrderId: number;
  public workorderName: string;
  public images: ImageDto[];
  public customerId: number;
  public portfolioId: number;
  public siteId: number;
  public assesmentId: number;
  public assesmentType: number;
  public frequencyType: number;
  public workorderStatus: string;
}

export class ImageDto {
  public imageGuid: string;
  public imageUrl: string;
  public thumbnailUrl: string;
  public reportId: string;
  public IsIncludeinReport: boolean;
  public OriginalImage: string;
  public selectedIndex = false;
}

export class UploadReportDetail {
  id: string;
  reportId: string;
  imageURL: any;
  image: string;
  siteImageId: string;
}
export class AllReportModel {
  public customerName: string;
  public portfolioName: string;
  public siteName: string;
  public workorderName: string;
  public workorderId: number;
  public reportTitle: string;
  public reportCount: number;
  public reports: MasterReportModel[];
  public ncIssues: NCIssueList[];
  public ncActions: NCActionList[];
  public reportPdf: string;
  public reportPpt: string;
  public siteId: number;
  public customerId: number;
  public portfolioId: number;
  public assesmentId: number;
  public assesmentType: string;
  public frequencyType: string;
  public workorderStatus: string;
  public versionId: number;
  public fileId: number;
  public isUploadCompleted: boolean;
}

export class NCIssueList {
  id: number;
  isActive: boolean;
  issueObservation: string;
  componentId: number;
  componentStr: string;
}
export class NCActionList {
  id: number;
  isActive: boolean;
  action: string;
  componentId: number;
  issueObservationId: number;
  componentStr: string;
  issueObservationStr: string;
  actionRecommendation: string;
}

export class ListArray {
  id: number;
  name: string;
}
export class SiteImage {
  public image: any;
  public imageURL: string;
  public thumbnailUrl: string;
  public isReviewed: boolean;
  public order: any;
  public siteId: number;
  public siteImageId: string;
}
export class MasterReportModel {
  public order: number;
  public siteId: number;
  public isFinal: boolean;
  public reportTitle: string;
  public reporterName: string;
  public reportCreatedOn: Date;
  public reportCreatedOnStr: string;
  public reportUploadOn: Date;
  public reportUploadOnStr: string;
  public reportGuid: string;
  public jhaReports: MasterJHAReportModel;
  public checkListReport: MasterChecklistModel;
  public generalImages: MasterGeneralImagesModel[];
  public beforeImages: MasterGeneralImagesModel[] = [];
  public afterImages: MasterGeneralImagesModel[] = [];
  public nonConformances: MasterNonConformanceDto[];
  public nonConformanceCoordinate: NonConformanceGetCoordinates[];
  public ipmImages: MasterIPMImagesModel[];
  public siteImages: SiteImages;
  public siteReportImages: SiteReportImages;
  public ncOrderChange: boolean;
  public isFinalReportImageCount: boolean;
  public viewdeletetedbutton: boolean;
  public potentialHazardValues: ExpectedValue[];
  public uploadEquipmentTransDevices: MasterUploadEquipmentTransDevices[];
  public uploadEquipmentMVDevices: MasterUploadEquipmentMVDevices[];
  public uploadEquipmentPRDevices: MasterUploadEquipmentPRDevices[];
  public uploadRP: RiserPolesDto[];
  public jhaMap: [];
}

export class MasterJHAReportModel {
  public conductedBy: string;
  public createdDate: Date;

  public createdDateStr: string;
  public isFinal: boolean;
  public jhaGuid: string;
  public jhaPersonalAttending: JHAPAModel[];
  public jhaQERepresentitive: QERepresentativeModel[];
  public jhaSPH: JHASPHModel;
  public notes: string;
  public order: number;
  public otherHazards: string;
  public selectedItems: number[];
  public descriptionOfWork: ExpectedValue[];
  public potentialHazardValues: ExpectedValue[];
  public reportCreatedOn: Date;
  public reportCreatedOnStr: string;
  public reportGuid: string;
  public reportTitle: string;
  public reportUploadOn: Date;
  public reporterName: string;
  public reportUploadOnStr: string;
  public siteFacility: string;
  public siteContact: string;
  public sitePhone: string;
  public jhaMap: [];
}

export class DescriptionWork {
  public id: number;
  public name: string;
  public sectionId: number;
  public sectionName: string;
  public sectionAbbreviation: string;
  public abbreviation: string;
  public isSelected: boolean;
}

export class ExpectedValue {
  public id: number;
  public name: string;
  public sectionId: number;
  public sectionName: string;
  public sectionAbbreviation: string;
  public abbreviation: string;
  public isSelected: boolean;
  public sectionOverview: string[];
  public sectionDetails: string[];
  public categoryId: number;
  public categoryName: string;
}

export class QERepresentativeModel {
  public id: number;
  public userID: number;
  public name: string;
  public phone: number;
}

export class JHAPAModel {
  public paGuid: string;
  public jhaGuid: string;
  public name: string;
  public visitDates: Date;
  public visitDatestr: string;
  public imageGuid: string;
  public imageURL: string;
  public thumbnailUrl: string;
  public isIncludeReport: boolean;
  public originalImage: string;
}

export class JHASPHModel {
  public basicJobSteps: BasicJobSteps[];
  public potential: BasicJobSteps[];
  public hazardsControl: BasicJobSteps[];
}

export class BasicJobSteps {
  public jhaGuid: string;
  public sphGuid: string;
  public value: string;
  public type: string;
  public order: number;
}

export class MasterChecklistModel {
  public checklistValues: ExpectedValue[];
  public miscellaneous: string;
}

export class MasterGeneralImagesModel {
  public imageGuid: string;
  public imageUrl: string;
  public thumbnailUrl: string;
  public isIncludeinReport: boolean;
  public originalImage: string;
  public type: string;
  public order: number;
  public reportId: string;
}

export class UploadGeneralImages {
  public fileImage: File;
  public reportId: string;
  public order: number;
  public imageURL: string;
  public thumbnailUrl: string;
  public isIncludeInReport: boolean;
  public originalImageGuid: string;
  public groupId: number;
}

export class MasterNonConformanceDto {
  public ncGuid: string;
  public reportGuid: string;
  public order: number;
  public isResolve = false;
  public isUrgent: boolean;
  public component: number;
  public isLiftRequired: boolean = false;
  public specialTools: string = '';
  public estimatedHours: number;
  public materialsEstimate: string;
  public numberOfPeopleRequired: number;
  public componentStr: string;
  public location: string;
  public issue: string;
  public actions: string;
  public imageId: string;
  public nCIId: string;
  public componentList: NonConformanceComponentList[];
  public images: NonConformanceImagesById[];
  public isSelected: boolean;
  public isComponentDeleted: boolean;
  public isComponentActive: boolean;
}

export class NonConformanceImagesById {
  public fileImage: File;
  public reportId: string;
  public imageUrl: string;
  public thumbnailUrl: string;
  public isIncludeinReport: boolean;
  public originalImage: string;
  public groupId: number;
  public ncGuid: string;
  public nciGuid: string;
  public imageGuid: string;
  public order: number;
  public type: string;
}

export class NonConformanceComponentList {
  public abbreviation: string;
  public id: number;
  public isSelected: boolean;
  public name: string;
  public sectionAbbreviation: string;
  public sectionId: number;
  public sectionName: string;
}

export class NonConformanceGetCoordinates {
  public nccGuid: string;
  public ncGuid: string;
  public siteImageId: string;
  public x: number;
  public y: number;
  public markName: number;
  public width: number;
  public height: number;
  public dashGap: string;
  public dashWidth: string;
  public pointsSize: string;
  public fontSize: string;
  public lineWidth: string;
  public viewType: string;
  public viewColor: string;
  public imageHeight: number = 0;
  public imageWidth: number = 0;
}

export class SiteImages {
  public imageURL: string;
  public thumbnailUrl: string;
  public siteId: number;
  public siteImageId: string;
  public isReviewed: boolean;
  public order: number;
}

export class NonConformanceImageWithCoordinates {
  public nonConformance: MasterNonConformanceDto[] = [];
  public nonConformanceCoordinate: NonConformanceGetCoordinates[] = [];
  public originalSiteImage: string;
  public reportGuid: string;
  public reportSiteImages: string;
}

export class ResultModal {
  modalAction: number;
  markId: string;
}

export class NCSaveObject {
  imageUrl: string;
  thumbnailUrl: string;
  reportId: string;
  workorderId: number;
  siteImageId: string;
  nonConformance: NonConformanceGetCoordinates[] = [];
}

export class SiteReportImages {
  id: string;
  image: string;
  imageURL: string;
  thumbnailUrl: string;
  reportId: string;
}

export class UploadReport {
  public file: File;
  public reportId: string;
}

export class ReportType {
  public abbreviation: string;
  public id: number;
  public isActive: boolean;
  public name: string;
  public item_id: number;
  public item_text: string;
}

export class NewReport {
  public id: string;
  public workorderId: number;
  public workorderName: string;
  public siteId: number;
  public siteName: string;
  public portfolioId: number;
  public portfolioName: string;
  public customerId: number;
  public customerName: string;
  public reportType: number;
  public reportVersion: number = 3;
  public createdBy: number;
  public createdOn: string;
  public isFromMobileUploaded: boolean;
}

export interface GenerateReport {
  reportId: string;
  reportName: string;
  siteId: number;
  reportTypeId: number;
  workorderId: number;
  reporterId: number; // this is an user id
  reportCreatedDate: string;
  customerName?: string;
  portfolioName?: string;
  siteName?: string;
  reportVersion: number; // keeping 3 fix for now
  miscellaneous: string;
  comment: string;
  uploadJHA: any;
  uploadJHAv3ReportIds: string[];
  uploadEquipmentStatus: any;
  uploadChecklist: any;
  uploadNC: any;
  isFromMobileUploaded: boolean;
}

export class WorkOrderList {
  public id: number;
  public workOrderName: string;
}

export class AllReportDropdown {
  public ids: number[] = [];
  public customerIds?: number[] = [];
  public isActive = true;
}

export class UploadedReportList {
  public createdBy: number;
  public createdOn: string;
  public customerId: number;
  public customerName: string;
  public id: string;
  public portfolioId: number;
  public portfolioName: string;
  public reportType: number;
  public reports: ReportListPDFPPT[];
  public siteId: number;
  public siteName: string;
  public workorderId: number;
  public workorderName: string;
  public fileId: number;
}

export class ReportListPDFPPT {
  public fileType: string;
  public fileUrl: string;
  public id: string;
  public originalFileName: string;
  public reportId: string;
  public uploadedBy: number;
  public uploadedOn: string;
}

export class MasterIPMImagesModel {
  public giGuid: string;
  public height: number;
  public imageGuid: string;
  public imageUrl: string;
  public thumbnailUrl: string;
  public isIncludeinReport: boolean;
  public originalImage: string;
  public type: string;
  public order: number;
  public reportId: string;
  public width: number;
  public ipmItemGroupId: number;
}
export class ReportsFilter {
  public customerIds: number[] = [];
  public portfolioIds: number[] = [];
  public siteIds: number[] = [];
  public workorderId: number[] = [];
  public reportTypeIds: number[] = [];
  public frequencies: string[] = [];
  public year: number[] = [];
  public isDelete = false;
  public isArchive = false;
  public search: string;
  public page = 0;
  public sortBy = 'Year';
  public direction = 'desc';
  public itemsCount = +AppConstants.rowsPerPage;
}

export const REPORTSFILTERLIST = {
  customer: 'Customer',
  portfolio: 'Portfolio',
  site: 'Site',
  workOrder: 'Work Order',
  frequency: 'Frequency Type',
  reportType: 'Report Type',
  year: 'Year',
  isDelete: 'Show Deleted',
  isArchive: 'Show Archive'
};
export const SITEAUDITREPORTSFILTERLIST = {
  customer: 'Customer',
  portfolio: 'Portfolio',
  site: 'Site',
  year: 'Year'
};
export class DeleteIpmGroupby {
  public groupId: number;
  public reportId: string;
}

export class ExportNonConformanceReport {
  public customer: string;
  public portfolio: string;
  public site: string;
  public reportName: string;
  public id: string;
  public component: string; // ncItem
  public location: string; // addressed
  public issue: string; // Notes
  public actions: string;
  public urgent = false;
  public resolved = false;
  public isLiftRequired: boolean = false;
  public specialTools: string;
  public estimatedHours: number;
  public materialsEstimate: string;
  public numberOfPeopleRequired: number;
}
export class MasterUploadEquipmentTransDevices {
  public deviceTypeId: number;
  public esGuid: string;
  public siteDeviceName: string;
  public reportId: number;
  public equipmentStatusImages: EquipmentStatusImages[];
  public transformerEquipmentStatus: TransformerEquipmentStatusList[];
}

export class MasterEquipmentStatus {
  public fileId: number;
  public esGuid: string;
  public reportId: string;
  public siteDeviceId: number;
  public siteDeviceName: string;
  public equipmentStatusImages: EquipmentStatusImages[];
  public deviceTypeId: number;
  public transformerEquipmentStatus?: TransformerEquipmentStatusList = new TransformerEquipmentStatusList();
  public mvDisconnectedStatus?: MVDisconnectedStatus = new MVDisconnectedStatus();
  public protectiveRelayStatus?: ProtectiveRelayStatus = new ProtectiveRelayStatus();
}

export class EquipmentStatusImages {
  public fileImage: File;
  public reportId: string;
  public imageUrl: string;
  public thumbnailUrl: string;
  public isIncludeinReport: boolean;
  public originalImage: string;
  public groupId: number;
  public ncGuid: string;
  public nciGuid: string;
  public imageGuid: string;
  public order: number;
  public type: string;
  public imgId: string;
  public sectionId: number | null;
}
export class TransformerEquipmentStatusList {
  public transformerId: string;
  public topOilTemperature: string;
  public temperatureGauge: boolean;
  public sampleValve: boolean;
  public sampleValveExternal?: boolean;
  public nitrogenFillValveExternal?: boolean;
  public pressureGaugeExternal?: boolean;
  public temperatureGaugeExternal?: boolean;
  public pressureGauge: boolean;
  public pressureAsLeft: string;
  public pressureAsFound: string;
  public nitrogenFillValve: boolean;
  public nameplateImageGuid: string;
  public nameplateImageFileId: number;
  public nameplateImageURL: string;
  public maxTempReset: boolean;
  public maxOilTemperature: string;
  public liveSample: boolean;
  public liquidLevelGauge: boolean;
  public liquidLevel: string;
  public groundingTestResults: string;
  public esGuid: string;
  public detcPosition: string;
}
export class MasterUploadEquipmentMVDevices {
  public deviceTypeId: number;
  public esGuid: string;
  public siteDeviceName: string;
  public reportId: number;
  public mvDisconnectedStatus: MVDisconnectedStatus[];
}
export class MVDisconnectedStatus {
  public esGuid?: string;
  public mvDisconnectId?: string;
  public sF6Level?: string;
  public voltageIndicatorFunctional?: boolean;
  public liquidLevel?: string;
  public nameplateImageGuid?: string;
  public note: string;
}

export class MasterUploadEquipmentPRDevices {
  public deviceTypeId: number;
  public esGuid: string;
  public siteDeviceName: string;
  public reportId: number;
  public protectiveRelayStatus: ProtectiveRelayStatus[];
}

export class ProtectiveRelayStatus {
  public esGuid: string;
  public protectiveRelayId: string;
  public relay3PhaseOpenTest?: boolean;
  public relayLossOfPowerTest?: boolean;
  public relayInternalSelfTest?: boolean;
  public replaceDate?: Date;
  public replaceDateNA = false;
  public relayCalibrationDate?: Date;
  public relayCalibrationDateNA = false;
  public nameplateImageGuid?: string;
  public notes: string;
}

export class RiserPolesDto {
  public id: string;
  public imgUrl: string;
  public order: string;
  public reportId: string;
}

export const HAZARDLEVELLIST = [
  { name: 'Low', value: 'Low' },
  { name: 'Medium', value: 'Medium' },
  { name: 'High', value: 'High' }
];

export const BARRIERTYPELIST = [
  { name: 'Control', value: 'Control' },
  { name: 'Protective', value: 'Protective' },
  { name: 'Support', value: 'Support' }
];

export enum SA_REPORT_STATUS_ID_ENUMS {
  IN_PROGRESS = 1,
  COMPLETE = 2
}

export const SA_REPORT_STATUS_LABELS = {
  [SA_REPORT_STATUS_ID_ENUMS.IN_PROGRESS]: 'In Progress',
  [SA_REPORT_STATUS_ID_ENUMS.COMPLETE]: 'Complete'
};

export const SITE_AUDIT_REPORT_STATUS_LIST = [
  { name: SA_REPORT_STATUS_LABELS[SA_REPORT_STATUS_ID_ENUMS.IN_PROGRESS], id: SA_REPORT_STATUS_ID_ENUMS.IN_PROGRESS },
  { name: SA_REPORT_STATUS_LABELS[SA_REPORT_STATUS_ID_ENUMS.COMPLETE], id: SA_REPORT_STATUS_ID_ENUMS.COMPLETE }
];

export class SiteAuditReportModel {
  public id: number;
  public customerName: string;
  public portflioName: string;
  public siteName: string;
  public workorderName: string;
  public reportCount: string;
  public reportDocument: string;
  public reportId: string;
  public createdDate: string;
  public pdfUrl: string;
  public year: number;
  public reportStatusId: SA_REPORT_STATUS_ID_ENUMS;
  public reportStatusLabel: (typeof SA_REPORT_STATUS_LABELS)[SA_REPORT_STATUS_ID_ENUMS];
}

export class SiteAuditReportModelDTO {
  public totalReport: number;
  public siteAuditReports: SiteAuditReportModel[];
}

export enum ReportTypes {
  reportTypeSVId = 1,
  reportTypeJHAId = 11,
  reportTypeIPMId = 2,
  reportTypeVGTId = 8,
  reportTypeMVPMId = 3,
  reportTypeSAId = 13
}

export class SiteAuditNewJhaModel {
  reportId: string;
  customerName: string;
  portfolioName: string;
  siteName: string;
  customerId: number;
  portfolioId: number;
  siteId: number;
  jhaGuid: string;
  isFromWeb: boolean;
  isSiteAuditJHA: boolean;
  jhaDate: Date;
  siteContactPhone: number | string;

  constructor(siteAuditNewJhaModel: Partial<SiteAuditNewJhaModel>) {
    this.reportId = siteAuditNewJhaModel?.reportId ?? uuid.v4();
    this.customerName = siteAuditNewJhaModel?.customerName ?? '';
    this.portfolioName = siteAuditNewJhaModel?.portfolioName ?? '';
    this.siteName = siteAuditNewJhaModel?.siteName ?? '';
    this.customerId = siteAuditNewJhaModel?.customerId ?? 0;
    this.portfolioId = siteAuditNewJhaModel?.portfolioId ?? 0;
    this.siteId = siteAuditNewJhaModel?.siteId ?? 0;
    this.siteContactPhone = siteAuditNewJhaModel?.siteContactPhone ?? '';
    this.jhaGuid = siteAuditNewJhaModel?.jhaGuid ?? uuid.v4();
    this.isFromWeb = siteAuditNewJhaModel?.isFromWeb ?? true;
    this.jhaDate = siteAuditNewJhaModel?.jhaDate ? new Date(siteAuditNewJhaModel.jhaDate) : new Date();
  }
}

export class StartNewSiteAuditReportModel extends SiteAuditReportModel {
  reportId: string;
  customerName: string;
  portfolioName: string;
  siteName: string;
  reportName: string;
  reportTypeId: ReportTypes;
  versionId: number = 3;

  // optional
  reportTitle: string;
  workorderName: string;
  reportCount: string;
  reportGuid: string;
  pdfUrl: string;
  year: number;
  isFromWeb: boolean;

  constructor(siteAuditReportModel: Partial<StartNewSiteAuditReportModel>) {
    super();
    const todayDate = new Date();
    const randomUUID = uuid.v4();
    const formatedDate =
      todayDate.getFullYear().toString() + String(todayDate.getMonth() + 1).padStart(2, '0') + String(todayDate.getDate()).padStart(2, '0');

    this.reportId = siteAuditReportModel?.reportId ?? randomUUID;
    this.customerName = siteAuditReportModel?.customerName ?? '';
    this.portfolioName = siteAuditReportModel?.portfolioName ?? '';
    this.siteName = siteAuditReportModel?.siteName ?? '';
    this.reportName = `SA-${formatedDate}-${this.siteName}`;
    this.reportTypeId = siteAuditReportModel?.reportTypeId ?? ReportTypes.reportTypeSAId;
    this.versionId = siteAuditReportModel?.versionId ?? 3;

    // optional
    this.reportTitle = `SA-${formatedDate}-${this.siteName}`;
    this.workorderName = siteAuditReportModel?.workorderName ?? '';
    this.reportCount = siteAuditReportModel?.reportCount ?? '';
    this.reportGuid = siteAuditReportModel?.reportGuid ?? randomUUID;
    this.pdfUrl = siteAuditReportModel?.pdfUrl ?? '';
    this.year = siteAuditReportModel?.year ?? todayDate.getFullYear();
    this.isFromWeb = siteAuditReportModel?.isFromWeb ?? true;
  }
}

export class ReportDeepCheckDataClass {
  reportMasterList: AllReportModel;
  reports: MasterReportModel[];
  constructor(reportMasterList: AllReportModel, reports: MasterReportModel[]) {
    this.reportMasterList = reportMasterList;
    this.reports = reports;
  }
}

export class SiteAuditCPSListDataClass {
  customerList: string[];
  portfolioList: string[];
  siteList: string[];
  constructor(customerList: string[] = [], portfolioList: string[] = [], siteList: string[] = []) {
    this.customerList = customerList;
    this.portfolioList = portfolioList;
    this.siteList = siteList;
  }
}

export class SiteAuditCPSValueClass {
  customerName: string;
  portfolioName: string;
  siteName: string;
  constructor(customerName: string = '', portfolioName: string = '', siteName: string = '') {
    this.customerName = customerName.trim();
    this.portfolioName = portfolioName.trim();
    this.siteName = siteName.trim();
  }
}

export const pmReportListingPageFilterKeys = [
  'customerIds',
  'portfolioIds',
  'siteIds',
  'regionIds',
  'subregionIds',
  'reportTypeIds',
  'frequencies',
  'isDelete',
  'isArchive'
];

export enum NC_ACTION_ISSUE_TYPE {
  OTHER = 'Other'
}
