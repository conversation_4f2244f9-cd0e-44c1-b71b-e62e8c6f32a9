import { DatePipe } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiUrl, AppConstants } from '../../../@shared/constants';
import {
  ChartViewList,
  DeviceRecords,
  FilterModel,
  INVERTER_COLORS,
  POWER_CHART_MARK_LINE_ENUM,
  PowerChartResponse,
  PowerChartSitesDateTime,
  PowerChartSiteSunriseSunsetData
} from '../power-chart/power-chart.model';
import { PowerCardsResponse } from './power-cards-model';

@Injectable({
  providedIn: 'root'
})
export class PowerCardsService {
  chartViewList = [
    { displayName: 'Plot Irradiance', isSelected: true, name: 'Irradiance', disabled: false },
    { displayName: 'Plot Meters', isSelected: true, name: 'Meters', disabled: false },
    { displayName: 'Sum Meters', isSelected: false, name: 'Meter Sum', disabled: false },
    { displayName: 'Plot Inverters', isSelected: true, name: 'Inverters', disabled: false },
    { displayName: 'Sum Inverters', isSelected: false, name: 'Inverter Sum', disabled: false },
    { displayName: 'Sunrise/Sunset', isSelected: true, name: 'Sunrise/Sunset', disabled: false }
  ];

  constructor(private readonly http: HttpClient) {}

  viewPowerCardsData(data: any): Observable<PowerCardsResponse[]> {
    return this.http.post<PowerCardsResponse[]>(ApiUrl.GET_POWER_CARDS, data, {
      headers: new HttpHeaders({ timeout: `${1000 * 60 * 5}` })
    });
  }

  getSiteSunriseSunsetChartsInfo(data: FilterModel): Observable<PowerChartSiteSunriseSunsetData[]> {
    return this.http.post<PowerChartSiteSunriseSunsetData[]>(ApiUrl.GET_SUNRISE_SUNSET_BY_SITE_IDS, data, {
      headers: new HttpHeaders({ timeout: `${1000 * 60 * 5}` })
    });
  }

  getChartsData(data: FilterModel): Observable<PowerChartResponse> {
    return this.http.post<PowerChartResponse>(ApiUrl.GET_POWER_CHARTS, data, {
      headers: new HttpHeaders({ timeout: `${1000 * 60 * 5}` })
    });
  }

  generateSingleSiteChart(
    deviceData: DeviceRecords[],
    powerChartSiteSunriseSunsetData: PowerChartSiteSunriseSunsetData[],
    powerChartSitesDateTimeData: PowerChartSitesDateTime[],
    siteName: string = '',
    isSingleTooltip: boolean = false,
    customChartViewList: ChartViewList[] = []
  ): any {
    if (!deviceData?.length) {
      return null;
    }
    const siteData = deviceData[0];
    const sunriseSunsetData = powerChartSiteSunriseSunsetData.find(x => x.qesiteid === siteData.qesiteid);
    if (customChartViewList && customChartViewList.length) {
      this.chartViewList = customChartViewList;
    }
    const isSelect = this.chartViewList.find(x => x.displayName === 'Plot Inverters').isSelected;
    const sunriseSunsetSelected = this.chartViewList.find(x => x.displayName === 'Sunrise/Sunset').isSelected;

    const sunriseSunsetMarkAreaData = sunriseSunsetSelected && sunriseSunsetData ? sunriseSunsetData.qesiteSunriseSunsetMarkArea ?? [] : [];

    const sunriseSunsetMarkLineData = sunriseSunsetSelected && sunriseSunsetData ? sunriseSunsetData.qesiteSunriseSunsetMarkLine ?? [] : [];

    const powerChartSitesMarkLineData =
      powerChartSitesDateTimeData
        .filter((x: PowerChartSitesDateTime) => x.qesiteid === siteData.qesiteid)
        .flatMap((x: PowerChartSitesDateTime) => x.powerChartSitesMarkLine) ?? [];

    const markArea = {
      itemStyle: {
        color: 'rgba(143,155,179,.24)'
      },
      silent: true,
      data: sunriseSunsetMarkAreaData
    };

    const markLine = {
      symbol: 'none',
      silent: false,
      label: {
        show: false
      },
      lineStyle: {
        type: 'solid',
        color: 'rgba(143,155,179,.24)',
        width: 2
      },
      tooltip: {
        confine: true,
        trigger: 'item',
        className: sunriseSunsetSelected ? 'tooltipText' : '',
        formatter: function (params) {
          const formattedDateFn = (dateTimeFormat: string = AppConstants.powerChartSiteDateTimeFormat) =>
            new DatePipe('en-US').transform(new Date(params.data.xAxis), dateTimeFormat);

          const tooltipFormatterFn = (): string => {
            switch (params.data.markLineEnum) {
              case POWER_CHART_MARK_LINE_ENUM.SUNRISE_LINE:
              case POWER_CHART_MARK_LINE_ENUM.SUNSET_LINE:
                return `${params.name}: ${formattedDateFn()}`;
              case POWER_CHART_MARK_LINE_ENUM.LAST_DATA_POINT_LINE:
                return `${params.name}: ${formattedDateFn(AppConstants.powerChartSiteTimeFormat)} (Local Site Time)`;
              case POWER_CHART_MARK_LINE_ENUM.SITE_CURRENT_TIME_LINE:
                return `${params.name}: ${formattedDateFn(AppConstants.powerChartSiteTimeFormat)}`;
              default:
                return '';
            }
          };

          return tooltipFormatterFn();
        }
      },
      data: [...sunriseSunsetMarkLineData, ...powerChartSitesMarkLineData]
    };

    const data = {
      customObj: {
        qeSiteId: deviceData[0].qesiteid,
        qeSiteName: deviceData[0].qesitename,
        qePortfolioName: deviceData[0].portfolioid,
        qePortfolioId: deviceData[0].portfolioid
      },
      title: {
        text: siteName ?? deviceData[0].qesitename,
        left: 'center',
        top: 45
      },
      grid: {
        y: 100,
        containLabel: true
      },
      responsive: true,
      tooltip: {
        confine: true,
        trigger: isSingleTooltip ? 'item' : 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        },
        extraCssText: (isSelect ? 'width:auto; max-width:550px; white-space:pre-wrap;' : '') + 'z-index: 99999',
        className: isSelect ? 'tooltipText' : '',
        position: function (point, params, dom, rect, size) {
          const [x, y] = point;
          const tooltipWidth = size.contentSize[0];
          const tooltipHeight = size.contentSize[1];
          const chartWidth = size.viewSize[0];
          const chartHeight = size.viewSize[1];

          let xPos = x + tooltipWidth > chartWidth ? x - tooltipWidth : x;
          let yPos = y + tooltipHeight > chartHeight ? y - tooltipHeight : y;

          if (xPos < 0) xPos = 0;
          if (yPos < 0) yPos = 0;

          return [xPos, yPos];
        }
      },
      toolbox: {
        feature: {
          dataZoom: { show: true },
          dataView: {
            show: true,
            readOnly: true,
            optionToContent: function (opt) {
              let series = opt.series;
              let table = '<table class="table table-bordered"><thead><tr>' + '<th>Time</th>';
              for (let i = 0, l = series.length; i < l; i++) {
                table += `<th>${series[i].name}</th>`;
              }
              table += '</tr></thead><tbody style="color:#000000">';
              for (let i = 0, l = series[0].data.length; i < l; i++) {
                table += '<tr>';
                table += `<td style="color:#000000">${new Date(series[0].data[i][0])?.toLocaleString()}</td>`;
                for (let j = 0, m = series.length; j < m; j++) {
                  table += `<td style="text-align:right; color:#000000;">${series[j].data[i][1]?.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  })}</td>`;
                }
                table += '</tr>';
              }
              table += '</thead></table>';
              return table;
            }
          },
          magicType: { show: true, type: ['line', 'bar'] },
          saveAsImage: { show: true }
        },
        top: 7,
        right: 40
      },
      legend: {
        data: [],
        type: 'scroll',
        bottom: 10
      },
      xAxis: [
        {
          type: 'time',
          data: [],
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            hideOverlap: true
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: 'kW',
          nameTextStyle: {
            fontWeight: 'bolder',
            align: 'right',
            padding: [0, 6, 0, 0]
          },
          min: 0,
          splitLine: {
            show: true
          },
          formatter: function (value) {
            return value?.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          },
          axisLabel: {
            formatter: function (value) {
              return value?.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              });
            }
          }
        }
      ],
      series: []
    };

    // Group devices by type
    const deviceGroupBy = deviceData.reduce((group, product) => {
      const { devicetype } = product;
      group[devicetype] = group[devicetype] ?? [];
      group[devicetype].push(product);
      return group;
    }, {});

    const sumOfMeter = [];
    const sumOfInverter = [];

    // Process chart view options
    this.chartViewList.forEach(element => {
      if (element.displayName === 'Plot Irradiance' && element.isSelected) {
        this.processIrradianceData(deviceGroupBy, data, markArea, markLine, isSingleTooltip);
      } else if (element.displayName === 'Plot Meters' && element.isSelected) {
        this.processMeterData(deviceGroupBy, data, markArea, markLine, sumOfMeter, isSingleTooltip);
      } else if (element.displayName === 'Sum Meters' && element.isSelected) {
        this.processSumMeterData(deviceGroupBy, data, markArea, markLine, sumOfMeter, isSingleTooltip);
      } else if (element.displayName === 'Plot Inverters' && element.isSelected) {
        this.processInverterData(deviceGroupBy, data, markArea, markLine, sumOfInverter, isSingleTooltip);
      } else if (element.displayName === 'Sum Inverters' && element.isSelected) {
        this.processSumInverterData(deviceGroupBy, data, markArea, markLine, sumOfInverter, isSingleTooltip);
      }
    });

    return data;
  }

  private processIrradianceData(deviceGroupBy: any, data: any, markArea: any, markLine: any, isSingleTooltip: boolean) {
    const tempData = deviceGroupBy['12'];
    let count = 0;
    if (tempData?.length) {
      const devices = tempData.reduce((group, device) => {
        const { qedeviceid } = device;
        group[qedeviceid] = group[qedeviceid] ?? [];
        group[qedeviceid].push(device);
        return group;
      }, {});

      for (let key in devices) {
        const value = devices[key];
        const obj = [];
        for (const i of value) {
          if (i.bindata) {
            count++;
          }
          obj.push([new Date(i.bindatetime), i.bindata]);
        }
        data.legend.data.push(value[0].qedevicename);
        data.series.push({
          color: '#FE8713',
          yAxisIndex: count > 0 ? 1 : 0,
          name: value[0].qedevicename,
          type: 'line',
          smooth: true,
          tooltip: {
            confine: true,
            position: function (point, params, dom, rect, size) {
              const [x, y] = point;
              const tooltipWidth = size.contentSize[0];
              const tooltipHeight = size.contentSize[1];
              const chartWidth = size.viewSize[0];
              const chartHeight = size.viewSize[1];

              let xPos = x + tooltipWidth > chartWidth ? x - tooltipWidth : x;
              let yPos = y + tooltipHeight > chartHeight ? y - tooltipHeight : y;
              if (xPos < 0) xPos = 0;
              if (yPos < 0) yPos = 0;

              return [xPos, yPos];
            },
            valueFormatter: function (value: any) {
              return value || value === 0
                ? Number(value)?.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  }) + ' W/m²'
                : 'N/A';
            }
          },
          responsive: true,
          data: obj,
          symbolSize: isSingleTooltip ? 4 : 1,
          markArea,
          markLine
        });
      }

      if (count > 0) {
        data.yAxis.push({
          type: 'value',
          name: 'W/m²',
          nameTextStyle: {
            fontWeight: 'bolder',
            align: 'left',
            padding: [0, 0, 0, 6]
          },
          min: 0,
          splitLine: {
            show: true
          },
          formatter: function (value) {
            return value?.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          },
          axisLabel: {
            formatter: function (value) {
              return value?.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              });
            }
          }
        });
      }
    }
  }

  private processMeterData(deviceGroupBy: any, data: any, markArea: any, markLine: any, sumOfMeter: any[], isSingleTooltip: boolean) {
    const tempData = deviceGroupBy['2'];
    if (tempData?.length) {
      const devices = tempData.reduce((group, device) => {
        const { qedeviceid } = device;
        group[qedeviceid] = group[qedeviceid] ?? [];
        group[qedeviceid].push(device);
        return group;
      }, {});

      let meterCount = 0;
      for (let key in devices) {
        const value = devices[key];
        const obj = [];
        for (const i of value) {
          obj.push([new Date(i.bindatetime), i.bindata]);
          if (!meterCount) {
            sumOfMeter.push([new Date(i.bindatetime), i.bindata]);
          } else {
            sumOfMeter[obj.length - 1][1] = sumOfMeter[obj.length - 1][1]
              ? Math.round((Number(sumOfMeter[obj.length - 1][1]) + Number(i.bindata)) * 100) / 100
              : sumOfMeter[obj.length - 1][1];
          }
        }
        data.legend.data.push(value[0].qedevicename);
        data.series.push({
          name: value[0].qedevicename,
          type: 'line',
          smooth: true,
          color: '#4589FF',
          tooltip: {
            confine: true,
            position: function (point, params, dom, rect, size) {
              const [x, y] = point;
              const tooltipWidth = size.contentSize[0];
              const tooltipHeight = size.contentSize[1];
              const chartWidth = size.viewSize[0];
              const chartHeight = size.viewSize[1];

              let xPos = x + tooltipWidth > chartWidth ? x - tooltipWidth : x;
              let yPos = y + tooltipHeight > chartHeight ? y - tooltipHeight : y;
              if (xPos < 0) xPos = 0;
              if (yPos < 0) yPos = 0;

              return [xPos, yPos];
            },
            valueFormatter: function (value: any) {
              return value || value === 0
                ? Number(value)?.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  }) + ' kW'
                : 'N/A';
            }
          },
          data: obj,
          responsive: true,
          symbolSize: isSingleTooltip ? 4 : 1,
          markArea,
          markLine
        });
        meterCount++;
      }
    }
  }

  private processSumMeterData(deviceGroupBy: any, data: any, markArea: any, markLine: any, sumOfMeter: any[], isSingleTooltip: boolean) {
    if (sumOfMeter.length) {
      data.legend.data.push('Meter Sum');
      data.series.push({
        name: 'Meter Sum',
        type: 'line',
        smooth: true,
        color: '#4589FF',
        tooltip: {
          confine: true,
          position: function (point, params, dom, rect, size) {
            const [x, y] = point;
            const tooltipWidth = size.contentSize[0];
            const tooltipHeight = size.contentSize[1];
            const chartWidth = size.viewSize[0];
            const chartHeight = size.viewSize[1];

            let xPos = x + tooltipWidth > chartWidth ? x - tooltipWidth : x;
            let yPos = y + tooltipHeight > chartHeight ? y - tooltipHeight : y;
            if (xPos < 0) xPos = 0;
            if (yPos < 0) yPos = 0;

            return [xPos, yPos];
          },
          valueFormatter: function (value: any) {
            return value || value === 0
              ? Number(value)?.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + ' kW'
              : 'N/A';
          }
        },
        data: sumOfMeter,
        responsive: true,
        symbolSize: isSingleTooltip ? 4 : 1,
        markArea,
        markLine
      });
    } else {
      // Calculate sum if not already done
      const tempData = deviceGroupBy['2'];
      if (tempData?.length) {
        const devices = tempData.reduce((group, device) => {
          const { qedeviceid } = device;
          group[qedeviceid] = group[qedeviceid] ?? [];
          group[qedeviceid].push(device);
          return group;
        }, {});

        let meterCount = 0;
        for (let key in devices) {
          const value = devices[key];
          for (const i of value) {
            if (!meterCount) {
              sumOfMeter.push([new Date(i.bindatetime), i.bindata]);
            } else {
              sumOfMeter[sumOfMeter.length - 1][1] = sumOfMeter[sumOfMeter.length - 1][1]
                ? Math.round((sumOfMeter[sumOfMeter.length - 1][1] + i.bindata) * 100) / 100
                : sumOfMeter[sumOfMeter.length - 1][1];
            }
          }
          meterCount++;
        }

        data.legend.data.push('Meter Sum');
        data.series.push({
          name: 'Meter Sum',
          color: '#4589FF',
          type: 'line',
          smooth: true,
          tooltip: {
            confine: true,
            valueFormatter: function (value: any) {
              return value || value === 0
                ? Number(value)?.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  }) + ' kW'
                : 'N/A';
            }
          },
          data: sumOfMeter,
          responsive: true,
          symbolSize: isSingleTooltip ? 4 : 1,
          markArea,
          markLine
        });
      }
    }
  }

  private processInverterData(deviceGroupBy: any, data: any, markArea: any, markLine: any, sumOfInverter: any[], isSingleTooltip: boolean) {
    const tempData = deviceGroupBy['1'];
    if (tempData?.length) {
      const devices = tempData.reduce((group, device) => {
        const { qedeviceid } = device;
        group[qedeviceid] = group[qedeviceid] ?? [];
        group[qedeviceid].push(device);
        return group;
      }, {});

      let inverterCount = 0;
      const colorList = INVERTER_COLORS;
      let colorIndex = 0;

      for (let key in devices) {
        const value = devices[key];
        const obj = [];
        for (const i of value) {
          obj.push([new Date(i.bindatetime), i.bindata]);
          if (!inverterCount) {
            sumOfInverter.push([new Date(i.bindatetime), i.bindata]);
          } else {
            sumOfInverter[obj.length - 1][1] = sumOfInverter[obj.length - 1][1]
              ? Math.round((Number(sumOfInverter[obj.length - 1][1]) + Number(i.bindata)) * 100) / 100
              : sumOfInverter[obj.length - 1][1];
          }
        }
        inverterCount++;
        data.legend.data.push(value[0].qedevicename);

        let color = colorList[colorIndex] || colorList[0];
        colorIndex = (colorIndex + 1) % colorList.length;

        data.series.push({
          name: value[0].qedevicename,
          type: 'line',
          smooth: true,
          color: color,
          tooltip: {
            confine: true,
            position: function (point, params, dom, rect, size) {
              const [x, y] = point;
              const tooltipWidth = size.contentSize[0];
              const tooltipHeight = size.contentSize[1];
              const chartWidth = size.viewSize[0];
              const chartHeight = size.viewSize[1];

              let xPos = x + tooltipWidth > chartWidth ? x - tooltipWidth : x;
              let yPos = y + tooltipHeight > chartHeight ? y - tooltipHeight : y;
              if (xPos < 0) xPos = 0;
              if (yPos < 0) yPos = 0;

              return [xPos, yPos];
            },
            valueFormatter: function (value: any) {
              return value || value === 0
                ? Number(value)?.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  }) + ' kW'
                : 'N/A';
            }
          },
          data: obj,
          responsive: true,
          symbolSize: isSingleTooltip ? 4 : 1,
          markArea,
          markLine
        });
      }
    }
  }

  private processSumInverterData(
    deviceGroupBy: any,
    data: any,
    markArea: any,
    markLine: any,
    sumOfInverter: any[],
    isSingleTooltip: boolean
  ) {
    if (sumOfInverter.length) {
      data.legend.data.push('Inverter Sum');
      data.series.push({
        name: 'Inverter Sum',
        color: '#00B050',
        type: 'line',
        smooth: true,
        tooltip: {
          confine: true,
          position: function (point, params, dom, rect, size) {
            const [x, y] = point;
            const tooltipWidth = size.contentSize[0];
            const tooltipHeight = size.contentSize[1];
            const chartWidth = size.viewSize[0];
            const chartHeight = size.viewSize[1];

            let xPos = x + tooltipWidth > chartWidth ? x - tooltipWidth : x;
            let yPos = y + tooltipHeight > chartHeight ? y - tooltipHeight : y;
            if (xPos < 0) xPos = 0;
            if (yPos < 0) yPos = 0;

            return [xPos, yPos];
          },
          valueFormatter: function (value: any) {
            return value || value === 0
              ? Number(value)?.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }) + ' kW'
              : 'N/A';
          }
        },
        data: sumOfInverter,
        responsive: true,
        symbolSize: isSingleTooltip ? 4 : 1,
        markArea,
        markLine
      });
    } else {
      // Calculate sum if not already done
      const tempData = deviceGroupBy['1'];
      if (tempData?.length) {
        const devices = tempData.reduce((group, device) => {
          const { qedeviceid } = device;
          group[qedeviceid] = group[qedeviceid] ?? [];
          group[qedeviceid].push(device);
          return group;
        }, {});

        let inverterCount = 0;
        for (let key in devices) {
          const value = devices[key];
          for (const i of value) {
            if (!inverterCount) {
              sumOfInverter.push([new Date(i.bindatetime), i.bindata]);
            } else {
              sumOfInverter[sumOfInverter.length - 1][1] = sumOfInverter[sumOfInverter.length - 1][1]
                ? Math.round((sumOfInverter[sumOfInverter.length - 1][1] + i.bindata) * 100) / 100
                : sumOfInverter[sumOfInverter.length - 1][1];
            }
          }
          inverterCount++;
        }

        data.legend.data.push('Inverter Sum');
        data.series.push({
          name: 'Inverter Sum',
          color: '#00B050',
          type: 'line',
          smooth: true,
          tooltip: {
            confine: true,
            position: function (point, params, dom, rect, size) {
              const [x, y] = point;
              const tooltipWidth = size.contentSize[0];
              const tooltipHeight = size.contentSize[1];
              const chartWidth = size.viewSize[0];
              const chartHeight = size.viewSize[1];

              let xPos = x + tooltipWidth > chartWidth ? x - tooltipWidth : x;
              let yPos = y + tooltipHeight > chartHeight ? y - tooltipHeight : y;
              if (xPos < 0) xPos = 0;
              if (yPos < 0) yPos = 0;

              return [xPos, yPos];
            },
            valueFormatter: function (value: any) {
              return value || value === 0
                ? Number(value)?.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  }) + ' kW'
                : 'N/A';
            }
          },
          data: sumOfInverter,
          responsive: true,
          symbolSize: isSingleTooltip ? 4 : 1,
          markArea,
          markLine
        });
      }
    }
  }
}
