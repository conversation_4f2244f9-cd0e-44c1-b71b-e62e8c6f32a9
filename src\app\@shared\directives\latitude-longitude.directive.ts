import { Directive, ElementRef, HostListener, Input } from '@angular/core';

@Directive({
  selector: '[appLatitudeLongitude]'
})
export class LatitudeLongitudeDirective {
  @Input('appLatitudeLongitude') type: 'lat' | 'lng' = 'lat';

  constructor(private el: ElementRef<HTMLInputElement>) {}

  @HostListener('input', ['$event'])
  onInput(event: Event) {
    const input = this.el.nativeElement;
    let value = input.value;

    value = value.replace(/[^0-9.\-]/g, '');

    const parts = value.split('.');
    if (parts.length > 2) {
      value = parts.slice(0, 2).join('.');
    }

    if ((value.match(/-/g) || []).length > 1) {
      value = value.replace(/-/g, '');
    }
    if (value.indexOf('-') > 0) {
      value = value.replace(/-/g, '');
      value = '-' + value;
    }

    const num = parseFloat(value);
    if (!isNaN(num)) {
      if (this.type === 'lat' && (num < -90 || num > 90)) {
        value = input.value.slice(0, -1);
      } else if (this.type === 'lng' && (num < -180 || num > 180)) {
        value = input.value.slice(0, -1);
      }
    }

    input.value = value;
  }

  @HostListener('paste', ['$event'])
  onPaste(event: ClipboardEvent) {
    event.preventDefault();
    const pasted = (event.clipboardData?.getData('text') ?? '').trim();
    const num = parseFloat(pasted);
    if (!isNaN(num)) {
      if (this.type === 'lat' && num >= -90 && num <= 90) {
        this.el.nativeElement.value = pasted;
      } else if (this.type === 'lng' && num >= -180 && num <= 180) {
        this.el.nativeElement.value = pasted;
      }
    }
  }
}
