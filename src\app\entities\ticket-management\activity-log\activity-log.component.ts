import { DatePipe } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { FormControl, NgForm } from '@angular/forms';
import { NbTagComponent } from '@nebular/theme';
import { NbTagInputAddEvent } from '@nebular/theme/components/tag/tag-input.directive';
import moment from 'moment';
import * as _moment from 'moment/moment';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription, catchError, forkJoin, of } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { AppConstants } from '../../../@shared/constants';
import { AUTHORITY_ROLE_STRING, ROLE_TYPE } from '../../../@shared/enums';
import { Dropdown } from '../../../@shared/models/dropdown.model';
import { MessageVM } from '../../../@shared/models/messageVM.model';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import { UserService } from '../../user-management/user.service';
import {
  DeviceListForTicketActivity,
  FieldTechs,
  GetTruckRollDropDownParams,
  HoursList,
  ServicesRateDropDownList,
  TicketActivityDevice,
  TicketActivityFaultCodeDeviceMap,
  TicketCostType,
  TicketDeviceTypeList,
  TicketMaterials,
  TicketSiteDeviceOutage,
  TruckRollDropDown
} from '../ticket.model';
import { TicketService } from '../ticket.service';

@Component({
  selector: 'sfl-log-work',
  templateUrl: './activity-log.component.html',
  styleUrls: ['./activity-log.component.scss'],
  encapsulation: ViewEncapsulation?.None
})
export class ActivityLogComponent implements OnInit, OnDestroy {
  public onClose: Subject<boolean>;
  @Input() ticketId;
  @Input() openTicket;
  @Input() closeTicket;
  @Input() ticketStatus;
  @Input() siteId;
  @Input() activityId;
  @Input() activityLogItem;
  @Input() isEdit;
  @Input() ticketActivityLog;
  @Input() ticketModel;
  @Input() ticketNumber: string;
  loading = false;
  subscription: Subscription = new Subscription();
  moment = (_moment as any).default ? (_moment as any).default : _moment;
  public event: EventEmitter<any> = new EventEmitter();
  hoursList: Dropdown[] = HoursList;
  files: File[] = [];
  fieldTechList: Dropdown[] = [];
  minDate: Date;
  deletedFieldTech: FieldTechs[] = [];
  deletedMaterials: TicketMaterials[] = [];
  min: Date;
  jhaDetail: Dropdown[] = [];
  filteredJHAs: number[] = [];
  truckRollDropDownList: TruckRollDropDown[] = [];
  servicesDropDownList: ServicesRateDropDownList[] = [];
  costTypeList: TicketCostType[] = [];
  modalRef: BsModalRef;
  dropdownLoading = {};
  dateTimeFormat = AppConstants.dateTimeFormat;
  deviceListForTicketActivity: DeviceListForTicketActivity[] = [];
  deletedTicketSiteDeviceOutage: TicketSiteDeviceOutage[] = [];
  env = environment;
  deletedFaultCodeDeviceMaps: TicketActivityFaultCodeDeviceMap[] = [];
  filteredDeviceIds: number[] = [];
  faultCodeDeviceList: TicketActivityDevice[] = [];

  constructor(
    public _bsModalRef: BsModalRef,
    private readonly ticketService: TicketService,
    private readonly alertService: AlertService,
    private readonly userService: UserService,
    private readonly modalService: BsModalService,
    private readonly datePipe: DatePipe,
    private readonly storageService: StorageService
  ) {}

  public ngOnInit(): void {
    this.min = new Date(this.openTicket);
    this.getTruckRollBySite();
    this.getJhaList();
    this.getSiteDeviceList();
    this.getAllApiCall();

    if (!this.activityLogItem.id) {
      const todayDate = new Date();
      const activityLogItemDate = todayDate < new Date(this.openTicket) ? this.openTicket : todayDate;
      this.activityLogItem.date = new Date(activityLogItemDate);
      this.addFieldTech();
      this.addMaterials();
      this.addFaultCodesAndDevices();
    } else {
      // this.activityLogItem.ticketFieldTechDto[0].isServiceDeleted = true;
      // if the first required service is deleted then set the operationServiceId to null as we want to make the service field required in this case
      if (!this.activityLogItem.ticketFieldTechDto.length) {
        this.addFieldTech();
      } else {
        if (this.activityLogItem.ticketFieldTechDto[0].isServiceDeleted) {
          this.activityLogItem.ticketFieldTechDto[0].operationServiceId = null;
        }
      }
      if (!this.activityLogItem.ticketActivityMaterials.length) {
        this.addMaterials();
      }
      if (!this.activityLogItem.ticketActivityFaultCodeDeviceMaps.length) {
        this.addFaultCodesAndDevices();
      }
      this.activityLogItem.date = new Date(this.activityLogItem.date);
      this.activityLogItem.ticketActivityFaultCodeDeviceMaps.forEach(item => {
        if (!item.selectedDeviceIds) {
          item.selectedDeviceIds = item.faultCodeDevices ? item.faultCodeDevices.map(d => d.siteDeviceId) : [];
        }
      });
      if (this.activityLogItem.isADeviceOutage) {
        if (this.activityLogItem?.ticketSiteDeviceOutage && this.activityLogItem.ticketSiteDeviceOutage.length) {
          this.activityLogItem.ticketSiteDeviceOutage = this.activityLogItem.ticketSiteDeviceOutage.map(item => ({
            ...item,
            minStartDateTimeUTCDate: this.setMinStartDateTimeUTCDate(),
            maxStartDateTimeUTCDate: this.setMaxStartDateTimeUTCDate(),
            startDateTimeUTCDate: this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? ''),
            startDateTimeUTC: this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? '').toString(),
            minEndDateTimeUTCDate: this.setMinEndDateTimeUTCDate(item.startDateTimeUTC ?? ''),
            maxEndDateTimeUTCDate: this.setMaxEndDateTimeUTCDate(),
            endDateTimeUTCDate: item.endDateTimeUTC ? this.convertToLocalTime(item.endDateTimeUTC) : null
          }));
        } else {
          this.addDeviceOutage();
        }
      }
    }
    this.onClose = new Subject();
  }

  public onConfirm(): void {
    this.onClose.next(this.activityLogItem.truckRole);
    this._bsModalRef.hide();
  }

  public onChange(value) {
    this.ticketActivityLog.forEach(element => {
      if (element.isResolve === true && value === true) {
        this.alertService.showErrorToast('This ticket has already been marked as resolved, please review recent activity');
        setTimeout(() => {
          this.activityLogItem.isResolve = false;
        }, 0);
      }
    });
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  onActivityLogTicket() {
    if (this.activityLogItem.isADeviceOutage == null) {
      this.activityLogItem.isADeviceOutage = false;
    }
    this.activityLogItem.ticketActivityMaterials.forEach((element, index) => {
      if (element.costTypeID === null || element.costTypeID === 0) {
        this.activityLogItem.ticketActivityMaterials.splice(index, 1);
      } else {
        element.materialCost = Number(element.materialCost);
      }
    });
    this.activityLogItem.ticketFieldTechDto.forEach((element, index) => {
      if (element.userId === null) {
        this.activityLogItem.ticketFieldTechDto.splice(index, 1);
      } else {
        element.ticketActivityId = this.activityLogItem.id;
      }
    });
    this.activityLogItem.ticketSiteDeviceOutage.forEach((element, index) => {
      if (element.deviceId === null || element.deviceId === 0) {
        this.activityLogItem.ticketSiteDeviceOutage.splice(index, 1);
      }
      if (element.startDateTimeUTCDate) {
        element.startDateTimeUTC = this.getFormatedDateTime(element.startDateTimeUTCDate.toString());
      }
      if (element.endDateTimeUTCDate) {
        element.endDateTimeUTC = this.getFormatedDateTime(element.endDateTimeUTCDate.toString());
      }
    });

    this.activityLogItem.date = this.activityLogItem.date ? this.ticketService.formateDate(this.activityLogItem.date) : null;
    if (this.activityLogItem.id) {
      if (this.deletedFieldTech.length) {
        for (const i of this.deletedFieldTech) {
          this.activityLogItem.ticketFieldTechDto.push(i);
        }
      }
      if (this.deletedMaterials.length) {
        for (const i of this.deletedMaterials) {
          this.activityLogItem.ticketActivityMaterials.push(i);
        }
      }
      if (this.deletedTicketSiteDeviceOutage.length) {
        for (const i of this.deletedTicketSiteDeviceOutage) {
          this.activityLogItem.ticketSiteDeviceOutage.push(i);
        }
      }
      if (this.deletedFaultCodeDeviceMaps.length) {
        for (const i of this.deletedFaultCodeDeviceMaps) {
          this.activityLogItem.ticketActivityFaultCodeDeviceMaps.push(i);
        }
      }
      if (this.activityLogItem.truckRole && !this.activityLogItem.jhaMapPost.length) {
        this.addUpdateActivityLog(this.activityLogItem.id);
      } else {
        this.updateActivityLogItem();
      }
    } else {
      if (this.activityLogItem.truckRole && !this.activityLogItem.jhaMapPost.length) {
        this.addUpdateActivityLog(null);
      } else {
        this.createActivityLogItem();
      }
    }
  }

  deleteFieldTech(index: number) {
    const deletedItem = { ...this.activityLogItem.ticketFieldTechDto[index] };
    if (!this.activityLogItem.ticketFieldTechDto[index].id) {
      if (index === 0 && this.activityLogItem.ticketFieldTechDto.length === 1) {
        this.resetDeletedFieldsForZeroIndex(index);
        return;
      } else {
        this.activityLogItem.ticketFieldTechDto.splice(index, 1);
      }
    } else {
      deletedItem.isDeleted = true;
      this.deletedFieldTech.push(deletedItem);
      if (index === 0 && this.activityLogItem.ticketFieldTechDto.length === 1) {
        this.resetDeletedFieldsForZeroIndex(index);
        return;
      } else {
        this.activityLogItem.ticketFieldTechDto.splice(index, 1);
      }
    }
  }

  resetDeletedFieldsForZeroIndex(index: number) {
    this.activityLogItem.ticketFieldTechDto[index].operationServiceId = null;
    this.activityLogItem.ticketFieldTechDto[index].userId = '';
    this.activityLogItem.ticketFieldTechDto[index].hours = 0;
  }

  updateActivityLogItem() {
    this.loading = true;
    this.subscription.add(
      this.ticketService.editActivitylog(this.activityLogItem).subscribe({
        next: res => {
          this.onActivityAdded(res);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  createActivityLogItem() {
    this.loading = true;
    this.activityLogItem.ticketId = Number(this.ticketId);
    this.subscription.add(
      this.ticketService.createActivityLog(this.activityLogItem).subscribe({
        next: (res: MessageVM) => {
          this.onActivityAdded(res);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  addUpdateActivityLog(activityLogId) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'No JHA is selected. All on site activities require a JHA. Are you sure you would like to save?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(
      result => {
        if (result) {
          if (activityLogId) {
            this.updateActivityLogItem();
          } else {
            this.createActivityLogItem();
          }
        } else {
          this.loading = false;
        }
      },
      err => {
        this.loading = false;
      }
    );
  }

  onActivityAdded(activityResp) {
    if (this.files.length) {
      this.uploadFiles(activityResp);
    } else {
      this.alertService.showSuccessToast(activityResp.message);
      this.event.emit(this.activityLogItem);
      this.loading = false;
      this._bsModalRef.hide();
    }
  }

  getUpload(files) {
    for (const i of files) {
      this.files.push(i);
    }
  }

  uploadFiles(activityResp) {
    this.loading = true;
    const tempArray: any = [];
    for (const i of this.files) {
      const formData: FormData = new FormData();
      formData.append('file', i as File);
      formData.append('fileType', i.type.split('/')[0] && i.type.split('/')[0] === 'application' ? 'document' : i.type.split('/')[0]);
      formData.append('fileExtension', i.type.split('/')[1]);
      formData.append('id', '0');
      formData.append('ticketActivityId', `${activityResp.entryid}`);
      formData.append('ticketId', `${this.activityLogItem.ticketId}`);
      formData.append('parentId', `${this.activityLogItem.ticketId}`);
      formData.append('documentUrl', '');
      formData.append('isDeleted', 'false');
      tempArray.push(this.ticketService.uploadFiles(this.activityLogItem.ticketId, formData));
    }
    forkJoin(tempArray).subscribe({
      next: res => {
        this.files = [];
        this.alertService.showSuccessToast('Activity Created Successfully');
        this.event.emit(this.activityLogItem);
        this.loading = false;
        this._bsModalRef.hide();
      },
      error: e => {
        this.alertService.showWarningToast('Activity Log created but fail to upload files.');
        this.loading = false;
      }
    });
  }

  deleteFile(index: number) {
    this.files.splice(index, 1);
  }

  getAllApiCall(): void {
    this.loading = true;
    this.dropdownLoading['costType'] = true;
    const getDeviceListForTicketActivity = this.ticketService.getDeviceListForTicketActivity(this.siteId, this.ticketNumber).pipe(
      catchError(error => {
        return of([]);
      })
    );

    const getCostTypeList = this.ticketService.getCostTypeList().pipe(
      catchError(error => {
        return of([]);
      })
    );

    const userRoles = Object.entries(AUTHORITY_ROLE_STRING)
      .filter(
        ([roleType, authorityRoleString]) =>
          authorityRoleString !== AUTHORITY_ROLE_STRING[ROLE_TYPE.CUSTOMER] &&
          authorityRoleString !== AUTHORITY_ROLE_STRING[ROLE_TYPE.CONTRACTOR]
      )
      .map(([roleType, authorityRoleString]) => Number(roleType));
    const getFieldTechList = this.userService.getUserByRoles(userRoles).pipe(
      catchError(error => {
        return of([]);
      })
    );

    const getServiceRateTypeList = this.ticketService.getServiceDropDownList().pipe(
      catchError(error => {
        return of([]);
      })
    );

    this.subscription.add(
      forkJoin({
        deviceListForTicketActivity: getDeviceListForTicketActivity,
        costTypeList: getCostTypeList,
        fieldTechList: getFieldTechList,
        servicesDropDownList: getServiceRateTypeList
      }).subscribe({
        next: (responses: any) => {
          this.deviceListForTicketActivity =
            responses.deviceListForTicketActivity && responses.deviceListForTicketActivity.siteDeviceDropDownDto.length
              ? responses.deviceListForTicketActivity.siteDeviceDropDownDto.map(item => ({
                  ...item,
                  id: item.deviceOutageId ? item.deviceOutageId : item.id,
                  minStartDateTimeUTCDate: this.setMinStartDateTimeUTCDate(),
                  maxStartDateTimeUTCDate: this.setMaxStartDateTimeUTCDate(),
                  startDateTimeUTCDate: this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? ''),
                  startDateTimeUTC: this.setStartDateTimeUTCDate(item.startDateTimeUTC ?? '').toString(),
                  minEndDateTimeUTCDate: this.setMinEndDateTimeUTCDate(item.startDateTimeUTC ?? ''),
                  maxEndDateTimeUTCDate: this.setMaxEndDateTimeUTCDate(),
                  endDateTimeUTCDate: item.endDateTimeUTC ? this.convertToLocalTime(item.endDateTimeUTC) : null,
                  deviceName: item.refTicketNumber === this.ticketNumber ? `${item.deviceName} (Current Ticket)` : item.deviceName
                }))
              : [];
          this.costTypeList = responses.costTypeList;
          this.fieldTechList = responses.fieldTechList;
          this.servicesDropDownList = responses.servicesDropDownList;
          this.loading = false;
          this.dropdownLoading['costType'] = false;
        },
        error: e => {
          this.loading = false;
          this.dropdownLoading['costType'] = false;
        }
      })
    );
  }

  getFieldTechList() {
    this.dropdownLoading['fieldTech'] = true;
    const userRoles = Object.entries(AUTHORITY_ROLE_STRING)
      .filter(
        ([roleType, authorityRoleString]) =>
          authorityRoleString !== AUTHORITY_ROLE_STRING[ROLE_TYPE.CUSTOMER] &&
          authorityRoleString !== AUTHORITY_ROLE_STRING[ROLE_TYPE.CONTRACTOR]
      )
      .map(([roleType, authorityRoleString]) => Number(roleType));
    this.subscription.add(
      this.userService.getUserByRoles(userRoles).subscribe({
        next: res => {
          this.fieldTechList = res;
          this.loading = false;
          this.dropdownLoading['fieldTech'] = false;
        },
        error: e => {
          this.loading = false;
          this.dropdownLoading['fieldTech'] = false;
        }
      })
    );
  }

  getJhaList() {
    this.dropdownLoading['jha'] = true;
    const date = this.ticketService.formateDate(this.activityLogItem.date);
    this.subscription.add(
      this.ticketService.getJhaListForActivityLog(this.siteId, date).subscribe({
        next: res => {
          this.jhaDetail = res;
          this.loading = false;
          this.dropdownLoading['jha'] = false;
        },
        error: e => {
          this.loading = false;
          this.dropdownLoading['jha'] = false;
        }
      })
    );
  }

  getSiteDeviceList() {
    this.dropdownLoading['device'] = true;
    this.subscription.add(
      this.ticketService.getDeviceTypeBySiteId(this.ticketModel.siteId).subscribe({
        next: (res: TicketDeviceTypeList[]) => {
          this.faultCodeDeviceList = res.flatMap(deviceType =>
            deviceType.siteDevices.map(device => ({
              siteDeviceId: device.id,
              siteDeviceName: device.name,
              groupName: deviceType.name
            }))
          );
          this.loading = false;
          this.dropdownLoading['device'] = false;
        },
        error: e => {
          this.loading = false;
          this.dropdownLoading['device'] = false;
        }
      })
    );
  }

  addFieldTech() {
    const newData = new FieldTechs();
    this.activityLogItem.ticketFieldTechDto.push(newData);
  }

  addMaterials() {
    const newData = new TicketMaterials();
    this.activityLogItem.ticketActivityMaterials.push(newData);
  }

  addFaultCodesAndDevices(): void {
    const newData = new TicketActivityFaultCodeDeviceMap();
    this.activityLogItem.ticketActivityFaultCodeDeviceMaps.push(newData);
  }

  removeSelectedErrors(form: NgForm): void {
    Object.entries(form.form.controls).forEach(([key, control]) => {
      if (key.includes('endDateTime') || key.includes('startDateTime')) {
        const errors = (control as FormControl).errors;

        if (errors) {
          const filteredErrors = Object.fromEntries(
            Object.entries(errors).filter(([errKey]) => ['minDateTimeError', 'maxDateTimeError'].includes(errKey))
          );

          (control as FormControl).setErrors(Object.keys(filteredErrors).length > 0 ? filteredErrors : null);
        }
      }
    });
  }

  compareDates = (actualDate: string, minDate: string): boolean => {
    return new Date(actualDate) < new Date(minDate);
  };

  getFormatedDateTime(requestedDateTime: string): string {
    const dateTimeformat = 'MM/DD/YYYY hh:mm A';
    if (requestedDateTime) return moment(new Date(requestedDateTime)).format(dateTimeformat);
    return null;
  }

  convertToLocalTime(utcDateString: string | Date = ''): Date {
    return utcDateString !== '' ? new Date(utcDateString) : new Date();
  }

  setStartDateTimeUTCDate(utcDateString: string | Date = ''): Date {
    const openDate = this.convertToLocalTime(this.openTicket);
    openDate.setHours(0, 0, 0, 0);
    const startDate =
      this.convertToLocalTime(utcDateString) < this.convertToLocalTime(this.openTicket)
        ? this.convertToLocalTime(this.openTicket)
        : this.convertToLocalTime(utcDateString);
    return this.ticketStatus === 3 ? openDate : startDate;
  }

  setMinStartDateTimeUTCDate(): Date {
    const openDate = this.convertToLocalTime(this.openTicket);
    openDate.setHours(0, 0, 0, 0);
    return this.ticketStatus === 3 ? openDate : this.min;
  }

  setMaxStartDateTimeUTCDate(): Date {
    const closeDate = this.convertToLocalTime(this.closeTicket);
    closeDate.setHours(23, 58, 59);
    return this.ticketStatus === 3 ? closeDate : null;
  }

  setMinEndDateTimeUTCDate(startDateTimeUTCDate: string | Date = '', fromStartDate = false): Date {
    const minStartDate = !fromStartDate ? (this.ticketStatus === 3 ? this.openTicket : startDateTimeUTCDate) : startDateTimeUTCDate;
    const minDate = this.convertToLocalTime(minStartDate);
    minDate.setMinutes(minDate.getMinutes() + 1);
    return minDate;
  }

  setMaxEndDateTimeUTCDate(): Date {
    const closeDate = this.convertToLocalTime(this.closeTicket);
    closeDate.setHours(23, 59, 59);
    return this.ticketStatus === 3 ? closeDate : null;
  }

  nbMinEndDateTimeUTCDate(minEndDateTimeUTCDate: string | Date = ''): Date {
    const minDate = this.convertToLocalTime(minEndDateTimeUTCDate);
    minDate.setDate(minDate.getDate() - 1);
    return minDate;
  }

  nbMaxEndDateTimeUTCDate(maxEndDateTimeUTCDate: string | Date = ''): Date {
    const closeDate = this.convertToLocalTime(maxEndDateTimeUTCDate);
    closeDate.setHours(23, 59, 59);
    return this.ticketStatus === 3 ? closeDate : null;
  }

  addDeviceOutage(): void {
    const newData = new TicketSiteDeviceOutage();
    this.activityLogItem.ticketSiteDeviceOutage.push(newData);
  }

  onDeviceOutageChange(event: boolean): void {
    if (event) {
      if (this.activityLogItem.ticketSiteDeviceOutage.length) {
        this.activityLogItem.ticketSiteDeviceOutage.forEach((element, index) => {
          element.isDeleted = false;
        });
      } else {
        this.addDeviceOutage();
      }
    } else {
      if (this.activityLogItem.ticketSiteDeviceOutage.length) {
        this.activityLogItem.ticketSiteDeviceOutage.forEach((element, index) => {
          if (element.deviceId === null || element.deviceId === 0) {
            this.activityLogItem.ticketSiteDeviceOutage.splice(index, 1);
          }
          element.isDeleted = true;
        });
      }
    }
  }

  onDeviceChange(event: DeviceListForTicketActivity, item: TicketSiteDeviceOutage): void {
    if (event?.startDateTimeUTC && event?.refTicketNumber && event.refTicketNumber !== this.ticketNumber) {
      setTimeout(() => {
        item.deviceId = null;
      }, 0);
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: `Device Outage : <a href="${this.env.baseUrl}/entities/ticket/detail/view/${event.refTicketNumber}" target="_blank">${event.refTicketNumber}</a>`,
          showConfirmButton: false,
          cancelBtnText: 'Okay'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(
        result => {},
        err => {
          this.loading = false;
        }
      );
    } else {
      event.minStartDateTimeUTCDate = this.setMinStartDateTimeUTCDate();
      event.maxStartDateTimeUTCDate = this.setMaxStartDateTimeUTCDate();
      event.startDateTimeUTCDate = this.setStartDateTimeUTCDate(event.startDateTimeUTC ?? '');
      event.minEndDateTimeUTCDate = this.setMinEndDateTimeUTCDate(event.startDateTimeUTCDate ?? '');
      event.maxEndDateTimeUTCDate = this.setMaxEndDateTimeUTCDate();
      event.endDateTimeUTCDate = event.endDateTimeUTC ? this.convertToLocalTime(event.endDateTimeUTC) : null;
      Object.assign(item, event);
    }
  }

  onActivityStartDateChange(item: TicketSiteDeviceOutage): void {
    item.minEndDateTimeUTCDate = this.setMinEndDateTimeUTCDate(item.startDateTimeUTCDate, true);
    item.maxEndDateTimeUTCDate = this.setMaxEndDateTimeUTCDate();
    item.endDateTimeUTCDate = null;
  }

  deleteTicketSiteDeviceOutage(index: number) {
    if (this.activityLogItem.ticketSiteDeviceOutage[index].id) {
      this.activityLogItem.ticketSiteDeviceOutage[index].isDeleted = true;
      this.deletedTicketSiteDeviceOutage.push(this.activityLogItem.ticketSiteDeviceOutage[index]);
    }
    this.activityLogItem.ticketSiteDeviceOutage.splice(index, 1);
  }

  deleteMaterials(index: number) {
    if (this.activityLogItem.ticketActivityMaterials[index].id) {
      this.activityLogItem.ticketActivityMaterials[index].isDeleted = true;
      this.deletedMaterials.push(this.activityLogItem.ticketActivityMaterials[index]);
    }
    this.activityLogItem.ticketActivityMaterials.splice(index, 1);
  }

  deleteFaultCodesAndDevices(index: number): void {
    if (this.activityLogItem.ticketActivityFaultCodeDeviceMaps[index].id) {
      this.activityLogItem.ticketActivityFaultCodeDeviceMaps[index].isDeleted = true;
      this.deletedFaultCodeDeviceMaps.push(this.activityLogItem.ticketActivityFaultCodeDeviceMaps[index]);
    }
    this.activityLogItem.ticketActivityFaultCodeDeviceMaps.splice(index, 1);
  }

  selectAndDeselectAll(isSelect = false) {
    if (isSelect) {
      if (this.filteredJHAs.length) {
        this.activityLogItem.jhaMapPost = [
          ...new Set([...this.activityLogItem.jhaMapPost, ...JSON.parse(JSON.stringify(this.filteredJHAs))])
        ];
      } else {
        this.activityLogItem.jhaMapPost = this.jhaDetail.map(jha => jha.id);
      }
    } else {
      if (this.filteredJHAs.length) {
        this.activityLogItem.jhaMapPost = this.activityLogItem.jhaMapPost.filter(x => !this.filteredJHAs.includes(x));
      } else {
        this.activityLogItem.jhaMapPost = [];
      }
    }
  }

  onFilter(event: any) {
    if (event.term) {
      this.filteredJHAs = event.items?.map(element => element.id);
    } else {
      this.filteredJHAs = [];
    }
  }

  onSiteDeviceFilter(event: any): void {
    if (event.term) {
      this.filteredDeviceIds = event.items?.map(element => element.siteDeviceId);
    } else {
      this.filteredDeviceIds = [];
    }
  }

  onTruckRoleIdChange(truckRoleId) {
    if (truckRoleId === -1) {
      this.activityLogItem.truckRollId = null;
      this.activityLogItem.truckRollType = 2;
    } else if (truckRoleId === 0) {
      this.activityLogItem.truckRollId = null;
      this.activityLogItem.truckRollType = 1;
    }
  }

  onActivityDateChange() {
    this.getJhaList();
    this.activityLogItem.truckRollId = null;
    const objParams: GetTruckRollDropDownParams = {
      siteId: this.siteId,
      ticketActivityId: null,
      truckRollId: null,
      activityDate: this.ticketService.formateDate(this.activityLogItem.date)
    };
    this.getTruckRollBySite(objParams);
  }

  getTruckRollBySite(getTruckRollObj?: GetTruckRollDropDownParams) {
    this.loading = true;
    this.dropdownLoading['truckRoll'] = true;
    const objParams: GetTruckRollDropDownParams = {
      siteId: this.siteId,
      ticketActivityId: null,
      truckRollId: this.activityLogItem.truckRollId,
      activityDate: this.ticketService.formateDate(this.activityLogItem.date)
    };
    const getListParams = getTruckRollObj ? getTruckRollObj : objParams;
    this.subscription.add(
      this.ticketService.getTruckRollBySite(getListParams).subscribe({
        next: (res: TruckRollDropDown[]) => {
          const additionalItems = [
            {
              truckRollId: 0,
              truckRollNumber: 'Create TR',
              truckRollType: 1,
              truckRollTypeName: 'TR'
            },
            {
              truckRollId: -1,
              truckRollNumber: 'Create TR-PM',
              truckRollType: 2,
              truckRollTypeName: 'TR_PM'
            }
          ];
          this.truckRollDropDownList = [...additionalItems, ...res];
          this.loading = false;
          this.dropdownLoading['truckRoll'] = false;
        },
        error: e => {
          this.loading = false;
          this.dropdownLoading['truckRoll'] = false;
        }
      })
    );
  }

  getServiceRateTypeList() {
    this.loading = true;
    this.dropdownLoading['services'] = true;
    this.subscription.add(
      this.ticketService.getServiceDropDownList().subscribe({
        next: (res: ServicesRateDropDownList[]) => {
          this.servicesDropDownList = res;
          this.loading = false;
          this.dropdownLoading['services'] = false;
        },
        error: e => {
          this.loading = false;
          this.dropdownLoading['services'] = false;
        }
      })
    );
  }

  onTagAdd(tagValue: NbTagInputAddEvent, item: TicketActivityFaultCodeDeviceMap): void {
    if (tagValue.value && tagValue.value.trim()) {
      if (!item.faultCodes) {
        item.faultCodes = [];
      }
      item.faultCodes.push(tagValue.value.trim());
    }
    tagValue.input.nativeElement.value = '';
  }

  onTagRemove(event: NbTagComponent, item: TicketActivityFaultCodeDeviceMap): void {
    const index = item.faultCodes.indexOf(event.text);
    if (index > -1) {
      item.faultCodes.splice(index, 1);
    }
  }

  onDeviceSelectionChange(item: TicketActivityFaultCodeDeviceMap): void {
    item.faultCodeDevices = [];
    item.selectedDeviceIds?.forEach(deviceId => {
      const device = this.faultCodeDeviceList.find(d => d.siteDeviceId === deviceId);
      if (device) {
        const deviceObj = new TicketActivityDevice();
        deviceObj.siteDeviceId = device.siteDeviceId;
        deviceObj.siteDeviceName = device.siteDeviceName;
        item.faultCodeDevices.push(deviceObj);
      }
    });
  }

  selectAndDeselectAllSiteDevices(isSelect: boolean, item: TicketActivityFaultCodeDeviceMap): void {
    if (isSelect) {
      if (this.filteredDeviceIds.length) {
        this.filteredDeviceIds.forEach(deviceId => {
          const device = this.faultCodeDeviceList.find(d => d.siteDeviceId === deviceId);
          if (device && !item.faultCodeDevices.some(d => d.siteDeviceId === deviceId)) {
            const deviceObj = new TicketActivityDevice();
            deviceObj.siteDeviceId = device.siteDeviceId;
            deviceObj.siteDeviceName = device.siteDeviceName;
            item.faultCodeDevices.push(deviceObj);
            if (!item.selectedDeviceIds.includes(deviceId)) {
              item.selectedDeviceIds.push(deviceId);
            }
          }
        });
      } else {
        item.faultCodeDevices = [];
        item.selectedDeviceIds = [];
        this.faultCodeDeviceList.forEach(device => {
          const deviceObj = new TicketActivityDevice();
          deviceObj.siteDeviceId = device.siteDeviceId;
          deviceObj.siteDeviceName = device.siteDeviceName;
          item.faultCodeDevices.push(deviceObj);
          item.selectedDeviceIds.push(device.siteDeviceId);
        });
      }
    } else {
      if (this.filteredDeviceIds.length) {
        item.faultCodeDevices = item.faultCodeDevices.filter(device => !this.filteredDeviceIds.includes(device.siteDeviceId));
        item.selectedDeviceIds = item.selectedDeviceIds.filter(id => !this.filteredDeviceIds.includes(id));
      } else {
        item.faultCodeDevices = [];
        item.selectedDeviceIds = [];
      }
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
