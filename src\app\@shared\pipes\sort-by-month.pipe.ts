import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'sortByMonth'
})
export class SortByMonthPipe implements PipeTransform {
  transform(array: any, month: string, day?: string): any[] {
    let sortedArray = array?.filter((element: any) => {
      const date =
        element.status === 1 || element.status === 2
          ? element.datePerformed
          : element.status === 4 && element.tentativeMonth && !(element?.rescheduleDate ?? element?.dateScheduled)
          ? Date.parse(`${element.tentativeMonth} 1, ${new Date().getFullYear()}`)
          : element.rescheduleDate ?? element.dateScheduled;

      return date && new Date(date).toLocaleString('en-US', { month: 'long' }) === month;
    });

    if (day) {
      sortedArray = sortedArray.filter(element => {
        const flag = element.status === 1 || element.status === 2 ? element.datePerformed : element.rescheduleDate ?? element.dateScheduled;

        return (
          flag && new Date(Date.parse(`${month} 1, ${new Date().getFullYear()}`)).getMonth() + 1 + '/' + new Date(flag).getDate() === day
        );
      });
    }

    return sortedArray || [];
  }
}
