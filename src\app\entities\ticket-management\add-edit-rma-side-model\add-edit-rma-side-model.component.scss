.modal-dialog-right {
  position: fixed;
  margin: auto;
  width: 600px;
  right: 0px;
  height: 100%;
}

@media (max-width: 700px) {
  .modal-dialog-right {
    width: 80%;
  }
}

.modal-content {
  height: 100%;
}

.rma-detail-modal-body {
  max-height: calc(100vh - 150px);
  overflow-y: auto;
  overflow-x: hidden;
}

.rma-detail-modal-body::-webkit-scrollbar {
  background: #101426;
  cursor: pointer;
  border-radius: 0.15625rem;
  width: 0.3125rem;
  height: 0.3125rem;
}

.border-top {
  border-top: 1px solid #151a30 !important;
}

.border-left {
  border-left: 1px solid #151a30 !important;
}

.dropZone {
  height: 100% !important;
  padding: 0.5rem;
  em {
    font-size: 20px !important;
  }
  h5 {
    font-size: 16px !important;
    margin-bottom: 0px !important;
  }
  label {
    font-size: 13px !important;
    margin-bottom: 0px !important;
  }
}
input:disabled {
  cursor: not-allowed !important;
}

.disable-field {
  opacity: 0.5 !important;
}

.nb-theme-dark [nbInput].mfg-name:disabled {
  background-color: #192038;
  border-color: #101426;
  color: #ffff !important;
}
.image-container {
  img {
    max-width: -webkit-fill-available;
  }

  .imageFilename {
    max-width: 70% !important;
    word-wrap: break-word;
    min-width: 70% !important;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
