<nb-card class="siteSpinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Notifications</h6>
        <div class="ms-auto">
          <button
            class="linear-mode-button ms-3"
            nbButton
            status="primary"
            size="small"
            type="button"
            [disabled]="loading"
            (click)="openAddEditNotification()"
          >
            <span class="d-none d-lg-inline-block"> Add Notifications </span>
            <i class="d-inline-block d-lg-none fa-solid fa-plus"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <div class="row">
      <div class="col-12 siteFilter appFilter">
        <sfl-filter
          [filterDetails]="filterDetails"
          (refreshList)="refreshList($event)"
          (refreshTableHeight)="this.isFilterDisplay = $event"
        ></sfl-filter>
      </div>
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="col-12 table-responsive mt-3 table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Site List">
          <thead>
            <tr>
              <th class="Portfolio" (click)="sort('customerPortfolio', sortOptionList['customerPortfolio'])" id="Portfolio">
                <div class="d-flex align-items-center">
                  Customer (Portfolio)
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['customerPortfolio'] === 'desc',
                      'fa-arrow-down': sortOptionList['customerPortfolio'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'customerPortfolio'
                    }"
                  ></span>
                </div>
              </th>
              <th class="Site" (click)="sort('Site', sortOptionList['Site'])" id="Site">
                <div class="d-flex align-items-center">
                  Site
                  <span
                    class="fa cursor-pointer ms-auto"
                    [ngClass]="{
                      'fa-arrow-up': sortOptionList['Site'] === 'desc',
                      'fa-arrow-down': sortOptionList['Site'] === 'asc',
                      'icon-selected': filterModel.sortBy === 'Site'
                    }"
                  ></span>
                </div>
              </th>
              <th class="notification-type" id="notification-type">
                <div class="d-flex align-items-center">Notification Type</div>
              </th>
              <th class="photos" id="photos">
                <div class="d-flex align-items-center">Photos</div>
              </th>
              <th class="text-center action" id="action">Action</th>
            </tr>
          </thead>

          <tbody>
            <tr
              *ngFor="
                let notification of notificationList
                  | paginate
                    : {
                        itemsPerPage: filterModel.itemsCount,
                        currentPage: currentPage,
                        totalItems: total
                      };
                let i = index
              "
            >
              <td data-title="Customer (Portfolio)" class="td-custom-width">
                {{ notification?.customerName || 'Customer Name' }} ({{ notification?.portfolioNameStr || 'Portfolio' }})
              </td>
              <td data-title="Site" class="text-truncate td-custom-width text-break">
                <span>{{ notification?.siteNameStr || 'Site Name' }}</span>
              </td>
              <td class="td-custom-width">
                <span *ngIf="notification.isActive; else InActiveMissingConfig">
                  {{ notification?.notificationTypeNameStr }}
                </span>
                <ng-template #InActiveMissingConfig>
                  <span [ngClass]="notification?.isInactive ? 'text-danger' : ''">
                    {{ notification?.isInactive ? 'Inactive' : 'Missing Configuration' }}
                  </span>
                </ng-template>
              </td>
              <td class="td-custom-width">
                {{ notification?.isIncludePhotos ? 'Yes' : 'No' }}
              </td>
              <td data-title="Action" class="text-center">
                <a class="listgrid-icon px-2">
                  <em
                    class="fa fa-edit"
                    nbTooltip="Edit"
                    (click)="openAddEditNotification(notification.id)"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                  ></em>
                </a>
              </td>
            </tr>
            <tr>
              <td colspan="8" *ngIf="!notificationList?.length" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2 d-md-flex align-items-center" *ngIf="notificationList?.length">
        <div class="d-flex align-items-center">
          <label class="mb-0">Items per page: </label>
          <ng-select class="ms-2" [(ngModel)]="pageSize" [clearable]="false" [searchable]="false" (change)="onChangeSize()" appendTo="body">
            <ng-option value="5">5</ng-option>
            <ng-option value="10">10</ng-option>
            <ng-option value="50">50</ng-option>
            <ng-option value="100">100</ng-option>
          </ng-select>
        </div>
        <strong class="ms-md-3">Total Records: {{ total }}</strong>
        <div class="ms-md-auto ms-sm-0">
          <pagination-controls (pageChange)="onPageChange($event)" class="paginate"></pagination-controls>
        </div>
      </div>
    </div>
  </nb-card-body>
</nb-card>
