import { StorageService } from '../services/storage.service';
import { ROLE_TYPE } from '../enums';
import { AppConstants } from '../constants';
import { UserAuthorisation } from '../models/user.model';

const storageService: StorageService = new StorageService();
let userAuthorisations: UserAuthorisation;

const hasValidAuthData = (): boolean => {
  return (
    Array.isArray(userAuthorisations?.authorities) &&
    Array.isArray(userAuthorisations?.authorisationIds) &&
    userAuthorisations.authorities.length > 0 &&
    userAuthorisations.authorisationIds.length > 0 &&
    userAuthorisations.authorities.length === userAuthorisations.authorisationIds.length
  );
};

const setUserAuthorisations = (): void => {
  if (!hasValidAuthData()) {
    const currentUser = storageService.get(AppConstants.userKey);
    userAuthorisations = storageService.get(AppConstants.userAuthorisations) ?? new UserAuthorisation(currentUser?.authorities ?? []);
  }
};

export const checkAuthorisations = (allowedRoles: ROLE_TYPE[]): boolean => {
  setUserAuthorisations();
  const { authorisationIds } = userAuthorisations;
  if (!authorisationIds?.length) return false;
  if (authorisationIds.length === 1) return allowedRoles.includes(authorisationIds[0]);
  const authSet = new Set(authorisationIds);
  return allowedRoles.some(role => authSet.has(role));
};
