<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="d-flex align-items-center">
      <h6>{{ getComponentTitle() }}</h6>
      <div class="d-flex ms-auto">
        <button nbButton status="basic" type="button" (click)="goBack()" size="small" class="float-end">
          <span class="d-none d-lg-inline-block">Back</span>
          <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
        </button>
        <button
          *ngIf="isEdit || (isDetail && !checkAuthorisationsFn([roleType.CUSTOMER]))"
          (click)="gotoDevice(site?.customerId, site?.portfolioId, site?.id)"
          nbButton
          status="primary"
          size="small"
          type="button"
          id="siteSubmit"
          class="float-end ms-2"
        >
          <span class="d-none d-lg-inline-block">Manage Devices {{ '(' + (site?.siteDeviceCount || '-') + ')' }}</span>
          <i class="d-inline-block d-lg-none fa-solid fa-list-check"></i>
        </button>
        <button
          *ngIf="isDetail && !checkAuthorisationsFn([roleType.CUSTOMER]) && !site.isArchive"
          (click)="isEdit = true; isDetail = false"
          nbButton
          status="primary"
          size="small"
          type="button"
          id="siteSubmit"
          class="float-end ms-2"
        >
          <span class="d-none d-lg-inline-block">Edit</span>
          <i class="d-inline-block d-lg-none fa-solid fa-pen"></i>
        </button>
        <button
          *ngIf="isCreate || isEdit"
          nbButton
          (click)="siteForm.onSubmit()"
          status="primary"
          size="small"
          type="button"
          id="siteSubmit"
          class="float-end ms-2"
        >
          <span class="d-none d-lg-inline-block">Save</span>
          <i class="d-inline-block d-lg-none fa-solid fa-save"></i>
        </button>
        <button
          nbButton
          status="danger"
          size="small"
          type="button"
          id="siteSubmit"
          class="float-end ms-2"
          *ngIf="checkAuthorisationsFn([roleType.ADMIN, roleType.DIRECTOR]) && !isCreate"
          (click)="onDeleteSite(site.id)"
        >
          <span class="d-none d-lg-inline-block">Delete</span>
          <i class="d-inline-block d-lg-none fa-solid fa-trash"></i>
        </button>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <form name="siteForm" #siteForm="ngForm" aria-labelledby="title" autocomplete="off" (ngSubmit)="createSite(siteForm)">
      <div class="form-group row">
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="input-Customer">Customer<span class="ms-1 text-danger">*</span></label>
          <span *ngIf="isDetail && !checkAuthorisationsFn([roleType.CUSTOMER])">
            <a [routerLink]="['/entities/customers/detail/' + siteDetail?.customerId]">{{ siteDetail.customerName }}</a>
          </span>
          <span *ngIf="isDetail && checkAuthorisationsFn([roleType.CUSTOMER])">
            {{ siteDetail.customerName }}
          </span>
          <div *ngIf="isCreate || isEdit">
            <ng-select
              name="Customer"
              bindLabel="name"
              bindValue="id"
              #customer="ngModel"
              (change)="onCustomerSelect(true); site.portfolioId = null; isVGTScopeAvailableForSite()"
              [(ngModel)]="site.customerId"
              [items]="customerData"
              notFoundText="No Customer Found"
              placeholder="Select Customer"
              [clearable]="false"
              required
              appendTo="body"
            >
            </ng-select>
            <sfl-error-msg [control]="customer" [isFormSubmitted]="siteForm?.submitted" fieldName="Customer"></sfl-error-msg>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="input-Portfolio">Portfolio<span class="ms-1 text-danger">*</span></label>
          <span *ngIf="isDetail && !checkAuthorisationsFn([roleType.CUSTOMER])">
            <a [routerLink]="['/entities/portfolios/detail/' + siteDetail?.portfolioId]"> {{ siteDetail.portfolioName }}</a>
          </span>
          <span *ngIf="isDetail && checkAuthorisationsFn([roleType.CUSTOMER])">
            {{ siteDetail.portfolioName }}
          </span>
          <div *ngIf="isCreate || isEdit">
            <ng-select
              name="Portfolio"
              bindLabel="name"
              bindValue="id"
              #portfolio="ngModel"
              [(ngModel)]="site.portfolioId"
              (change)="onPortfolioSelect($event)"
              [items]="portData"
              notFoundText="No Portfolio Found"
              placeholder="Select Portfolio"
              [clearable]="false"
              required
              appendTo="body"
            >
            </ng-select>
            <sfl-error-msg [control]="portfolio" [isFormSubmitted]="siteForm?.submitted" fieldName="Portfolio"></sfl-error-msg>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="input-name"> Site Name <span class="ms-1 text-danger">*</span></label>
          <span *ngIf="isDetail">{{ siteDetail.siteName || '-' }}</span>
          <div *ngIf="isCreate || isEdit">
            <input
              nbInput
              fullWidth
              name="name"
              id="input-name"
              [(ngModel)]="site.siteName"
              #siteName="ngModel"
              class="form-control"
              required
            />
            <sfl-error-msg [control]="siteName" [isFormSubmitted]="siteForm?.submitted" fieldName="Site"></sfl-error-msg>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="input-name"> Site ID </label>
          <span *ngIf="isDetail">{{ siteDetail.qeSiteId || '-' }}</span>
          <div *ngIf="isCreate || isEdit">
            <input
              nbInput
              fullWidth
              name="site-number"
              id="input-site-number"
              [(ngModel)]="site.qeSiteId"
              #qeSiteId="ngModel"
              class="form-control"
            />
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2 qe-service-types">
          <label class="label" for="input-qe-service-types"> Service Type<span class="ms-1 text-danger">*</span></label>
          <span *ngIf="isDetail">{{ siteDetail.qeServiceTypeStr || '-' }}</span>
          <div *ngIf="isCreate || isEdit">
            <ng-select
              id="input-qe-service-types"
              name="input-qe-service-types"
              class="input-qe-service-types"
              #qeServiceTypes="ngModel"
              [items]="qeServiceTypeDropDownOption"
              [(ngModel)]="site.qeServiceTypes"
              (ngModelChange)="handleCategoryTypeListSelection($event)"
              [multiple]="true"
              bindLabel="name"
              bindValue="id"
              groupBy="groupName"
              [selectableGroup]="false"
              [selectableGroupAsModel]="false"
              [closeOnSelect]="false"
              (remove)="handleCategoryTypeListSelectionRemove($event)"
              appendTo="body"
              notFoundText="No Service Types Found"
              placeholder="Select Service Type"
              appQEServiceTypeValidator
            >
              <ng-template ng-multi-label-tmp let-items="items" let-item$="item$" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.groupName }} - {{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
              <ng-template ng-optgroup-tmp let-item="item">
                <div class="">
                  <span class="ng-optgroup-label"
                    >{{ item.groupName }}<span class="ms-1 text-danger" *ngIf="isRequiredQEServiceTypeGroup(item.groupName)">*</span></span
                  >
                </div>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div [ngClass]="{ 'pe-none opacity-75': item.disabled }">
                  <input
                    id="item-{{ index }}"
                    name="item-{{ index }}"
                    type="checkbox"
                    [ngModel]="item$.selected"
                    [disabled]="item.disabled"
                    [ngModelOptions]="{ standalone: true }"
                  />
                  {{ item.name }}
                </div>
              </ng-template>
            </ng-select>
            <sfl-error-msg [control]="qeServiceTypes" [isFormSubmitted]="siteForm?.submitted" fieldName="Service Type"></sfl-error-msg>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="input-name"> NTP Date </label>
          <span *ngIf="isDetail" class="text-center">
            <ng-container *ngIf="site.contractStartDate; else noNTPDate">
              {{ site.contractStartDate | date : 'MM/dd/yyyy' }}
            </ng-container>
            <ng-template #noNTPDate> - </ng-template>
          </span>
          <div *ngIf="isCreate || isEdit">
            <input
              class="form-control search-textbox"
              [nbDatepicker]="contractStartDate"
              [(ngModel)]="site.contractStartDate"
              name="contractStartDate"
              placeholder="Select"
              id="input-openDate"
              readonly
              autocomplete="off"
            />
            <nb-datepicker #contractStartDate></nb-datepicker>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
          <label class="label" for="input-name"> Contract End Date </label>
          <span *ngIf="isDetail" class="text-center">
            <ng-container *ngIf="selectedContract?.endDate; else noContractEndDate">
              {{ selectedContract?.endDate | date : 'MM/dd/yyyy' }}
            </ng-container>
            <ng-template #noContractEndDate> <span class="text-center"> -</span> </ng-template>
          </span>
          <ng-container *ngIf="selectedContract && selectedContract?.endDate && (isCreate || isEdit)">
            <span>{{ selectedContract?.endDate | date : 'MM/dd/yyyy' }}</span>
          </ng-container>
        </div>
        <div class="col-12 col-sm-6 col-lg-3 mb-2">
          <label class="label" for="contract-drop-down">Site Contract</label>
          <span *ngIf="isDetail">{{ site.contractName || '-' }}</span>
          <div *ngIf="isCreate || isEdit">
            <ng-select
              id="contract-drop-down"
              class="sfl-track-dropdown"
              name="sitecontract"
              bindValue="id"
              bindLabel="name"
              [items]="customerContracts"
              [(ngModel)]="site.contractId"
              notFoundText="No Contract Found"
              placeholder="Select a Contract"
              [disabled]="!checkAuthorisationsFn([roleType.ADMIN, roleType.DIRECTOR, roleType.MANAGER, roleType.SUPPORT])"
              appendTo="body"
              (change)="onContractChange($event)"
            >
            </ng-select>
          </div>
        </div>
        <div class="col-12 col-sm-1 col-lg-auto mb-2">
          <label class="label" for="tickets">NERC</label>
          <div class="d-flex">
            <nb-toggle
              status="primary"
              [disabled]="checkAuthorisationsFn([roleType.CUSTOMER]) || isDetail"
              [(checked)]="site.isNERC"
              (checkedChange)="site.isNERC = $event"
            ></nb-toggle>
          </div>
          <div class="nerc-site-type-dd" *ngIf="site.isNERC">
            <label class="label" for="nercSiteTypeId"> NERC Site Type <span class="ms-1 text-danger">*</span></label>
            <span *ngIf="isDetail">{{ site.nercSiteTypeStr || '-' }}</span>
            <div *ngIf="isCreate || isEdit">
              <ng-select
                name="nercSiteTypeId"
                #nercSiteTypeId="ngModel"
                bindLabel="name"
                bindValue="id"
                id="nercSiteTypeId"
                [items]="nercDropDownOption"
                [(ngModel)]="site.nercSiteTypeId"
                [closeOnSelect]="true"
                notFoundText="No NERC Site Type Found"
                placeholder="Select a NERC Site Type"
                appendTo="body"
                [required]="site.isNERC"
              >
              </ng-select>
              <sfl-error-msg [control]="nercSiteTypeId" [isFormSubmitted]="siteForm?.submitted" fieldName="NERC Site Type"></sfl-error-msg>
            </div>
          </div>
        </div>
        <div class="col-12 col-sm-1 col-lg-auto mb-2" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && !isCreate">
          <label class="label" for="customer"> Archive </label>
          <div class="d-flex">
            <nb-toggle
              (checkedChange)="(!!site.isArchive)"
              status="primary"
              [disabled]="isDetail"
              [(checked)]="site.isArchive"
              class="toogleStyle"
            ></nb-toggle>
          </div>
        </div>
        <div class="col-12 col-sm-1 col-lg-auto mb-2" *ngIf="site.isArchive === false && !isCreate">
          <label class="label" for="tickets">Active</label>
          <div class="d-flex">
            <nb-toggle
              [(checked)]="site.isActive"
              [disabled]="checkAuthorisationsFn([roleType.CUSTOMER]) || isDetail || !site.isParentActive"
              (checkedChange)="activeToggleChange(site.isActive)"
              status="primary"
            ></nb-toggle>
          </div>
        </div>
        <div class="col-12 col-sm-1 col-lg-1 mb-2" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && !isCreate">
          <label class="label" for="tickets">GADS Reporting</label>
          <div class="d-flex">
            <nb-toggle [(checked)]="site.isGADSReporting" [disabled]="isDetail" status="primary"></nb-toggle>
          </div>
        </div>
        <div class="col-12 col-sm-1 col-lg-auto mb-2" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
          <label class="label" for="customer"> Check-In Notifications </label>
          <div class="d-flex">
            <nb-toggle
              (checkedChange)="(!!site?.isCheckInOutNoticeEnabled)"
              status="primary"
              [disabled]="isDetail"
              [(checked)]="site.isCheckInOutNoticeEnabled"
              class="toogleStyle"
            ></nb-toggle>
          </div>
        </div>
        <div class="col-12 col-sm-1 col-lg-1 mb-2" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && !isCreate">
          <label class="label" for="tickets">Contracted Hours</label>
          <div class="d-flex">
            <nb-toggle [(checked)]="site.isBankofHours" [disabled]="isDetail" status="primary"></nb-toggle>
          </div>
        </div>
      </div>
      <nb-tabset *ngIf="activeTab" fullWidth class="row" #siteAddEditTabSet (changeTab)="changeTab($event)">
        <nb-tab
          [tabTitle]="siteAddEditScreenTabsName[siteAddEditScreenTabsEnum.SITE_INFO]"
          [badgeText]="getErrorNumber(siteForm, siteAddEditScreenTabsEnum.SITE_INFO)"
          [active]="getActiveTab(siteAddEditScreenTabsName[siteAddEditScreenTabsEnum.SITE_INFO])"
          badgeStatus="danger"
          [id]="siteAddEditScreenTabsEnum.SITE_INFO"
        >
          <nb-accordion class="mb-2">
            <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head">Location</nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="row" [ngClass]="{ 'justify-content-center': site.siteInfoImageURL }">
                  <div class="col-12 col-xl-3 col-md-8 site-photo-container" *ngIf="site.siteInfoImageURL">
                    <div class="photo-frame">
                      <img [src]="site.siteInfoImageURL" alt="Location Icon" class="location-icon" />
                    </div>
                  </div>
                  <div class="col-12 col-xl-9 mt-3 mt-xl-0">
                    <div class="row">
                      <div class="col-12 col-sm-4 mb-3">
                        <div class="form-control-group">
                          <label class="label" for="input-address">Address<span class="ml-1 text-danger">*</span></label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLocation?.address || '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              #address="ngModel"
                              [(ngModel)]="site.siteLocation.address"
                              name="address"
                              pattern=".*\S.*"
                              id="input-address"
                              class="form-control"
                              spellcheck="true"
                              contenteditable="true"
                              required
                            />
                            <sfl-error-msg [control]="address" [isFormSubmitted]="siteForm?.submitted" fieldName="Address"></sfl-error-msg>
                          </div>
                        </div>
                      </div>
                      <div class="col-12 col-sm-4 mb-3">
                        <div class="form-control-group">
                          <label class="label" for="input-city">City<span class="ml-1 text-danger">*</span></label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLocation?.city || '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="site?.siteLocation.city"
                              #City="ngModel"
                              name="city"
                              pattern=".*\S.*"
                              id="input-city"
                              class="form-control"
                              spellcheck="true"
                              contenteditable="true"
                              required
                            />
                            <sfl-error-msg [control]="City" [isFormSubmitted]="siteForm?.submitted" fieldName="City"></sfl-error-msg>
                          </div>
                        </div>
                      </div>
                      <div class="col-sm-4">
                        <label class="label" for="input-State">State<span class="ml-1 text-danger">*</span></label>
                        <span *ngIf="isDetail">{{ siteDetail?.siteLocation?.state || '-' }}</span>
                        <div *ngIf="isCreate || isEdit">
                          <ng-select
                            fullWidth
                            id="State"
                            name="State"
                            #State="ngModel"
                            required
                            [(ngModel)]="site.siteLocation.state"
                            size="large"
                            [clearable]="false"
                            appendTo="body"
                          >
                            <ng-option *ngFor="let state of states" [value]="state.name">
                              {{ state?.name }}
                            </ng-option>
                          </ng-select>
                          <sfl-error-msg [control]="State" [isFormSubmitted]="siteForm?.submitted" fieldName="State"></sfl-error-msg>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-12 col-sm-4 mb-3">
                        <div class="form-control-group">
                          <label class="label" for="input-zipCode">Zip Code<span class="ml-1 text-danger">*</span></label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLocation?.zipCode || '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="site.siteLocation.zipCode"
                              #zipCode="ngModel"
                              sflNumbersOnly
                              name="zipCode"
                              id="input-zipCode"
                              class="form-control"
                              [maxlength]="6"
                              [minlength]="3"
                              required
                            />
                            <sfl-error-msg [control]="zipCode" [isFormSubmitted]="siteForm?.submitted" fieldName="ZipCode"></sfl-error-msg>
                          </div>
                        </div>
                      </div>
                      <div class="col-12 col-sm-4 mb-3">
                        <div class="form-control-group">
                          <label class="label" for="input-latitude">Latitude<span class="ml-1 text-danger">*</span></label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLocation?.latitude || '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="site.siteLocation.latitude"
                              #latitude="ngModel"
                              sflValidators
                              name="latitude"
                              id="input-latitude"
                              class="form-control"
                              required
                              [appLatitudeLongitude]="'lat'"
                              (ngModelChange)="
                                getSiteTimeZoneBaseOnLongitudeLatitude(site.siteLocation.logitude, site.siteLocation.latitude)
                              "
                            />
                            <sfl-error-msg
                              [control]="latitude"
                              [isFormSubmitted]="siteForm?.submitted"
                              fieldName="Latitude"
                            ></sfl-error-msg>
                          </div>
                        </div>
                      </div>
                      <div class="col-12 col-sm-4 mb-3">
                        <div class="form-control-group">
                          <label class="label" for="input-logitude">Longitude<span class="ml-1 text-danger">*</span></label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLocation?.logitude || '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="site.siteLocation.logitude"
                              sflValidators
                              #longitude="ngModel"
                              name="logitude"
                              id="input-logitude"
                              class="form-control"
                              required
                              [appLatitudeLongitude]="'lng'"
                              (ngModelChange)="
                                getSiteTimeZoneBaseOnLongitudeLatitude(site.siteLocation.logitude, site.siteLocation.latitude)
                              "
                            />
                            <sfl-error-msg
                              [control]="longitude"
                              [isFormSubmitted]="siteForm?.submitted"
                              fieldName="Longitude"
                            ></sfl-error-msg>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-12 col-sm-4 mb-3">
                        <div class="form-control-group email">
                          <label class="label" for="input-googleMapLink">Google Map Link</label>
                          <span *ngIf="isDetail">
                            <span *ngIf="siteDetail?.siteLocation?.googleMapLink?.match(regex)">
                              <a
                                class="ellipsis"
                                [nbTooltip]="siteDetail?.siteLocation?.googleMapLink"
                                href="{{ siteDetail?.siteLocation?.googleMapLink }}"
                                target="_blank"
                                >{{ siteDetail?.siteLocation?.googleMapLink }}</a
                              ></span
                            >
                            <span
                              class="ellipsis"
                              [nbTooltip]="siteDetail?.siteLocation?.googleMapLink"
                              *ngIf="!siteDetail?.siteLocation?.googleMapLink?.match(regex)"
                            >
                              {{ siteDetail?.siteLocation?.googleMapLink }}
                            </span>
                            <span *ngIf="!siteDetail?.siteLocation?.googleMapLink">
                              {{ '-' }}
                            </span>
                          </span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="site.siteLocation.googleMapLink"
                              #googleMapLink="ngModel"
                              name="googleMapLink"
                              id="input-googleMapLink"
                              class="form-control"
                            />
                          </div>
                        </div>
                      </div>
                      <div class="col-12 col-sm-4 mb-3">
                        <div class="form-control-group">
                          <label class="label" for="input-lockBoxCode">Lock Box Code</label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLocation?.lockBoxCode || '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="site.siteLocation.lockBoxCode"
                              #lockBoxCode="ngModel"
                              name="lockBoxCode"
                              id="input-lockBoxCode"
                              class="form-control"
                            />
                          </div>
                        </div>
                      </div>
                      <div class="col-12 col-sm-4 mb-3">
                        <div class="form-control-group">
                          <label class="label" for="input-timezone">Site TimeZone<span class="ml-1 text-danger">*</span></label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteTimeZoneOffset || '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <ng-select
                              name="siteTimeZoneOffset"
                              [items]="timeZoneList"
                              bindLabel="displayName"
                              bindValue="id"
                              #siteTimeZoneOffset="ngModel"
                              [(ngModel)]="site.siteTimeZoneOffset"
                              notFoundText="No TimeZone Found"
                              placeholder="Select TimeZone"
                              appendTo="body"
                              [clearable]="false"
                              id="input-timezone"
                              required
                            >
                            </ng-select>
                            <sfl-error-msg
                              [control]="siteTimeZoneOffset"
                              [isFormSubmitted]="siteForm?.submitted"
                              fieldName="Site TimeZone"
                            ></sfl-error-msg>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-12 col-sm-4 mb-3">
                        <div class="form-control-group">
                          <label class="label" for="countyName">County</label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLocation?.countyName || '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              id="countyName"
                              name="countyName"
                              class="form-control"
                              [(ngModel)]="site.siteLocation.countyName"
                              nbInput
                              readonly
                              fullWidth
                            />
                          </div>
                        </div>
                      </div>
                      <div class="col-12 col-sm-4 mb-3">
                        <div class="form-control-group">
                          <label class="label" for="countyFIPSCode">County FIPS</label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLocation?.countyFIPSCode || '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              id="countyFIPSCode"
                              name="countyFIPSCode"
                              class="form-control"
                              [(ngModel)]="site.siteLocation.countyFIPSCode"
                              nbInput
                              readonly
                              fullWidth
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion class="mb-2">
            <nb-accordion-item [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head">Site Access</nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="row">
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-emergencyPhone">Emergency Services Phone</label>
                      <span *ngIf="isDetail" class="white-space-manual">{{ siteDetail?.siteAccess?.emergencyPhone || '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <textarea
                          nbInput
                          fullWidth
                          #emrPhone="ngModel"
                          [(ngModel)]="site.siteAccess.emergencyPhone"
                          name="emrPhone"
                          id="input-emergencyPhone"
                          class="form-control"
                          spellcheck="true"
                          contenteditable="true"
                          type="text"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="tickets">Lift Required ?</label>
                      <div class="d-flex">
                        <nb-toggle
                          [(checked)]="site.siteAccess.liftRequired"
                          status="primary"
                          (change)="onChangeLiftRequired($event)"
                          [disabled]="isDetail"
                        ></nb-toggle>
                        <label class="lbl-margin-left" *ngIf="!site.siteAccess.liftRequired">No</label>
                        <label class="lbl-margin-left" *ngIf="site.siteAccess.liftRequired">Yes</label>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="tickets">Ladder Required ?</label>

                      <div class="d-flex">
                        <nb-toggle
                          [(checked)]="site.siteAccess.ladderRequired"
                          status="primary"
                          (change)="onChangeLadderRequired($event)"
                          [disabled]="isDetail"
                        ></nb-toggle>
                        <label class="lbl-margin-left" *ngIf="!site.siteAccess.ladderRequired">No</label>
                        <label class="lbl-margin-left" *ngIf="site.siteAccess.ladderRequired">Yes</label>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="tickets">Notice Required ?</label>
                      <div class="d-flex">
                        <nb-toggle
                          [(checked)]="site.siteAccess.noticeRequired"
                          status="primary"
                          (change)="onChangeNoticeRequired($event)"
                          [disabled]="isDetail"
                        ></nb-toggle>
                        <label class="lbl-margin-left" *ngIf="!site.siteAccess.noticeRequired">No</label>
                        <label class="lbl-margin-left" *ngIf="site.siteAccess.noticeRequired">Yes</label>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-liftDetails">Lift Details</label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteAccess?.liftDetails || '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <input
                          nbInput
                          fullWidth
                          #liftDetails="ngModel"
                          [(ngModel)]="site.siteAccess.liftDetails"
                          name="liftDetails"
                          pattern=".*\S.*"
                          id="input-liftDetails"
                          class="form-control"
                          spellcheck="true"
                          contenteditable="true"
                          [disabled]="!site.siteAccess.liftRequired"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-ladderDetails">Ladder Details<span class="ms-1 text-danger">*</span></label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteAccess?.ladderDetails || '-' }}</span>
                      <nb-radio-group
                        class="d-flex"
                        [disabled]="!site.siteAccess.ladderRequired || isDetail"
                        [(ngModel)]="site.siteAccess.ladderDetails"
                        name="ladderDetails"
                        #ladderDetails="ngModel"
                        id="input-ladderDetails"
                        [required]="site.siteAccess.ladderRequired ? true : false"
                      >
                        <nb-radio *ngFor="let item of ladderDetailesArray" [value]="item.value">{{ item.name }}</nb-radio>
                      </nb-radio-group>
                      <sfl-error-msg
                        [control]="ladderDetails"
                        [isFormSubmitted]="siteForm?.submitted"
                        fieldName="Ladder Details"
                      ></sfl-error-msg>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-noticeDetails">Notice Details<span class="ms-1 text-danger">*</span></label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteAccess?.noticeDetails || '-' }}</span>
                      <nb-radio-group
                        class="d-flex"
                        [disabled]="!site.siteAccess.noticeRequired || isDetail"
                        [(ngModel)]="site.siteAccess.noticeDetails"
                        name="noticeDetails"
                        #noticeDetails="ngModel"
                        id="input-noticeDetails"
                        [required]="site.siteAccess.noticeRequired ? true : false"
                      >
                        <nb-radio *ngFor="let item of noticeDetailsArray" [value]="item.value">{{ item.name }}</nb-radio>
                      </nb-radio-group>
                      <sfl-error-msg
                        [control]="noticeDetails"
                        [isFormSubmitted]="siteForm?.submitted"
                        fieldName="Notice Details"
                      ></sfl-error-msg>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="tickets">Weekend Work Allowed</label>
                      <div class="d-flex">
                        <nb-toggle [(checked)]="site.siteAccess.weekendWorkAllowed" status="primary" [disabled]="isDetail"></nb-toggle>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="tickets">PM Requires 2 Technician</label>
                      <div class="d-flex">
                        <nb-toggle
                          [(checked)]="site.siteAccess.pmRequiresTwoTechnicians"
                          status="primary"
                          [disabled]="isDetail"
                        ></nb-toggle>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-12 col-lg-12 mb-12">
                  <div class="form-control-group">
                    <label class="label" for="input-siteNotes">Site Notes</label>
                    <span *ngIf="isDetail">{{ siteDetail?.siteAccess?.siteNotes || '-' }}</span>
                    <div *ngIf="isCreate || isEdit">
                      <textarea
                        nbInput
                        fullWidth
                        #siteNotes="ngModel"
                        [(ngModel)]="site.siteAccess.siteNotes"
                        name="siteNotes"
                        id="input-siteNotes"
                        class="form-control"
                        spellcheck="true"
                        contenteditable="true"
                      >
                      </textarea>
                    </div>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion class="mb-2">
            <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head"> Site Contact Detail </nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="col-12">
                  <div class="custom-x-scroll mb-2">
                    <div style="min-width: 650px">
                      <div class="contact-header row mb-2">
                        <div class="col-1 pe-0 text-center"><label class="label">Use In JHA</label></div>
                        <div class="col-1 pe-0 ps-lg-0 text-center">
                          <label class="label"
                            >Check-In Notifications <span class="ms-1 text-danger" *ngIf="site.isCheckInOutNoticeEnabled">*</span>
                          </label>
                        </div>
                        <div class="col-1 pe-0 text-center"><label class="label">Use In Ticket</label></div>
                        <div class="col-1 pe-0 text-center"><label class="label">Utility Contact</label></div>
                        <div class="col-1 pe-0 text-center"><label class="label">PM Reporting</label></div>
                        <div class="col-1">
                          <label class="label">Contact Name<span class="ms-1 text-danger">*</span></label>
                        </div>
                        <div class="col-2">
                          <label class="label">Email</label>
                        </div>
                        <div class="col-2">
                          <label class="label">Phone</label>
                        </div>
                        <div class="col-1">
                          <label class="label">Notes</label>
                        </div>
                        <div
                          *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER, roleType.ANALYST, roleType.PORTFOLIOMANAGER])"
                          class="col-1 text-center"
                        >
                          <label class="label">Action</label>
                        </div>
                      </div>
                      <div class="contact-section row mb-2" *ngFor="let items of site.customerContactEmails; let i = index">
                        <div class="col-1 pe-0 d-flex justify-content-center align-items-center">
                          <nb-checkbox
                            name="{{ 'useAsJHAContact-' + i }}"
                            (change)="selectDeselectContact(items.useAsJHAContact, i)"
                            [checked]="items.useAsJHAContact"
                            [(ngModel)]="items.useAsJHAContact"
                            [disabled]="isDetail"
                          ></nb-checkbox>
                        </div>
                        <div class="col-1 pe-0 d-flex justify-content-center align-items-center">
                          <nb-checkbox
                            name="{{ 'useAsCheckInOutAlert-' + i }}"
                            [checked]="items.isCheckInOutSendEmailOn"
                            [(ngModel)]="items.isCheckInOutSendEmailOn"
                            [disabled]="isDetail || !site.isCheckInOutNoticeEnabled"
                          ></nb-checkbox>
                        </div>
                        <div class="col-1 pe-0 d-flex justify-content-center align-items-center">
                          <nb-checkbox
                            name="{{ 'useAsTicketContact-' + i }}"
                            (change)="selectDeselectTicketContact(items, i, items.useAsTicketContact)"
                            [checked]="items.useAsTicketContact"
                            [(ngModel)]="items.useAsTicketContact"
                            [disabled]="isDetail"
                          ></nb-checkbox>
                        </div>
                        <div class="col-1 pe-0 d-flex justify-content-center align-items-center">
                          <nb-checkbox
                            name="{{ 'utilityContact-' + i }}"
                            [checked]="items.utilityContact"
                            [(ngModel)]="items.utilityContact"
                            [disabled]="isDetail"
                          ></nb-checkbox>
                        </div>
                        <div class="col-1 pe-0 d-flex justify-content-center align-items-center">
                          <nb-checkbox
                            name="{{ 'sendEmailOnPMComplete-' + i }}"
                            [checked]="items.sendEmailOnPMComplete"
                            [(ngModel)]="items.sendEmailOnPMComplete"
                            [disabled]="isDetail"
                          ></nb-checkbox>
                        </div>
                        <div class="col-1 pe-0 d-flex">
                          <span *ngIf="isDetail">{{ items?.contactName || '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="items.contactName"
                              #contactName="ngModel"
                              pattern=".*\S.*"
                              name="{{ 'customerName-' + i }}"
                              id="input-customerName"
                              class="form-control"
                              spellcheck="true"
                              contenteditable="true"
                              required
                              [disabled]="items.portfolioCustomerContactId"
                            />
                            <sfl-error-msg
                              [control]="contactName"
                              [isFormSubmitted]="siteForm?.submitted"
                              fieldName="Contact Name"
                            ></sfl-error-msg>
                          </div>
                        </div>
                        <div class="col-2 pe-0 d-flex">
                          <div class="text-break" *ngIf="isDetail">{{ items?.email || '-' }}</div>
                          <div *ngIf="isCreate || isEdit" class="w-100">
                            <input
                              nbInput
                              type="email"
                              fullWidth
                              [(ngModel)]="items.email"
                              #email="ngModel"
                              name="{{ 'email-' + i }}"
                              id="input-email"
                              class="form-control"
                              appEmailValidate
                              [required]="items.useAsJHAContact || items.useAsTicketContact"
                              [disabled]="items.portfolioCustomerContactId"
                            />
                            <sfl-error-msg [control]="email" [isFormSubmitted]="true" fieldName="Email"></sfl-error-msg>
                          </div>
                        </div>
                        <div class="col-2 pe-0 d-flex">
                          <span *ngIf="isDetail">{{ items?.phoneNumber || '-' }}</span>
                          <div *ngIf="isCreate || isEdit" class="w-100">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="items.phoneNumber"
                              #phone="ngModel"
                              name="{{ 'phone-' + i }}"
                              id="input-phone"
                              class="form-control"
                              [mask]="contactNoFormat"
                              [disabled]="items.portfolioCustomerContactId"
                            />
                            <sfl-error-msg [control]="phone" [isFormSubmitted]="siteForm?.submitted" fieldName="Phone"></sfl-error-msg>
                          </div>
                        </div>
                        <div class="col-1 d-flex">
                          <span *ngIf="isDetail">{{ items?.customerTitle || '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="items.customerTitle"
                              #customerTitle="ngModel"
                              pattern=".*\S.*"
                              name="{{ 'customerTitle-' + i }}"
                              id="input-customerTitle"
                              class="form-control"
                              spellcheck="true"
                              contenteditable="true"
                              [disabled]="items.portfolioCustomerContactId"
                            />
                          </div>
                        </div>
                        <div
                          class="col-1 d-flex justify-content-center align-items-center"
                          *ngIf="
                            !items.portfolioCustomerContactId &&
                            !checkAuthorisationsFn([roleType.CUSTOMER, roleType.ANALYST, roleType.PORTFOLIOMANAGER])
                          "
                        >
                          <em class="fa fa-trash text-danger px-2 pointerReportLink" (click)="deleteContact(items.id, i)"></em>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && !isDetail">
                    <span class="text-primary pointerReportLink" (click)="addContact()">+ Add Contact</span>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion class="mb-2">
            <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head"> Site Layout Details </nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="row">
                  <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-workorderId">Array Type<span class="ms-1 text-danger">*</span></label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.siteTypeStr || '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <ng-select
                          name="siteTypes"
                          #siteTypes="ngModel"
                          [multiple]="true"
                          bindLabel="name"
                          bindValue="id"
                          [items]="arrayTypeList"
                          [(ngModel)]="site.siteLayoutDetail.siteType"
                          [closeOnSelect]="false"
                          notFoundText="No Array Type Found"
                          placeholder="Select Array Type"
                          appendTo="body"
                          (search)="onDropdownSearchFilter($event, 'filteredSiteTypeIds')"
                          (close)="filteredSiteTypeIds = []"
                          required
                        >
                          <ng-template ng-header-tmp>
                            <button
                              type="button"
                              (click)="selectAndDeselectAll(true, 'filteredSiteTypeIds')"
                              class="btn btn-sm btn-primary"
                            >
                              Select all
                            </button>
                            <button
                              type="button"
                              (click)="selectAndDeselectAll(false, 'filteredSiteTypeIds')"
                              class="btn btn-sm btn-primary ms-1"
                            >
                              Unselect all
                            </button>
                          </ng-template>
                          <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
                            {{ item.name }}
                          </ng-template>
                          <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                            <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                              <span class="ng-value-label">{{ item.name }}</span>
                              <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label">+{{ items.length - 2 }} </span>
                            </div>
                          </ng-template>
                        </ng-select>
                        <sfl-error-msg [control]="siteTypes" [isFormSubmitted]="siteForm?.submitted" fieldName="Array Type"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-dcSize">DC Size (kW)<span class="ms-1 text-danger">*</span></label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.dcSize ?? '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="site.siteLayoutDetail.dcSize"
                          #dcSize="ngModel"
                          sflValidators
                          name="dcSize"
                          id="input-dcSize"
                          class="form-control"
                          required
                        />
                        <sfl-error-msg [control]="dcSize" [isFormSubmitted]="siteForm?.submitted" fieldName="Dc Size"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <div class="row">
                      <div class="mb-2" [ngClass]="site.isNERC ? 'col-6' : 'col-12'">
                        <div class="form-control-group">
                          <label class="label" for="input-acSize">AC Size (kW)<span class="ms-1 text-danger">*</span></label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.acSize ?? '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="site.siteLayoutDetail.acSize"
                              sflValidators
                              #acSize="ngModel"
                              name="acSize"
                              id="input-acSize"
                              class="form-control"
                              required
                            />
                            <sfl-error-msg [control]="acSize" [isFormSubmitted]="siteForm?.submitted" fieldName="Ac Size"></sfl-error-msg>
                          </div>
                        </div>
                      </div>
                      <div class="col-6 mb-2" *ngIf="site.isNERC">
                        <div class="form-control-group">
                          <label class="label" for="input-poIkVs">POI KV<span *ngIf="site.isNERC" class="ms-1 text-danger">*</span></label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.poIkV ?? '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="site.siteLayoutDetail.poIkV"
                              sflValidators
                              #poIkV="ngModel"
                              name="poIkVs"
                              id="input-poIkVs"
                              class="form-control"
                              maxlength="10"
                              [required]="site.isNERC"
                            />
                            <sfl-error-msg [control]="poIkV" [isFormSubmitted]="siteForm?.submitted" fieldName="POI KV"></sfl-error-msg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-6 col-lg-3 mb-2 d-flex flex-column">
                    <label class="label fs-13">Utility Information</label>
                    <div class="form-control-group">
                      <label class="label" for="input-dcSize">Utility Number</label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.utilityNumber || '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="site.siteLayoutDetail.utilityNumber"
                          #utilityNumber="ngModel"
                          name="utilityNumber"
                          id="input-utilityNumber"
                          class="form-control"
                          maxlength="120"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-dcSize">Site Az (180 South)</label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.siteAz || '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="site.siteLayoutDetail.siteAz"
                          #SiteAz="ngModel"
                          name="SiteAz"
                          id="input-SiteAz"
                          class="form-control"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-dcSize">Tilt</label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.tilt || '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="site.siteLayoutDetail.tilt"
                          #tilt="ngModel"
                          name="tilt"
                          id="input-tilt"
                          class="form-control"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-inverterType">Inverter Type<span class="ms-1 text-danger">*</span></label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.inverterTypeStr }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <ng-select
                          fullWidth
                          bindLabel="name"
                          bindValue="id"
                          id="inverterType"
                          name="inverterType"
                          [multiple]="true"
                          required
                          [items]="inverterTypes"
                          [(ngModel)]="site.siteLayoutDetail.inverterTypes"
                          #inverterType="ngModel"
                          size="large"
                          [clearable]="false"
                          (search)="onDropdownSearchFilter($event, 'filteredInverterTypeIds')"
                          (close)="filteredInverterTypeIds = []"
                        >
                          <ng-template ng-header-tmp>
                            <button
                              type="button"
                              (click)="selectAndDeselectAll(true, 'filteredInverterTypeIds')"
                              class="btn btn-sm btn-primary"
                            >
                              Select all
                            </button>
                            <button
                              type="button"
                              (click)="selectAndDeselectAll(false, 'filteredInverterTypeIds')"
                              class="btn btn-sm btn-primary ms-1"
                            >
                              Unselect all
                            </button>
                          </ng-template>
                          <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <input id="item-{{ index }}" name="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" />
                            {{ item.name }}
                          </ng-template>
                          <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                            <div class="ng-value" *ngFor="let item of items">
                              <span class="ng-value-label">{{ item.name }}</span>
                              <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                            </div>
                          </ng-template>
                        </ng-select>
                        <sfl-error-msg
                          [control]="inverterType"
                          [isFormSubmitted]="siteForm?.submitted"
                          fieldName="Inverter Type"
                        ></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-6 col-lg-3 mb-2 d-flex flex-column">
                    <div class="form-control-group">
                      <label class="label" for="input-dcSize">Pole Number</label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.poleNumber || '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="site.siteLayoutDetail.poleNumber"
                          #poleNumber="ngModel"
                          name="poleNumber"
                          id="input-poleNumber"
                          class="form-control"
                          maxlength="120"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-inv"># of Inverters<span class="ms-1 text-danger">*</span></label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.inv ?? '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="site.siteLayoutDetail.inv"
                          sflValidators
                          #inv="ngModel"
                          name="inv"
                          id="input-inv"
                          class="form-control"
                          required
                        />
                        <sfl-error-msg [control]="inv" [isFormSubmitted]="siteForm?.submitted" fieldName="No Of Inverters"></sfl-error-msg>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-numOfModules"># of Modules</label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.numberofModules ?? '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="site.siteLayoutDetail.numberofModules"
                          #numOfModules="ngModel"
                          name="numOfModules"
                          id="input-numOfModules"
                          class="form-control"
                          sflNumbersOnly
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-dcSize"># of Combiners</label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.numberofCombiners || '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="site.siteLayoutDetail.numberofCombiners"
                          #numberofcombiners="ngModel"
                          name="numberofcombiners"
                          id="input-numberofcombiners"
                          class="form-control"
                          sflNumbersOnly
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-6 col-lg-3 mb-2 d-flex flex-column">
                    <div class="form-control-group">
                      <label class="label" for="input-dcSize">Circuit Number</label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.circuitNumber || '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="site.siteLayoutDetail.circuitNumber"
                          #circuitNumber="ngModel"
                          name="circuitNumber"
                          id="input-circuitNumber"
                          class="form-control"
                          maxlength="120"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-3 mb-2">
                    <div class="form-control-group">
                      <label class="label" for="input-numofPanelboards"># of Panelboard</label>
                      <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.numberofPanelboards ?? '-' }}</span>
                      <div *ngIf="isCreate || isEdit">
                        <input
                          nbInput
                          fullWidth
                          [(ngModel)]="site.siteLayoutDetail.numberofPanelboards"
                          #numofPanelboards="ngModel"
                          name="numofPanelboards"
                          id="input-numofPanelboards"
                          class="form-control"
                          sflNumbersOnly
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="row">
                      <div class="col-12 col-md-6 mb-2">
                        <div class="form-control-group">
                          <label class="label" for="input-xfmr"># of Oil Filled Transformers</label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.xfmr ?? '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="site.siteLayoutDetail.xfmr"
                              sflValidators
                              name="xfmr"
                              id="input-xfmr"
                              class="form-control"
                              (input)="onInputChange($event, 'oilFilled')"
                            />
                          </div>
                        </div>
                      </div>
                      <div class="col-12 col-md-6 d-flex flex-column">
                        <label class="label">Transformer Ownership</label>
                        <nb-checkbox
                          status="basic"
                          class="small-checkbox"
                          name="oilFilledUtility"
                          id="oilFilledUtility"
                          [(checked)]="checkboxStates.oilFilled.utility"
                          [ngModelOptions]="{ standalone: true }"
                          (change)="onCheckboxClick(checkboxStates.oilFilled.utility, 'oilFilled', 'customer')"
                          [disabled]="isDetail || isCheckboxDisable(site?.siteLayoutDetail?.xfmr)"
                        >
                          Utility Owned</nb-checkbox
                        >
                        <nb-checkbox
                          status="basic"
                          class="small-checkbox"
                          name="oilFilledCustomer"
                          id="oilFilledCustomer"
                          [(checked)]="checkboxStates.oilFilled.customer"
                          [ngModelOptions]="{ standalone: true }"
                          (change)="onCheckboxClick(checkboxStates.oilFilled.customer, 'oilFilled', 'utility')"
                          [disabled]="isDetail || isCheckboxDisable(site?.siteLayoutDetail?.xfmr)"
                        >
                          Customer Owned</nb-checkbox
                        >
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="row">
                      <div class="col-12 col-md-6 mb-2">
                        <div class="form-control-group">
                          <label class="label" for="input-dryXFMR"># of Dry Type Transformers</label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.dryXFMR ?? '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="site.siteLayoutDetail.dryXFMR"
                              sflValidators
                              name="dryXFMR"
                              id="input-dryXFMR"
                              class="form-control"
                              (input)="onInputChange($event, 'dry')"
                            />
                          </div>
                        </div>
                      </div>
                      <div class="col-12 col-md-6 d-flex flex-column">
                        <label class="label">Transformer Ownership</label>
                        <nb-checkbox
                          status="basic"
                          class="small-checkbox"
                          name="dryUtility"
                          id="dryUtility"
                          [(checked)]="checkboxStates.dry.utility"
                          (change)="onCheckboxClick(checkboxStates.dry.utility, 'dry', 'customer')"
                          [ngModelOptions]="{ standalone: true }"
                          [disabled]="isDetail || isCheckboxDisable(site?.siteLayoutDetail?.dryXFMR)"
                        >
                          Utility Owned</nb-checkbox
                        >
                        <nb-checkbox
                          status="basic"
                          class="small-checkbox"
                          name="dryCustomer"
                          id="dryCustomer"
                          [(checked)]="checkboxStates.dry.customer"
                          [ngModelOptions]="{ standalone: true }"
                          (change)="onCheckboxClick(checkboxStates.dry.customer, 'dry', 'utility')"
                          [disabled]="isDetail || isCheckboxDisable(site?.siteLayoutDetail?.dryXFMR)"
                        >
                          Customer Owned</nb-checkbox
                        >
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <div class="row">
                      <div class="col-12 col-md-6 mb-2">
                        <div class="form-control-group">
                          <label class="label" for="input-bessxfmr"># of BESS Transformers</label>
                          <span *ngIf="isDetail">{{ siteDetail?.siteLayoutDetail?.bessxfmr ?? '-' }}</span>
                          <div *ngIf="isCreate || isEdit">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="site.siteLayoutDetail.bessxfmr"
                              sflValidators
                              name="bessxfmr"
                              id="input-bessxfmr"
                              class="form-control"
                              (input)="onInputChange($event, 'bess')"
                            />
                          </div>
                        </div>
                      </div>
                      <div class="col-12 col-md-6 d-flex flex-column">
                        <label class="label">Transformer Ownership</label>
                        <nb-checkbox
                          status="basic"
                          class="small-checkbox"
                          name="utility"
                          id="vehicle2"
                          [(checked)]="checkboxStates.bess.utility"
                          [ngModelOptions]="{ standalone: true }"
                          (change)="onCheckboxClick(checkboxStates.bess.utility, 'bess', 'customer')"
                          [disabled]="isDetail || isCheckboxDisable(site?.siteLayoutDetail?.bessxfmr)"
                        >
                          Utility Owned</nb-checkbox
                        >
                        <nb-checkbox
                          status="basic"
                          class="small-checkbox"
                          name="utility"
                          id="vehicle2"
                          [(checked)]="checkboxStates.bess.customer"
                          [ngModelOptions]="{ standalone: true }"
                          (change)="onCheckboxClick(checkboxStates.bess.customer, 'bess', 'utility')"
                          [disabled]="isDetail || isCheckboxDisable(site?.siteLayoutDetail?.bessxfmr)"
                        >
                          Customer Owned</nb-checkbox
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion class="mb-2">
            <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head">Site Layout Images </nb-accordion-item-header>
              <nb-accordion-item-body>
                <div *ngIf="!isDetail" class="row mb-3">
                  <div class="col-12">
                    <div class="dropZone" ngFileDragDr (fileDropped)="getUploadedFiles($event, siteImageTypes.SITE_LAYOUT_IMAGE)">
                      <input
                        type="file"
                        #file
                        accept="image/*"
                        multiple
                        (change)="getUploadedFiles($event?.target?.files, siteImageTypes.SITE_LAYOUT_IMAGE, file)"
                      />
                      <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em>
                      <h5 class="fw-bold">Drop Image To Attach</h5>
                      <label style="text-transform: none" class="fw-bold">OR Click to Browse </label>
                    </div>
                    <ul *ngIf="siteLayoutImg.length">
                      <li *ngFor="let item of siteLayoutImg; let i = index">
                        <span>{{ item.name }}</span>
                        <em
                          (click)="deleteFile(i, siteImageTypes.SITE_LAYOUT_IMAGE)"
                          *ngIf="!checkAuthorisationsFn([roleType.ANALYST, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER])"
                          nbtooltip="Delete"
                          nbtooltipplacement="top"
                          nbtooltipstatus="text-danger"
                          aria-hidden="true"
                          class="fa fa-times-circle text-danger ms-2 pointer"
                        ></em>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="row">
                  <div
                    *ngFor="let item of site.siteImages | orderBy : 'order'; let i = index"
                    class="col-12 col-sm-4 col-md-3 col-lg-2 mb-2"
                  >
                    <div class="text-center">
                      <img
                        [src]="item?.imageURL"
                        alt="Non Conformance Image"
                        class="text-center img-thumbnail img-responsive siteImage cursor-pointer position-relative"
                        onError="this.src='assets/images/no-image-found.jpg'"
                        (click)="imagePopup(i, siteImageTypes.SITE_LAYOUT_IMAGE)"
                        [id]="'img' + i"
                      />
                      <a
                        *ngIf="
                          !checkAuthorisationsFn([
                            roleType.ANALYST,
                            roleType.MANAGER,
                            roleType.SUPPORT,
                            roleType.PORTFOLIOMANAGER,
                            roleType.CUSTOMER
                          ])
                        "
                        (click)="deleteSiteImages(item?.siteImageId)"
                        [id]="'deleteIcon' + i"
                      >
                        <em
                          class="fa-solid fa-circle-xmark text-danger img-delete-icon"
                          nbTooltip="Delete"
                          nbTooltipPlacement="top"
                          nbTooltipStatus="danger"
                          aria-hidden="true"
                        ></em>
                      </a>
                    </div>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion class="mb-2">
            <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head">Site Zones </nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="row">
                  <div class="col-lg-6 my-1">
                    <div class="p-3 accordion_head mb-3">
                      <span>Site Zone Map</span>
                    </div>
                    <div *ngIf="!isDetail" class="row mb-3">
                      <div class="col-12">
                        <div
                          *ngIf="!isDetail && ((site.id && site?.siteZoneImages?.length === 0) || (!site.id && zoneMapImg?.length === 0))"
                          class="dropZone"
                          ngFileDragDr
                          (fileDropped)="getUploadedFiles($event, siteImageTypes.ZONE_MAP_IMAGE)"
                        >
                          <input
                            type="file"
                            #file
                            accept="image/*"
                            (change)="getUploadedFiles($event?.target?.files, siteImageTypes.ZONE_MAP_IMAGE, file)"
                          />
                          <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em>
                          <h5 class="fw-bold">Drop Image To Attach</h5>
                          <label style="text-transform: none" class="fw-bold">OR Click to Browse </label>
                        </div>
                        <ul *ngIf="zoneMapImg.length">
                          <li *ngFor="let item of zoneMapImg; let i = index">
                            <span>{{ item.name }}</span>
                            <em
                              (click)="deleteFile(i, siteImageTypes.ZONE_MAP_IMAGE)"
                              *ngIf="
                                !checkAuthorisationsFn([roleType.ANALYST, roleType.MANAGER, roleType.SUPPORT, roleType.PORTFOLIOMANAGER])
                              "
                              nbtooltip="Delete"
                              nbtooltipplacement="top"
                              nbtooltipstatus="text-danger"
                              aria-hidden="true"
                              class="fa fa-times-circle text-danger ms-2 pointer"
                            ></em>
                          </li>
                        </ul>
                      </div>
                    </div>
                    <div class="row">
                      <div
                        *ngFor="let item of site.siteZoneImages | orderBy : 'order'; let i = index"
                        class="col-12 col-sm-6 col-md-3 col-lg-4 col-xxl-3 mb-2"
                      >
                        <div class="text-center">
                          <img
                            [src]="item?.imageURL"
                            alt="Non Conformance Image"
                            class="text-center img-thumbnail img-responsive siteImage cursor-pointer position-relative"
                            onError="this.src='assets/images/no-image-found.jpg'"
                            (click)="imagePopup(i, siteImageTypes.ZONE_MAP_IMAGE)"
                            [id]="'img' + i"
                          />
                          <a
                            *ngIf="
                              !checkAuthorisationsFn([
                                roleType.ANALYST,
                                roleType.MANAGER,
                                roleType.SUPPORT,
                                roleType.PORTFOLIOMANAGER,
                                roleType.CUSTOMER
                              ])
                            "
                            (click)="deleteSiteImages(item?.siteImageId)"
                            [id]="'deleteIcon' + i"
                          >
                            <em
                              class="fa-solid fa-circle-xmark text-danger img-delete-icon"
                              nbTooltip="Delete"
                              nbTooltipPlacement="top"
                              nbTooltipStatus="danger"
                              aria-hidden="true"
                            ></em>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6 my-1">
                    <div class="p-3 accordion_head mb-3">
                      <div class="d-flex justify-content-between align-items-center">
                        <span>Zones & Work Orders</span>
                        <em
                          *ngIf="!isDetail && site.zones && site.zones.length !== 0"
                          class="fa fa-plus text-primary cursor-pointer"
                          (click)="addRemoveZoneItems(null, siteForm)"
                          nbTooltip="Add Zone"
                          nbTooltipPlacement="top"
                          aria-hidden="true"
                        ></em>
                      </div>
                    </div>
                    <div class="row zones-workorder">
                      <div *ngIf="!site.id">
                        <p>Once the site is created, the zones and its associated workorder details will be shown here.</p>
                      </div>
                      <ng-container *ngFor="let zoneItem of site.zones; let i = index">
                        <ng-container *ngIf="!zoneItem.isDeleted">
                          <div class="col-12 col-sm-6 col-md-4 col-lg-6 col-xxl-4 px-2" [ngModelGroup]="i">
                            <div class="d-flex justify-content-center align-items-center">
                              <nb-checkbox
                                name="isSelected"
                                id="isSelected"
                                class="m-1"
                                [(ngModel)]="zoneItem.isSelected"
                                [disabled]="isDetail"
                              ></nb-checkbox>
                              <div class="zone-work-order-input-section m-1">
                                <input
                                  nbInput
                                  fullWidth
                                  fieldSize="small"
                                  name="zoneName"
                                  id="zoneName"
                                  #zoneName="ngModel"
                                  [(ngModel)]="zoneItem.zoneName"
                                  [status]="zoneItem.hasError ? 'danger' : 'basic'"
                                  required
                                  (input)="zoneNameErrorCheck(zoneName, i)"
                                  (blur)="zoneNameErrorCheck(zoneName, i)"
                                  [ngClass]="{ 'input-error': zoneItem.hasError }"
                                  class="form-control zone-work-order-input"
                                  [disabled]="isDetail"
                                />
                              </div>
                              <div class="zone-work-order-input-section m-1">
                                <input
                                  nbInput
                                  fullWidth
                                  fieldSize="small"
                                  [(ngModel)]="zoneItem.zoneWOName"
                                  name="zoneWOName"
                                  id="zoneWOName"
                                  class="form-control zone-work-order-input"
                                  [disabled]="true"
                                />
                              </div>
                              <div class="m-1" *ngIf="!isDetail">
                                <div class="d-flex align-items-center justify-content-center">
                                  <em
                                    *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                    class="fa fa-trash text-danger cursor-pointer"
                                    nbTooltip="Delete"
                                    nbTooltipPlacement="top"
                                    nbTooltipStatus="danger"
                                    aria-hidden="true"
                                    (click)="zoneItem.isDeleted = true; addRemoveZoneItems(zoneItem, siteForm)"
                                  ></em>
                                </div>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                      </ng-container>
                    </div>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion class="mb-2" [nbSpinner]="attachmentsLoading">
            <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head">Site Files </nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="file-attachments">
                  <div class="d-flex align-items-start justify-content-end mb-3">
                    <input
                      *ngIf="isEdit"
                      nbInput
                      name="fileSearch"
                      id="input-fileSearch"
                      [(ngModel)]="fileSearchText"
                      #fileSearchModel="ngModel"
                      class="form-control me-2"
                      placeholder="Search Files"
                      (ngModelChange)="fileSearchChanged()"
                    />

                    <button
                      *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && isEdit"
                      nbButton
                      status="primary"
                      size="small"
                      type="button"
                      id="addFiles"
                      class="me-2"
                      [disabled]="!allSelectedFiles.length"
                      (click)="openEditTagsModal(addRemoveFilesTagsModal)"
                    >
                      <span class="d-flex"><em class="pi pi-tag me-2"></em>Manage tags</span>
                    </button>
                    <button
                      *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && isEdit"
                      nbButton
                      status="primary"
                      size="small"
                      type="button"
                      id="addFiles"
                      class="me-2"
                      (click)="openFileUploadSidePanel(false, null)"
                    >
                      <span class="d-flex"><em class="pi pi-plus me-2"></em>Add Files</span>
                    </button>
                  </div>
                  <div class="main-image-gallery">
                    <div id="fixed-table" setTableHeight [isFilterDisplay]="true" class="col-12 table-responsive table-card-view">
                      <table class="table table-hover table-bordered site-files-table" aria-describedby="Ticket List">
                        <thead *ngIf="createFileUploadList.length || fileAttachments?.fileGallery?.length">
                          <tr>
                            <th *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && isEdit" id="select-all" class="text-center">
                              <nb-checkbox
                                id="select-all"
                                class="sfl-track-checkbox"
                                [(ngModel)]="isSelectedAllFiles"
                                (change)="selectAllFiles()"
                                name="selectAllFiles"
                              >
                              </nb-checkbox>
                            </th>
                            <th id="FileName" (click)="sortFiles('fileName', sortOptionList['fileName'])">
                              <div class="d-flex align-items-center">
                                File Name
                                <span
                                  class="fa cursor-pointer ms-auto"
                                  [ngClass]="{
                                    'fa-arrow-up': sortOptionList['fileName'] === 'desc',
                                    'fa-arrow-down': sortOptionList['fileName'] === 'asc',
                                    'icon-selected': sortBy === 'fileName'
                                  }"
                                ></span>
                              </div>
                            </th>
                            <th id="Tags">Tags</th>
                            <th id="Note">Note</th>
                            <th id="UploadedBy">Uploaded By</th>
                            <th id="Date" (click)="sortFiles('createdDate', sortOptionList['createdDate'])">
                              <div class="d-flex align-items-center">
                                Date
                                <span
                                  class="fa cursor-pointer ms-auto"
                                  [ngClass]="{
                                    'fa-arrow-up': sortOptionList['createdDate'] === 'desc',
                                    'fa-arrow-down': sortOptionList['createdDate'] === 'asc',
                                    'icon-selected': sortBy === 'createdDate'
                                  }"
                                ></span>
                              </div>
                            </th>
                            <th id="Action" class="text-center">Action</th>
                          </tr>
                        </thead>
                        <tbody *ngIf="!isCreate">
                          <ng-container *ngIf="fileAttachments?.fileGallery?.length">
                            <tr
                              *ngFor="
                                let document of fileAttachments?.fileGallery
                                  | paginate
                                    : {
                                        id: 'documents',
                                        itemsPerPage: filesPaginationParams.itemsCount,
                                        currentPage: filesPaginationParams.currentPage,
                                        totalItems: fileAttachments?.totalCount
                                      }
                              "
                            >
                              <td
                                data-title="Select Files"
                                class="text-center"
                                *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && isEdit"
                              >
                                <nb-checkbox
                                  id="select-file+{{ document.id }}"
                                  class="sfl-track-checkbox"
                                  name="selectSingleFiles+{{ document.id }}"
                                  (change)="singleFilesCheckChanged(document)"
                                  [(ngModel)]="document.isSelectedForPreview"
                                  [checked]="document.isSelectedForPreview"
                                >
                                </nb-checkbox>
                              </td>
                              <td data-title="File Name">
                                <div class="d-flex align-items-center">
                                  <em aria-hidden="true" class="pi pi-file me-2 pdf-icon text-light cursor-pointer"></em>
                                  <a [href]="document.fileUrl" target="_blank">
                                    {{ document.fileName }}
                                  </a>
                                </div>
                              </td>
                              <td data-title="tags">
                                <ng-container *ngIf="document?.fileTagTxt?.length">
                                  <span class="tag-info-badge fw-bold" *ngFor="let tagName of document?.fileTagTxt | slice : 0 : 5">
                                    <span class="px-2">
                                      {{ tagName }}
                                    </span>
                                  </span>
                                  {{ document?.fileTagTxt?.length > 5 ? '+' + (document?.fileTagTxt?.length - 5) + ' More' : '' }}
                                </ng-container>
                                <ng-container *ngIf="!document?.fileTagTxt?.length">N/A</ng-container>
                              </td>
                              <td data-title="Note">
                                <div
                                  *ngIf="document.notes"
                                  nbTooltip="{{
                                    document.notes.length > 600 ? (document.notes | slice : 0 : 600) + '...' : document.notes
                                  }}"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="primary"
                                >
                                  <sfl-read-more [content]="document.notes"></sfl-read-more>
                                </div>
                                <span *ngIf="!document.notes">N/A</span>
                              </td>
                              <td data-title="Uploaded By">{{ document.createdBy }}</td>
                              <td data-title="Date">{{ document.createdDate | date : fullDateFormat }}</td>
                              <td data-title="Action" class="text-center">
                                <div class="d-flex align-items-center justify-content-center">
                                  <em
                                    *ngIf="isEdit"
                                    class="fa fa-edit text-primary cursor-pointer me-3"
                                    (click)="openFileUploadSidePanel(true, document)"
                                  ></em>
                                  <em
                                    class="fa fa-download text-primary cursor-pointer me-3"
                                    (click)="downloadDropBoxFile(document.id, document.fileName)"
                                  ></em>
                                  <em
                                    *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                    class="fa fa-trash text-danger cursor-pointer"
                                    (click)="deleteDropBoxFile(document.id)"
                                  ></em>
                                </div>
                              </td>
                            </tr>
                          </ng-container>
                        </tbody>
                        <tbody *ngIf="isCreate">
                          <ng-container *ngFor="let document of createFileUploadList">
                            <tr *ngIf="document.fileType === 'document'">
                              <td data-title="File Name">
                                <div class="d-flex align-items-center">
                                  <em aria-hidden="true" class="pi pi-file me-2 pdf-icon text-light cursor-pointer"></em>
                                  <span>
                                    {{ document.fileName }}
                                  </span>
                                </div>
                              </td>
                              <td data-title="tags">
                                <ng-container *ngIf="document?.fileTagTxt?.length">
                                  <span class="tag-info-badge fw-bold" *ngFor="let tagName of document?.fileTagTxt | slice : 0 : 5">
                                    <span class="px-2">
                                      {{ tagName }}
                                    </span>
                                  </span>
                                  {{ document?.fileTagTxt?.length > 5 ? '+' + (document?.fileTagTxt?.length - 5) + ' More' : '' }}
                                </ng-container>
                                <ng-container *ngIf="!document?.fileTagTxt?.length">N/A</ng-container>
                              </td>
                              <td data-title="Note">
                                <div
                                  *ngIf="document.notes"
                                  nbTooltip="{{ document.notes }}"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="primary"
                                >
                                  <sfl-read-more [content]="document.notes"></sfl-read-more>
                                </div>
                                <span *ngIf="!document.notes">N/A</span>
                              </td>
                              <td data-title="Uploaded By">{{ document.createdBy }}</td>
                              <td data-title="Date">{{ document.createdDate | date : fullDateFormat }}</td>
                              <td data-title="Action" class="text-center">
                                <div
                                  class="d-flex align-items-center justify-content-center"
                                  *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                >
                                  <em class="fa fa-trash text-danger cursor-pointer" (click)="deleteDropBoxFile(document.id, true)"></em>
                                </div>
                              </td>
                            </tr>
                          </ng-container>
                        </tbody>
                      </table>
                      <ng-container
                        *ngIf="(!fileAttachments?.fileGallery?.length && !isCreate) || (!createFileUploadList?.length && isCreate)"
                      >
                        <p class="no-record text-center">No Data Found</p>
                      </ng-container>
                    </div>
                    <div class="mt-2 d-md-flex align-items-center" *ngIf="fileAttachments?.fileGallery?.length && !isCreate">
                      <div class="d-flex align-items-center">
                        <label class="mb-0">Items per page: </label>
                        <ng-select
                          class="ms-2"
                          [(ngModel)]="filesPaginationParams.pageSize"
                          [clearable]="false"
                          [searchable]="false"
                          (change)="onChangeSize()"
                          name="documentsPageSize"
                          #documentsPageSize="ngModel"
                          appendTo="body"
                        >
                          <ng-option value="5">5</ng-option>
                          <ng-option value="10">10</ng-option>
                          <ng-option value="50">50</ng-option>
                          <ng-option value="100">100</ng-option>
                        </ng-select>
                      </div>
                      <strong class="ms-md-3">Total: {{ fileAttachments?.totalCount }}</strong>
                      <div class="ms-md-auto ms-sm-0">
                        <pagination-controls
                          id="documents"
                          (pageChange)="onPageChange($event)"
                          class="paginate ticket-attachment"
                        ></pagination-controls>
                      </div>
                    </div>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion class="mb-2" [nbSpinner]="siteNotesLoading" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
            <nb-accordion-item (collapsedChange)="accordionChange($event, 'siteNotesInformation')" [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head">Site Notes </nb-accordion-item-header>
              <nb-accordion-item-body>
                <sfl-notes-listing
                  [entityId]="id"
                  [entityTypeId]="entityTypeId"
                  [entityTypeName]="entityTypeName"
                  [isEntityEditMode]="isEdit"
                  [isEntityViewMode]="isDetail"
                  [isEntityCreateMode]="isCreate"
                  (notesListingLoadingEvent)="siteNotesLoading = $event"
                ></sfl-notes-listing>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <ng-container>
            <nb-accordion class="mb-2" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER, roleType.CONTRACTOR])">
              <nb-accordion-item
                (collapsedChange)="accordionChange($event, 'contractedCostsAndHours')"
                [expanded]="true"
                class="border-bottom"
              >
                <nb-accordion-item-header class="accordion_head">Contracted Costs and Hours </nb-accordion-item-header>
                <nb-accordion-item-body>
                  <div class="row">
                    <div class="col-12 col-sm-6 col-md-3 mb-2" *ngIf="siteDetail.isSiteSelectedForVGTWO">
                      <div class="form-control-group">
                        <label class="label" for="input-cost">Vegetation Costs</label>
                        <span *ngIf="isDetail">{{ siteDetail?.cost | currency : '$' || '-' }}</span>
                        <div *ngIf="isCreate || isEdit">
                          <input
                            nbInput
                            fullWidth
                            fieldSize="small"
                            currencyMask
                            [options]="{ allowNegative: false }"
                            name="cost"
                            id="cost"
                            #cost="ngModel"
                            [(ngModel)]="site.cost"
                            class="form-control"
                            [disabled]="isDetail"
                            maxlength="12"
                          />
                          <sfl-error-msg [control]="cost" [isFormSubmitted]="siteForm?.submitted" fieldName="Cost"></sfl-error-msg>
                        </div>
                      </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-3 mb-2" *ngIf="site.isBankofHours">
                      <div class="form-control-group">
                        <label class="label" for="input-cost">Contracted Hours</label>
                        <p *ngIf="isDetail" class="mb-0">
                          {{ siteDetail?.contractedHours }} Hrs / {{ siteDetail?.remainingContractHours }} Hrs Remaining ({{
                            siteDetail?.contractedHoursPercent | number : '1.2-2'
                          }}%) / {{ siteDetail.pendingContractHours }} Hrs Pending
                        </p>
                        <p *ngIf="isDetail" class="mb-0">
                          Hours replenish : {{ siteDetail?.replenishmentTypeId === 1 ? 'NTP Date' : 'Fiscal Year ' }}
                        </p>
                        <div *ngIf="isCreate || isEdit">
                          <input
                            nbInput
                            fullWidth
                            fieldSize="small"
                            [options]="{ allowNegative: false }"
                            name="contractedHours"
                            id="contractedHours"
                            #contractedHours="ngModel"
                            [(ngModel)]="site.contractedHours"
                            class="form-control"
                            [disabled]="isDetail"
                            maxlength="12"
                          />
                          <sfl-error-msg
                            [control]="contractedHours"
                            [isFormSubmitted]="siteForm?.submitted"
                            fieldName="Contracted Hours"
                          ></sfl-error-msg>
                        </div>
                      </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-3 mb-2" *ngIf="(isCreate || isEdit) && site.isBankofHours">
                      <label class="label" for="input-cost">Replenishment Type</label>
                      <div class="row">
                        <ng-select
                          name="replenishmentType"
                          id="replenishmentType"
                          #replenishmentType="ngModel"
                          [(ngModel)]="site.replenishmentTypeId"
                          [items]="replenishmentTypes"
                          bindLabel="name"
                          bindValue="id"
                          placeholder="Select Replenishment Type"
                          [clearable]="false"
                          (change)="replenishmentTypeChange($event)"
                          appendTo="body"
                        >
                        </ng-select>
                        <sfl-error-msg
                          [control]="replenishmentType"
                          [isFormSubmitted]="siteForm?.submitted"
                          fieldName="Replenishment Type"
                        ></sfl-error-msg>
                      </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-3 mb-2" *ngIf="(isCreate || isEdit) && site.isBankofHours">
                      <!-- NTP Date Picker (shows when NTP Date is selected) -->
                      <div *ngIf="site.replenishmentTypeId === 1">
                        <label class="label" for="replenishmentDateRange">Date</label>
                        <input
                          class="form-control search-textbox"
                          [nbDatepicker]="replenishmentDatePicker"
                          [(ngModel)]="site.contractStartDate"
                          name="replenishmentDateRange"
                          placeholder="Select Date"
                          [disabled]="true"
                          readonly
                          autocomplete="off"
                        />
                        <nb-datepicker #replenishmentDatePicker></nb-datepicker>
                      </div>

                      <!-- Fiscal Year Range Picker (shows when Fiscal Year is selected) -->
                      <div *ngIf="site.replenishmentTypeId === 2">
                        <label class="label" for="replenishmentDateRange">Date</label>
                        <input
                          class="form-control search-textbox"
                          [nbDatepicker]="fiscalYearPicker"
                          [(ngModel)]="site.fiscalDateRange"
                          name="replenishmentFiscalRange"
                          placeholder="Select Fiscal Year Range"
                          readonly
                          autocomplete="off"
                        />
                        <nb-rangepicker #fiscalYearPicker></nb-rangepicker>
                      </div>
                    </div>
                  </div>
                </nb-accordion-item-body>
              </nb-accordion-item>
            </nb-accordion>
          </ng-container>
          <div class="row mt-3" *appHasPermission="[roleType.ADMIN, roleType.DIRECTOR]">
            <div class="col-12">
              <nb-tabset fullWidthv class="row">
                <nb-tab tabTitle="History" [nbSpinner]="exclusionsLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
                  <div class="row">
                    <div class="col-12 history-section">
                      <nb-accordion class="mb-2" *ngFor="let item of siteAuditHistory">
                        <nb-accordion-item
                          (collapsedChange)="accordionChange($event, 'deviceInformation')"
                          [expanded]="historyAccordion"
                          class="border-bottom"
                        >
                          <nb-accordion-item-header class="accordion_head">
                            <strong>{{ item?.userName }}</strong>
                            <div class="label ms-2">{{ item?.action }}</div>
                            <span class="label ms-2"> - </span>
                            <span class="label ms-2">{{ item?.logDate | dateToUsersTimezone : userMomentDateTimeFormat }}</span>
                          </nb-accordion-item-header>
                          <nb-accordion-item-body>
                            <div class="mt-2" *ngIf="item?.auditLogDetails">
                              <div class="row">
                                <div class="col-2"><label class="label mb-0">Field</label></div>
                                <div class="col-5 text-center"><label class="label mb-0">Original Value</label></div>
                                <div class="col-5 text-center"><label class="label mb-0">New Value</label></div>
                              </div>
                              <div class="mt-2" *ngFor="let actionSummary of item?.auditLogDetails">
                                <div class="row">
                                  <div class="col-2">{{ actionSummary?.fieldName }}</div>
                                  <div class="col-5 text-center">{{ actionSummary?.oldValue || '-' }}</div>
                                  <div class="col-5 text-center">{{ actionSummary?.newValue || '-' }}</div>
                                </div>
                              </div>
                            </div>
                          </nb-accordion-item-body>
                        </nb-accordion-item>
                      </nb-accordion>
                    </div>
                  </div>
                  <div class="row col-12" *ngIf="!siteAuditHistory.length">
                    <label>No actions found</label>
                  </div>
                </nb-tab>
              </nb-tabset>
            </div>
          </div>
        </nb-tab>
        <nb-tab
          [tabTitle]="siteAddEditScreenTabsName[siteAddEditScreenTabsEnum.SITE_PHOTO_LIBRARY]"
          [badgeText]="getErrorNumber(siteForm, siteAddEditScreenTabsEnum.SITE_PHOTO_LIBRARY)"
          [active]="getActiveTab(siteAddEditScreenTabsName[siteAddEditScreenTabsEnum.SITE_PHOTO_LIBRARY])"
          [nbSpinner]="sitePhotoLoading"
          badgeStatus="danger"
          [id]="siteAddEditScreenTabsEnum.SITE_PHOTO_LIBRARY"
        >
          <nb-accordion class="mb-3">
            <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head"> Master </nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="text-center">
                  <button
                    nbButton
                    *ngIf="photoGalleryImage?.length === 0 && !dragDropRegion && !checkAuthorisationsFn([roleType.CUSTOMER])"
                    status="primary"
                    (click)="dragDropRegion = true"
                    size="medium"
                    type="button"
                  >
                    <em class="fa fa-upload" style="margin-right: 10px"></em>
                    Upload
                  </button>
                </div>
                <div
                  class="dropZone"
                  *ngIf="photoGalleryImage?.length > 0 || dragDropRegion"
                  ngFileDragDr
                  (fileDropped)="getPhotoGalleryUploadedFiles($event, siteImageTypes.MASTER_IMAGE)"
                >
                  <input
                    type="file"
                    #file
                    accept="image/*"
                    multiple
                    (change)="getPhotoGalleryUploadedFiles($event?.target?.files, siteImageTypes.MASTER_IMAGE, file)"
                  />
                  <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em>
                  <h5 class="fw-bold fs-small">Drop Image To Attach</h5>
                  <label style="text-transform: none" class="fw-bold">OR Click to Browse </label>
                </div>
                <ul *ngIf="masterImg.length">
                  <li *ngFor="let item of masterImg; let i = index">
                    <span>{{ item.name }}</span>
                    <em
                      (click)="deleteFile(i, siteImageTypes.MASTER_IMAGE)"
                      nbtooltip="Delete"
                      nbtooltipplacement="top"
                      nbtooltipstatus="text-danger"
                      aria-hidden="true"
                      class="fa fa-times-circle text-danger ms-2 pointer"
                    ></em>
                  </li>
                </ul>
                <div class="d-flex align-items-center justify-content-center flex-wrap mt-2">
                  <div class="m-2 cdkImagesborder" *ngFor="let item of photoGalleryImage; let i = index">
                    <div class="example-box m-3">
                      <a class="selectImage" nbTooltip="select photo" nbTooltipPlacement="top" nbTooltipStatus="primary">
                        <nb-checkbox
                          name="selected-Image-{{ i }}"
                          id="selected-image-{{ i }}"
                          class="select-check"
                          [(ngModel)]="item.isSelected"
                        ></nb-checkbox>
                      </a>
                      <a class="downloadIcon">
                        <em
                          class="fa fa-download"
                          (click)="downloadSitePhotoImage(item?.imageUrl)"
                          nbTooltip="Download Image"
                          nbTooltipPlacement="top"
                          aria-hidden="true"
                        ></em>
                      </a>
                      <a class="removeIcon">
                        <em
                          class="fa fa-times-circle"
                          style="color: red"
                          nbTooltip="Delete"
                          nbTooltipPlacement="top"
                          aria-hidden="true"
                          (click)="deletePhotoGalleryImages(item?.id)"
                          [id]="'deleteIcon' + i"
                        ></em>
                      </a>
                      <a
                        class="asKeyIcon text-primary"
                        nbTooltip="select photo as a key"
                        nbTooltipPlacement="top"
                        nbTooltipStatus="primary"
                      >
                        <nb-radio
                          [checked]="isKeyPhotoSelected(item)"
                          (valueChange)="onKeyPhotoChange(item?.id)"
                          class="chkRadiobtn chkRadioIncludebtn"
                          [id]="'keyPhoto' + i"
                        ></nb-radio>
                      </a>

                      <img
                        alt="General Image"
                        class="text-center cursor-pointer card-img-top galleryImage"
                        onError="this.src='assets/images/no-image-found.jpg'"
                        [src]="item?.imageUrl"
                        [id]="'img' + i"
                      />
                    </div>
                  </div>
                </div>
                <div class="mt-3">
                  <button
                    *ngIf="photoGalleryImage?.length > 0"
                    (click)="downloadAllAsZipFolder()"
                    nbButton
                    size="medium"
                    type="button"
                    class="mt-3 me-3"
                    status="primary"
                  >
                    Download Folder
                  </button>
                  <button
                    *ngIf="photoGalleryImage?.length > 0"
                    nbButton
                    size="medium"
                    type="button"
                    class="mt-3 me-3"
                    status="primary"
                    (click)="selectAllPhotoGalleryImages()"
                  >
                    Select All
                  </button>
                  <button
                    *ngIf="photoGalleryImage?.length > 0"
                    nbButton
                    size="medium"
                    type="button"
                    class="mt-3"
                    status="primary"
                    (click)="unselectAllPhotoGalleryImages()"
                  >
                    Unselect All
                  </button>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion class="mb-3">
            <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head">PM </nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="form-control-group mt-3">
                  <div class="row">
                    <div class="col-12 reportsFilter">
                      <div class="form-control-group mb-3">
                        <div class="row align-items-center">
                          <div class="col-12 col-md-4 col-xl-2 mb-2 pe-0">
                            <label class="label" for="customer">Year</label>
                            <ng-select
                              id="startYear"
                              name="startYear"
                              [items]="years"
                              bindLabel="name"
                              bindValue="id"
                              [(ngModel)]="pmFilterModel.year"
                              notFoundText="No Year Found"
                              placeholder="Select Year"
                              [clearable]="false"
                              appendTo="body"
                            >
                            </ng-select>
                          </div>
                          <div class="col-12 col-md-4 col-xl-2 mb-2 pe-0">
                            <label class="label" for="customer">Assesment Type</label>
                            <ng-select
                              name="Report Type"
                              [multiple]="true"
                              [items]="reportTypeData"
                              bindLabel="name"
                              bindValue="id"
                              [(ngModel)]="pmFilterModel.assessmentType"
                              notFoundText="No Assessment Type Found"
                              placeholder="Select Assessment Type"
                              appendTo="body"
                              (search)="onDropdownSearchFilter($event, 'filteredAssessmentTypeIds')"
                              (close)="filteredAssessmentTypeIds = []"
                            >
                              <ng-template ng-header-tmp *ngIf="reportTypeData && reportTypeData.length">
                                <button
                                  type="button"
                                  (click)="selectAndDeselectAll(true, 'filteredAssessmentTypeIds')"
                                  class="btn btn-sm btn-primary"
                                >
                                  Select all
                                </button>
                                <button
                                  type="button"
                                  (click)="selectAndDeselectAll(false, 'filteredAssessmentTypeIds')"
                                  class="btn btn-sm btn-primary ms-1"
                                >
                                  Unselect all
                                </button>
                              </ng-template>
                              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <input id="item-{{ index }}" name="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" />
                                {{ item.name }}
                              </ng-template>
                              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                                  <span
                                    class="ng-value-label text-truncate"
                                    [ngClass]="{
                                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                                    }"
                                    >{{ item.name }}</span
                                  >
                                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                                </div>
                                <div class="ng-value" *ngIf="items.length > 1">
                                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                                </div>
                              </ng-template>
                            </ng-select>
                          </div>
                          <div class="col-12 col-md-4 col-xl-2 mt-3 pe-0">
                            <button
                              class="linear-mode-button"
                              (click)="getFilteredPmReports()"
                              nbButton
                              status="primary"
                              size="small"
                              type="button"
                              [disabled]="loading"
                            >
                              View Data
                            </button>
                            <button
                              class="align"
                              (click)="ClearPMFilter()"
                              nbButton
                              status="primary"
                              size="small"
                              type="button"
                              [disabled]="loading"
                            >
                              clear
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form-control-group">
                  <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="mt-3 table-responsive">
                    <table class="table table-hover table-bordered" aria-describedby="Report List">
                      <thead>
                        <tr>
                          <th id="customer">Customer (Portfolio)</th>
                          <th id="site">Site</th>
                          <th id="workOrder"><div>Work Order</div></th>
                          <th id="reportType"><div>Report Type</div></th>
                          <th id="year"><div>Year</div></th>
                          <th id="lastUploaded">Last Uploaded</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let pmItem of sitePhotoPMTableData">
                          <td>{{ pmItem.customerPortfolio || '-' }}</td>
                          <td class="text-truncate" nbTooltipPlacement="top" nbTooltipStatus="primary">
                            {{ pmItem.siteName || '-' }}
                          </td>
                          <td class="report-workOrder">
                            <a href="javascript: void(0);" (click)="navigateToReportGallery(pmItem.workorderId)">{{
                              pmItem.workorderName || '-'
                            }}</a>
                          </td>
                          <td>{{ pmItem.reportType || '-' }}</td>
                          <td>{{ pmItem.year || '-' }}</td>
                          <td style="min-width: 105px">
                            {{ pmItem.uploadedDate ? (pmItem.uploadedDate | date : fullDateFormat) : '-' }}
                          </td>
                        </tr>
                        <tr *ngIf="!sitePhotoPMTableData?.length">
                          <td colspan="6" class="text-center">No Data Found</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion class="mb-3">
            <nb-accordion-item (collapsedChange)="accordionChange($event, 'deviceInformation')" [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head"> CM </nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="row">
                  <div class="ml-auto"></div>
                </div>
                <div class="form-control-group mt-3">
                  <div class="row">
                    <div class="col-12 reportsFilter">
                      <div class="form-control-group mb-3">
                        <div class="row align-items-center">
                          <div class="col-12 col-md-4 col-xl-2 mb-2 pe-0">
                            <label class="label" for="customer">Year</label>
                            <ng-select
                              id="startYear"
                              name="startYear"
                              [items]="years"
                              bindLabel="name"
                              bindValue="id"
                              [(ngModel)]="CMfilterModel.year"
                              notFoundText="No Year Found"
                              placeholder="Select Year"
                              [clearable]="false"
                              appendTo="body"
                            >
                            </ng-select>
                          </div>
                          <div class="col-12 col-md-4 col-xl-2 mb-2 pe-0">
                            <label class="label" for="customer">Priority</label>
                            <ng-select
                              name="Priority"
                              [multiple]="true"
                              [items]="ticketPriority"
                              bindLabel="name"
                              bindValue="id"
                              [(ngModel)]="CMfilterModel.priority"
                              notFoundText="No Priority Found"
                              placeholder="Select Priority"
                              appendTo="body"
                              (search)="onDropdownSearchFilter($event, 'filteredPriorityIds')"
                              (close)="filteredPriorityIds = []"
                            >
                              <ng-template ng-header-tmp *ngIf="ticketPriority && ticketPriority.length">
                                <button
                                  type="button"
                                  (click)="selectAndDeselectAll(true, 'filteredPriorityIds')"
                                  class="btn btn-sm btn-primary"
                                >
                                  Select all
                                </button>
                                <button
                                  type="button"
                                  (click)="selectAndDeselectAll(false, 'filteredPriorityIds')"
                                  class="btn btn-sm btn-primary ms-1"
                                >
                                  Unselect all
                                </button>
                              </ng-template>
                              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <input id="item-{{ index }}" name="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" />
                                {{ item.name }}
                              </ng-template>
                              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                                  <span
                                    class="ng-value-label text-truncate"
                                    [ngClass]="{
                                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                                    }"
                                    >{{ item.name }}</span
                                  >
                                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                                </div>
                                <div class="ng-value" *ngIf="items.length > 1">
                                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                                </div>
                              </ng-template>
                            </ng-select>
                          </div>
                          <div class="col-12 col-md-4 col-xl-2 mt-3 pe-0">
                            <button
                              class="linear-mode-button"
                              (click)="getFilteredCmReports()"
                              nbButton
                              status="primary"
                              size="small"
                              type="button"
                              [disabled]="loading"
                            >
                              View Data
                            </button>
                            <button
                              class="align"
                              (click)="ClearCMFilter()"
                              nbButton
                              status="primary"
                              size="small"
                              type="button"
                              [disabled]="loading"
                            >
                              clear
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form-control-group">
                  <div id="fixed-table" setTableHeight class="mt-3 table-responsive">
                    <table class="table table-hover table-bordered" aria-describedby="Ticket List">
                      <thead>
                        <tr>
                          <th scope="col">Number</th>
                          <th scope="col">Customer (Portfolio)</th>
                          <th scope="col">Site</th>
                          <th scope="col">Device</th>
                          <th scope="col">Issue</th>
                          <th scope="col">Opened</th>
                        </tr>
                      </thead>
                      <tbody>
                        <ng-container *ngIf="sitePhotoCMTableData.length">
                          <tr *ngFor="let cmItem of sitePhotoCMTableData">
                            <td class="pointerTicketNumberLink">
                              <a href="javascript:void(0)" (click)="openImageGallery(false, cmItem.ticketImages, cmItem.ticketNumber)">
                                {{ cmItem.ticketNumber || '-' }}</a
                              >
                            </td>
                            <td>{{ cmItem.customerPortfolio || '-' }}</td>
                            <td>{{ cmItem.siteName || '-' }}</td>
                            <td>{{ cmItem.deviceLabel || '-' }}</td>
                            <td>{{ cmItem.issue || '-' }}</td>
                            <td>{{ cmItem.open ? (cmItem.open | date : fullDateFormat) : '-' }}</td>
                          </tr>
                        </ng-container>
                        <ng-container *ngIf="!sitePhotoCMTableData?.length">
                          <tr>
                            <td colspan="6" class="text-center">No Data Found</td>
                          </tr>
                        </ng-container>
                      </tbody>
                    </table>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
        </nb-tab>
        <nb-tab
          [tabTitle]="siteAddEditScreenTabsName[siteAddEditScreenTabsEnum.PERFORMANCE_INFO]"
          [badgeText]="getErrorNumber(siteForm, siteAddEditScreenTabsEnum.PERFORMANCE_INFO)"
          badgeStatus="danger"
          [id]="siteAddEditScreenTabsEnum.PERFORMANCE_INFO"
          [active]="getActiveTab(siteAddEditScreenTabsName[siteAddEditScreenTabsEnum.PERFORMANCE_INFO])"
        >
          <nb-accordion class="mb-2">
            <nb-accordion-item [expanded]="true" (collapsedChange)="accordionChange($event, 'siteInformation')" class="border-bottom">
              <nb-accordion-item-header class="accordion_head"> Automation Details </nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="custom-x-scroll mb-2">
                  <div style="min-width: 650px">
                    <div class="row" *ngIf="site.siteAutomationDetails.automationSiteDetail.length; let i = index">
                      <div class="col-2">
                        <label class="label" for="input-name"> Data Source </label>
                      </div>
                      <div class="col-2">
                        <label class="label" for="input-name"> Account </label>
                      </div>
                      <div class="col-3">
                        <label class="label" for="input-name"> Site Name / IP Address</label>
                      </div>
                      <div class="col-2">
                        <label class="label" for="input-name">Data Source ID / Token</label>
                      </div>
                      <div class="col-2">
                        <label class="label" for="input-name">Action </label>
                      </div>
                      <div class="col-1">
                        <label class="label text-end" for="input-name">Device Count </label>
                      </div>
                    </div>
                    <div *ngIf="isDetail">
                      <div
                        class="row mb-2"
                        *ngFor="let automationSiteDetail of siteDetail.siteAutomationDetails.automationSiteDetail; let i = index"
                      >
                        <div class="col-2">
                          <span>{{ automationSiteDetail.automationDataSourceName || '-' }}</span>
                        </div>
                        <div class="col-2">
                          <span>{{ automationSiteDetail.automationDataPartnerName || '-' }}</span>
                        </div>
                        <div class="col-3" *ngIf="automationSiteDetail.automationDataSourceId !== 5">
                          <span>{{ automationSiteDetail.automationSiteName || '-' }}</span>
                        </div>
                        <div class="col-3" *ngIf="automationSiteDetail.automationDataSourceId === 5">
                          <span>{{ automationSiteDetail.ipAddress || '-' }}</span>
                        </div>
                        <div class="col-2" *ngIf="automationSiteDetail.automationDataSourceId !== 5">
                          <span>{{ automationSiteDetail.siteNumber || '-' }}</span>
                        </div>
                        <div class="col-2" *ngIf="automationSiteDetail.automationDataSourceId === 5">
                          <button
                            nbButton
                            (click)="openEGaugeCredentialModal(credentialTemplate, i)"
                            status="primary"
                            size="Medium"
                            type="button"
                            id="siteSubmit"
                            class="w-100 p-2"
                          >
                            Credentials
                          </button>
                        </div>
                        <div class="col-2">
                          <span style="color: yellow" *ngIf="automationSiteDetail.isPrimarySite">Primary</span>
                          <a
                            *ngIf="
                              i !== 0 &&
                              !checkAuthorisationsFn([
                                roleType.CUSTOMER,
                                roleType.MANAGER,
                                roleType.SUPPORT,
                                roleType.PORTFOLIOMANAGER,
                                roleType.ANALYST
                              ])
                            "
                            class="text-danger px-2 listgrid-icon"
                            (click)="onDelete(automationSiteDetail.automationSiteDetailId, automationSiteDetail.siteDeviceCount, i)"
                          >
                            <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                          </a>
                        </div>
                        <div class="col-1 text-end">
                          <a
                            *ngIf="automationSiteDetail?.siteDeviceCount"
                            class="cursor-pointer"
                            (click)="gotoAutomationDevice(automationSiteDetail?.automationSiteDetailId)"
                          >
                            {{ automationSiteDetail?.siteDeviceCount }}
                          </a>
                          <span *ngIf="!automationSiteDetail?.siteDeviceCount">-</span>
                        </div>
                      </div>
                    </div>
                    <div *ngIf="isCreate || isEdit">
                      <div
                        class="row mb-2"
                        *ngFor="let automationSiteDetail of site.siteAutomationDetails.automationSiteDetail; let i = index"
                      >
                        <div class="col-2">
                          <ng-select
                            name="{{ 'dataSource-' + i }}"
                            id="{{ 'select-dataSource-' + i }}"
                            [items]="sourceList"
                            bindLabel="dataSourceName"
                            bindValue="id"
                            [(ngModel)]="automationSiteDetail.automationDataSourceId"
                            (change)="dataSourceSelect($event.id, $event.dataSourceName, i, true)"
                            #dataSource="ngModel"
                            notFoundText="No Data Source Found"
                            placeholder="Select Data Source"
                            [clearable]="false"
                            appendTo="body"
                          >
                          </ng-select>
                        </div>
                        <div class="col-2">
                          <ng-select
                            name="{{ 'partnerId-' + i }}"
                            id="{{ 'select-partnerId-' + i }}"
                            [items]="partnerList[i]"
                            bindLabel="partnerName"
                            bindValue="partnerNumber"
                            #partnerId="ngModel"
                            [(ngModel)]="automationSiteDetail.partnerNumber"
                            (change)="partnerSelect($event.partnerNumber, automationSiteDetail.automationDataSourceId, i, null, true)"
                            notFoundText="No partner Found"
                            placeholder="Select Account"
                            [clearable]="false"
                            appendTo="body"
                            [required]="automationSiteDetail.automationDataSourceId"
                          >
                          </ng-select>
                          <sfl-error-msg [control]="partnerId" [isFormSubmitted]="siteForm?.submitted" fieldName="Account"></sfl-error-msg>
                        </div>
                        <div
                          class="col-3"
                          *ngIf="automationSiteDetail.automationDataSourceId !== 4 && automationSiteDetail.automationDataSourceId !== 5"
                        >
                          <ng-select
                            name="{{ 'site-' + i }}"
                            id="{{ 'select-site-' + i }}"
                            bindLabel="apiSiteName"
                            bindValue="apiSiteName"
                            [items]="automationSiteName[i]"
                            #automationSite="ngModel"
                            [(ngModel)]="automationSiteDetail.automationSiteName"
                            (change)="siteSelect($event, i)"
                            notFoundText="No site Found"
                            placeholder="Select Site"
                            [clearable]="false"
                            [loading]="siteLoader[i]"
                            loadingText="Fetching Sites..."
                            appendTo="body"
                            [disabled]="automationSiteDetail.automationDataSourceId === -1"
                            [required]="automationSiteDetail.automationDataSourceId !== -1 && automationSiteDetail.automationDataSourceId"
                          >
                          </ng-select>
                          <sfl-error-msg
                            [control]="automationSite"
                            [isFormSubmitted]="siteForm?.submitted"
                            fieldName="Automation SiteName"
                          ></sfl-error-msg>
                        </div>
                        <ng-container
                          *ngIf="automationSiteDetail.automationDataSourceId !== 4 && automationSiteDetail.automationDataSourceId !== 5"
                        >
                          <div class="col-2">
                            <input
                              name="{{ 'number-' + i }}"
                              id="{{ 'input-number-' + i }}"
                              nbInput
                              fullWidth
                              [(ngModel)]="automationSiteDetail.siteNumber"
                              #automationSiteNumber="ngModel"
                              class="form-control"
                              spellcheck="true"
                              contenteditable="true"
                              appendTo="body"
                              disabled
                            />
                          </div>
                          <div class="col-2">
                            <span style="color: yellow" *ngIf="automationSiteDetail.isPrimarySite">Primary</span>
                            <a
                              *ngIf="i !== 0"
                              class="text-danger px-2 listgrid-icon"
                              (click)="onDelete(automationSiteDetail.automationSiteDetailId, automationSiteDetail.siteDeviceCount, i)"
                            >
                              <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                            </a>
                          </div>
                          <div class="col-1 text-end">
                            <span>{{ automationSiteDetail.siteDeviceCount }}</span>
                          </div>
                        </ng-container>
                        <div
                          class="col-3"
                          *ngIf="automationSiteDetail.automationDataSourceId === 4 || automationSiteDetail.automationDataSourceId === 5"
                        >
                          <input
                            name="{{ 'string-' + i }}"
                            id="{{ 'input-string-' + i }}"
                            nbInput
                            fullWidth
                            [(ngModel)]="automationSiteDetail.ipAddress"
                            #automationIpAddress="ngModel"
                            class="form-control"
                            spellcheck="true"
                            contenteditable="true"
                            appendTo="body"
                          />
                        </div>
                        <ng-container
                          *ngIf="automationSiteDetail.automationDataSourceId === 4 || automationSiteDetail.automationDataSourceId === 5"
                        >
                          <div class="col-2">
                            <input
                              *ngIf="automationSiteDetail.automationDataSourceId === 4"
                              name="{{ 'number-' + i }}"
                              id="{{ 'input-number-' + i }}"
                              nbInput
                              fullWidth
                              [(ngModel)]="automationSiteDetail.token"
                              #automationTokenId="ngModel"
                              class="form-control"
                              spellcheck="true"
                              contenteditable="true"
                              appendTo="body"
                            />
                            <button
                              *ngIf="automationSiteDetail.automationDataSourceId === 5"
                              nbButton
                              (click)="openEGaugeCredentialModal(credentialTemplate, i)"
                              status="primary"
                              size="Medium"
                              type="button"
                              id="siteSubmit"
                              class="w-100 p-2"
                            >
                              Credentials
                            </button>
                          </div>
                          <div class="col-2">
                            <span style="color: yellow" *ngIf="automationSiteDetail.isPrimarySite">Primary</span>
                            <a
                              *ngIf="i !== 0"
                              class="text-danger px-2 listgrid-icon"
                              (click)="onDelete(automationSiteDetail.automationSiteDetailId, automationSiteDetail.siteDeviceCount, i)"
                            >
                              <em class="fa fa-trash" nbTooltip="Delete" nbTooltipPlacement="top" nbTooltipStatus="danger"></em>
                            </a>
                          </div>
                          <div class="col-1 text-end">
                            <span>{{ automationSiteDetail.siteDeviceCount }}</span>
                          </div>
                        </ng-container>
                      </div>
                    </div>
                  </div>
                </div>
                <div *ngIf="isEdit || isCreate">
                  <span class="text-primary pointerReportLink" (click)="addDataSource()">+ Add Data Source</span>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion class="mb-2" *ngIf="site.siteAutomationDetails.automationSiteDetail.length">
            <nb-accordion-item [expanded]="true" (collapsedChange)="accordionChange($event, 'siteInformation')" class="border-bottom">
              <nb-accordion-item-header class="accordion_head">Performance Details</nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="row">
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <label class="label">Is String Inverter Site</label>
                    <div class="d-flex">
                      <nb-toggle
                        [(checked)]="site.siteAutomationDetails.automationSitePerformanceDetails.isStringInverterSite"
                        (checkedChange)="site.siteAutomationDetails.automationSitePerformanceDetails.isStringInverterSite = $event"
                        status="primary"
                        [disabled]="isDetail"
                      ></nb-toggle>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <label class="label" for="input-name"> Primary Meter(s)</label>
                    <span *ngIf="isDetail">{{
                      siteDetail?.siteAutomationDetails?.automationSitePerformanceDetails?.primaryMeterDeviceName || '-'
                    }}</span>
                    <div *ngIf="isCreate || isEdit">
                      <ng-select
                        name="primaryMeter"
                        bindLabel="name"
                        bindValue="id"
                        #primaryMeter="ngModel"
                        [items]="deviceMeter"
                        [(ngModel)]="site.siteAutomationDetails.automationSitePerformanceDetails.primaryMeterDevice"
                        notFoundText="No primary meter Found"
                        placeholder="Select primary Meter"
                        [clearable]="false"
                        appendTo="body"
                        [multiple]="true"
                        (search)="onDropdownSearchFilter($event, 'filteredPrimaryMeterDeviceIds')"
                        (close)="filteredPrimaryMeterDeviceIds = []"
                      >
                        <ng-template ng-header-tmp>
                          <button
                            type="button"
                            (click)="selectAndDeselectAll(true, 'filteredPrimaryMeterDeviceIds')"
                            class="btn btn-sm btn-primary"
                          >
                            Select all
                          </button>
                          <button
                            type="button"
                            (click)="selectAndDeselectAll(false, 'filteredPrimaryMeterDeviceIds')"
                            class="btn btn-sm btn-primary ms-1"
                          >
                            Unselect all
                          </button>
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                          <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
                          {{ item.name }}
                        </ng-template>
                        <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                          <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                            <span class="ng-value-label">{{ item.name }}</span>
                            <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                          </div>
                          <div class="ng-value" *ngIf="items.length > 2">
                            <span class="ng-value-label">+{{ items.length - 2 }} </span>
                          </div>
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <label class="label" for="input-name"> Aggregate Meter(s)</label>
                    <span *ngIf="isDetail">{{
                      siteDetail?.siteAutomationDetails?.automationSitePerformanceDetails?.aggregateMeterDeviceName || '-'
                    }}</span>
                    <div *ngIf="isCreate || isEdit">
                      <ng-select
                        name="aggregateMeter"
                        bindLabel="name"
                        bindValue="id"
                        #aggregateMeter="ngModel"
                        [items]="deviceMeter"
                        [(ngModel)]="site.siteAutomationDetails.automationSitePerformanceDetails.aggregateMeterDevice"
                        notFoundText="No aggregate meter Found"
                        placeholder="Select aggregate Meter"
                        [clearable]="false"
                        appendTo="body"
                        [multiple]="true"
                        (search)="onDropdownSearchFilter($event, 'filteredAggregateMeterDeviceIds')"
                        (close)="filteredAggregateMeterDeviceIds = []"
                      >
                        <ng-template ng-header-tmp>
                          <button
                            type="button"
                            (click)="selectAndDeselectAll(true, 'filteredAggregateMeterDeviceIds')"
                            class="btn btn-sm btn-primary"
                          >
                            Select all
                          </button>
                          <button
                            type="button"
                            (click)="selectAndDeselectAll(false, 'filteredAggregateMeterDeviceIds')"
                            class="btn btn-sm btn-primary ms-1"
                          >
                            Unselect all
                          </button>
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                          <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
                          {{ item.name }}
                        </ng-template>
                        <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                          <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                            <span class="ng-value-label">{{ item.name }}</span>
                            <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                          </div>
                          <div class="ng-value" *ngIf="items.length > 2">
                            <span class="ng-value-label">+{{ items.length - 2 }} </span>
                          </div>
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <label class="label" for="input-name"> Sub Meter(s)</label>
                    <span *ngIf="isDetail">{{
                      siteDetail?.siteAutomationDetails?.automationSitePerformanceDetails?.subMeterDeviceName || '-'
                    }}</span>
                    <div *ngIf="isCreate || isEdit">
                      <ng-select
                        name="subMeter"
                        bindLabel="name"
                        bindValue="id"
                        #subMeter="ngModel"
                        [items]="deviceMeter"
                        [(ngModel)]="site.siteAutomationDetails.automationSitePerformanceDetails.subMeterDevice"
                        notFoundText="No sub meter Found"
                        placeholder="Select sub Meter"
                        [clearable]="false"
                        appendTo="body"
                        [multiple]="true"
                        (search)="onDropdownSearchFilter($event, 'filteredSubMeterDeviceIds')"
                        (close)="filteredSubMeterDeviceIds = []"
                      >
                        <ng-template ng-header-tmp>
                          <button
                            type="button"
                            (click)="selectAndDeselectAll(true, 'filteredSubMeterDeviceIds')"
                            class="btn btn-sm btn-primary"
                          >
                            Select all
                          </button>
                          <button
                            type="button"
                            (click)="selectAndDeselectAll(false, 'filteredSubMeterDeviceIds')"
                            class="btn btn-sm btn-primary ms-1"
                          >
                            Unselect all
                          </button>
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                          <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
                          {{ item.name }}
                        </ng-template>
                        <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                          <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                            <span class="ng-value-label">{{ item.name }}</span>
                            <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                          </div>
                          <div class="ng-value" *ngIf="items.length > 2">
                            <span class="ng-value-label">+{{ items.length - 2 }} </span>
                          </div>
                        </ng-template>
                      </ng-select>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <label class="label" for="input-name"> Primary Weather Sensor</label>
                    <span *ngIf="isDetail">{{
                      siteDetail?.siteAutomationDetails?.automationSitePerformanceDetails?.primaryWeatherSensorDeviceName || '-'
                    }}</span>
                    <div *ngIf="isCreate || isEdit">
                      <ng-select
                        name="primaryweather"
                        bindLabel="name"
                        bindValue="id"
                        #primaryweather="ngModel"
                        [items]="deviceWeather"
                        [(ngModel)]="site.siteAutomationDetails.automationSitePerformanceDetails.primaryWeatherSensorDevice"
                        notFoundText="No Primary Weather Found"
                        placeholder="Select Primary Weather Sensor"
                        [clearable]="false"
                        appendTo="body"
                      >
                      </ng-select>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <label class="label" for="input-name"> Module Degradation</label>
                    <span *ngIf="isDetail">{{
                      siteDetail?.siteAutomationDetails?.automationSitePerformanceDetails?.moduleDegradation || '-'
                    }}</span>
                    <div *ngIf="isCreate || isEdit">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="site.siteAutomationDetails.automationSitePerformanceDetails.moduleDegradation"
                        #module="ngModel"
                        name="module"
                        id="input-name"
                        class="form-control"
                        spellcheck="true"
                        contenteditable="true"
                        appendTo="body"
                      />
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <label class="label" for="input-name"> System Losses</label>
                    <span *ngIf="isDetail">{{
                      siteDetail?.siteAutomationDetails?.automationSitePerformanceDetails?.systemLosses || '-'
                    }}</span>
                    <div *ngIf="isCreate || isEdit">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="site.siteAutomationDetails.automationSitePerformanceDetails.systemLosses"
                        #systemloss="ngModel"
                        name="siteNumber"
                        id="input-name"
                        class="form-control"
                        spellcheck="true"
                        contenteditable="true"
                        appendTo="body"
                        sflNumbersOnly
                      />
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <label class="label" for="input-name"> Start Month/Year</label>
                    <span *ngIf="isDetail">{{ getMonthYear() }}</span>
                    <div *ngIf="isCreate || isEdit">
                      <div class="row">
                        <div class="col-6">
                          <ng-select
                            name="startmonth"
                            bindLabel="name"
                            bindValue="id"
                            [(ngModel)]="site.siteAutomationDetails.automationSitePerformanceDetails.startMonth"
                            #startmonth="ngModel"
                            notFoundText="No month Found"
                            placeholder="Select Month"
                            [clearable]="false"
                            appendTo="body"
                          >
                            <ng-option *ngFor="let month of monthId" [value]="month.id">
                              {{ month?.value }}
                            </ng-option>
                          </ng-select>
                        </div>
                        <div class="col-6">
                          <ng-select
                            name="startyear"
                            bindLabel="name"
                            bindValue="id"
                            [items]="years"
                            [(ngModel)]="site.siteAutomationDetails.automationSitePerformanceDetails.startYear"
                            #startyear="ngModel"
                            notFoundText="No year Found"
                            placeholder="Select Year"
                            [clearable]="false"
                            appendTo="body"
                          >
                          </ng-select>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <label class="label" for="input-name"> Temperature Coefficient</label>
                    <span *ngIf="isDetail">{{
                      siteDetail?.siteAutomationDetails?.automationSitePerformanceDetails?.temperatureCoefficient || '-'
                    }}</span>
                    <div *ngIf="isCreate || isEdit">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="site.siteAutomationDetails.automationSitePerformanceDetails.temperatureCoefficient"
                        #temp="ngModel"
                        name="temp"
                        id="input-name"
                        class="form-control"
                        spellcheck="true"
                        contenteditable="true"
                        appendTo="body"
                        sflNumbersOnly
                      />
                    </div>
                  </div>
                  <div class="col-12 col-sm-6 col-lg-4 mb-2">
                    <label class="label" for="input-name">Inverter Efficiency</label>
                    <span *ngIf="isDetail">{{
                      siteDetail?.siteAutomationDetails?.automationSitePerformanceDetails?.inverterEfficiency || '-'
                    }}</span>
                    <div *ngIf="isCreate || isEdit">
                      <input
                        nbInput
                        fullWidth
                        [(ngModel)]="site.siteAutomationDetails.automationSitePerformanceDetails.inverterEfficiency"
                        #inverter="ngModel"
                        name="inverter"
                        id="input-name"
                        class="form-control"
                        spellcheck="true"
                        contenteditable="true"
                        appendTo="body"
                        sflNumbersOnly
                      />
                    </div>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion
            class="mb-2"
            [nbSpinner]="productionLoading"
            nbSpinnerStatus="primary"
            nbSpinnerSize="large"
            *ngIf="site.siteAutomationDetails.automationSiteDetail.length"
          >
            <nb-accordion-item [expanded]="true" (collapsedChange)="accordionChange($event, 'siteInformation')" class="border-bottom">
              <nb-accordion-item-header class="accordion_head">Performance Model Table</nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="row">
                  <div
                    *ngIf="
                      site.id &&
                      site.siteAutomationDetails.automationSitePerformanceDetails.startYear &&
                      site.siteAutomationDetails.automationSitePerformanceDetails.startMonth &&
                      site.siteAutomationDetails.automationSitePerformanceDetails.moduleDegradation &&
                      !checkAuthorisationsFn([roleType.CUSTOMER])
                    "
                    class="col-12 text-end"
                  >
                    <button
                      nbButton
                      status="primary"
                      class="floar-end mb-2"
                      size="small"
                      type="button"
                      id="siteSubmit"
                      [disabled]="productionLoading"
                      *ngIf="!performanceRecordUpdated"
                      (click)="generatePerformanceRecord()"
                    >
                      Generate Production Data
                    </button>
                  </div>
                  <div class="col-12 table-responsive">
                    <table class="table table-hover table-bordered" aria-describedby="Site Performance List">
                      <thead>
                        <tr>
                          <th scope="col">Month</th>
                          <th scope="col">Expected Insolation</th>
                          <th scope="col">Expected Production</th>
                          <th scope="col" class="text-end" *ngFor="let item of siteExpectedTableHeader">{{ item }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let sitePerformance of site.siteAutomationDetails.listOfAutomationSitePerformanceModelTable;
                            let i = index
                          "
                        >
                          <td>
                            <span>{{ sitePerformance.monthName }}</span>
                          </td>
                          <td>
                            <span *ngIf="isDetail">{{
                              siteDetail.siteAutomationDetails.listOfAutomationSitePerformanceModelTable[i].expectedInsolationValue || '-'
                            }}</span>
                            <input
                              *ngIf="isCreate || isEdit"
                              name="expectedinsolation-{{ i }}"
                              id="input-name"
                              class="form-control"
                              spellcheck="true"
                              contenteditable="true"
                              [(ngModel)]="sitePerformance.expectedInsolationValue"
                              nbInput
                              class="form-control"
                              sflValidators
                            />
                          </td>
                          <td>
                            <span *ngIf="isDetail">{{
                              siteDetail.siteAutomationDetails.listOfAutomationSitePerformanceModelTable[i].expectedProductionValue || '-'
                            }}</span>
                            <input
                              *ngIf="isCreate || isEdit"
                              name="expectedProduction-{{ i }}"
                              id="input-production"
                              class="form-control"
                              spellcheck="true"
                              contenteditable="true"
                              [(ngModel)]="sitePerformance.expectedProductionValue"
                              nbInput
                              class="form-control"
                              sflValidators
                            />
                          </td>
                          <td
                            class="text-end black-color"
                            *ngFor="let item of performanceExpectation"
                            [style]="'background-color:' + getColor(item, sitePerformance.month) + ' !important'"
                          >
                            {{ getValueExpectedValue(item, sitePerformance.month) | sflDecimal : true : true }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
          <nb-accordion
            *ngIf="
              site.siteAutomationDetails.automationSiteDetail.length &&
              isOutageAlert &&
              checkAuthorisationsFn([roleType.ADMIN, roleType.DIRECTOR])
            "
          >
            <nb-accordion-item [expanded]="true" class="border-bottom">
              <nb-accordion-item-header class="accordion_head">Alerts</nb-accordion-item-header>
              <nb-accordion-item-body>
                <div class="row">
                  <div class="col-12 col-lg-6">
                    <div class="row mb-2">
                      <div class="col-12 col-md-6 mb-2">
                        <nb-toggle
                          status="primary"
                          [disabled]="isDetail"
                          [(ngModel)]="siteOutage.zeroGeneration"
                          name="isZeroGeneration"
                          labelPosition="start"
                        >
                          <label class="label">Zero Generation</label>
                        </nb-toggle>
                      </div>
                      <div class="col-12 col-md-6 mb-2">
                        <nb-toggle
                          status="primary"
                          [disabled]="isDetail || !siteOutage.zeroGeneration"
                          [(ngModel)]="siteOutage.isParentSetting"
                          name="portfolioSetting"
                          labelPosition="start"
                          (ngModelChange)="useParentSetting($event)"
                        >
                          <label class="label">Use Portfolio Settings</label>
                        </nb-toggle>
                      </div>
                    </div>
                    <div class="row mb-2">
                      <div class="col-12 col-md-6 mb-2">
                        <div class="form-control-group">
                          <label class="label" for="input-alertpowerThreshold">Power Threshold (kW)</label>
                          <span *ngIf="isDetail">{{ siteOutage?.powerThreshold ? siteOutage?.powerThreshold : '-' }}</span>
                          <div *ngIf="isEdit || isCreate">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="siteOutage.powerThreshold"
                              #alertPowerThreshold="ngModel"
                              name="alertPowerThreshold"
                              id="input-alertpowerThreshold"
                              class="form-control"
                              [disabled]="!siteOutage.zeroGeneration || siteOutage.isParentSetting"
                              sflNumbersOnly
                            />
                          </div>
                        </div>
                      </div>
                      <div class="col-12 col-md-6 mb-2">
                        <div class="form-control-group">
                          <label class="label" for="input-triggerCount">Trigger Count</label>
                          <span *ngIf="isDetail">{{ siteOutage?.triggerCount ? siteOutage?.triggerCount : '-' }}</span>
                          <div *ngIf="isEdit || isCreate">
                            <input
                              nbInput
                              fullWidth
                              [(ngModel)]="siteOutage.triggerCount"
                              #triggerCount="ngModel"
                              [disabled]="!siteOutage.zeroGeneration || siteOutage.isParentSetting"
                              name="triggerCount"
                              id="input-triggerCount"
                              class="form-control"
                              sflNumbersOnly
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="label">Daytime Alerting Window</label>
                      <table class="table table-hover table-bordered" aria-describedby="Site Performance List">
                        <thead>
                          <tr>
                            <th scope="col">Month</th>
                            <th scope="col">Local Start Time</th>
                            <th scope="col">Local End Time</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let Daytime of siteOutage.timeSetting; let i = index">
                            <td>
                              <span>{{ getMonthName(Daytime.month) }}</span>
                            </td>
                            <td>
                              <span *ngIf="isDetail">{{ Daytime.startTime }}</span>
                              <input
                                *ngIf="isCreate || isEdit"
                                name="expectedStartTime-{{ i }}"
                                id="input-name"
                                [(ngModel)]="siteOutage?.timeSetting[i].startTime"
                                class="form-control"
                                [disabled]="!siteOutage.zeroGeneration || siteOutage.isParentSetting"
                                spellcheck="true"
                                contenteditable="true"
                                nbInput
                                (ngModelChange)="formatTime($event, i, 'startTime')"
                                maxlength="5"
                                placeholder="hh:mm"
                                class="form-control"
                              />
                            </td>
                            <td>
                              <span *ngIf="isDetail">{{ Daytime.endTime }}</span>
                              <input
                                *ngIf="isCreate || isEdit"
                                name="expectedEndTime-{{ i }}"
                                id="input-production"
                                [(ngModel)]="siteOutage?.timeSetting[i].endTime"
                                class="form-control"
                                [disabled]="!siteOutage.zeroGeneration || siteOutage.isParentSetting"
                                spellcheck="true"
                                contenteditable="true"
                                nbInput
                                maxlength="5"
                                placeholder="hh:mm"
                                (ngModelChange)="formatTime($event, i, 'endTime')"
                                class="form-control"
                              />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </nb-accordion-item-body>
            </nb-accordion-item>
          </nb-accordion>
        </nb-tab>
        <nb-tab
          [nbSpinner]="loadAutomationTable"
          [tabTitle]="siteAddEditScreenTabsName[siteAddEditScreenTabsEnum.AUTOMATION_SITE_DEVICE_LIST]"
          [badgeText]="getErrorNumber(siteForm, siteAddEditScreenTabsEnum.AUTOMATION_SITE_DEVICE_LIST)"
          badgeStatus="danger"
          [id]="siteAddEditScreenTabsEnum.AUTOMATION_SITE_DEVICE_LIST"
          [active]="getActiveTab(siteAddEditScreenTabsName[siteAddEditScreenTabsEnum.AUTOMATION_SITE_DEVICE_LIST])"
          *ngIf="
            !checkAuthorisationsFn([roleType.CUSTOMER]) &&
            siteDetail.id &&
            siteDetail.siteAutomationDetails.automationSiteDetail &&
            siteDetail.siteAutomationDetails.automationSiteDetail.length &&
            siteDetail.siteAutomationDetails.automationSiteDetail[0].automationSiteDetailId
          "
        >
          <sfl-automation-site-device-list
            *ngIf="activeTabTitle === siteAddEditScreenTabsName[siteAddEditScreenTabsEnum.AUTOMATION_SITE_DEVICE_LIST]"
            (loading)="loadAutomationTable = $event"
            [(siteId)]="id"
            [(customerId)]="site.customerId"
            [(portfolioId)]="site.portfolioId"
            [(automationDataSourceId)]="siteDetail.siteAutomationDetails.automationSiteDetail[0].automationDataSourceId"
            [(automationSiteDetailId)]="siteDetail.siteAutomationDetails.automationSiteDetail[0].automationSiteDetailId"
          ></sfl-automation-site-device-list>
        </nb-tab>
      </nb-tabset>
    </form>
  </nb-card-body>
</nb-card>

<ng-template #credentialTemplate>
  <div class="alert-box">
    <div class="modal-header">
      <h4 class="modal-title">Credentials</h4>
      <button type="button" class="close" aria-label="Close" (click)="onModalClose()">
        <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="mb-4">
        <label class="label">User Name</label>
        <input
          name="UserName"
          id="userName"
          nbInput
          [(ngModel)]="eGaugeCredentials.automationUserName"
          fullWidth
          [readOnly]="isDetail"
          #automationUserName="ngModel"
          class="form-control border-0"
          spellcheck="true"
          contenteditable="true"
          appendTo="body"
        />
      </div>
      <div class="mb-4">
        <label class="label">Password</label>
        <input
          name="UserPassword"
          id="userPassword"
          nbInput
          fullWidth
          [(ngModel)]="eGaugeCredentials.automationPassword"
          [readOnly]="isDetail"
          #automationUserPassword="ngModel"
          class="form-control border-0"
          spellcheck="true"
          contenteditable="true"
          appendTo="body"
        />
      </div>
    </div>
    <div class="modal-footer" *ngIf="!isDetail">
      <button
        nbButton
        (click)="saveCredential(eGaugeCredentials.eGaugeIndex)"
        status="primary"
        size="Medium"
        type="button"
        id="siteSubmit"
        class="btn floar-end btn-primary"
      >
        Save
      </button>
    </div>
  </div>
</ng-template>

<ng-template #addRemoveFilesTagsModal>
  <div class="selcet-inverter">
    <div class="modal-header align-items-center">
      <h6 class="modal-title">Manage file tags</h6>
      <button type="button" class="close" aria-label="Close" (click)="addRemoveFilesTagsModalRef.hide(); searchText = ''">
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="row align-items-center">
        <!-- <p><strong>Selected files :</strong> {{ selectedFilesNames }}</p> -->
        <div class="file-attachments">
          <strong>Selected files :</strong>
          <ng-container *ngIf="allSelectedFiles?.length">
            <span class="tag-info-badge fw-bold" *ngFor="let file of allSelectedFiles | slice : 0 : 3">
              <span class="px-2">
                {{ file?.fileName }}
              </span>
            </span>
            <span nbTooltip="{{ selectedFilesNamesString }}" nbtooltipplacement="top" nbTooltipStatus="primary">
              <span class="tag-info-badge fw-bold" *ngIf="allSelectedFiles?.length > 3">{{ '+' + (allSelectedFiles?.length - 3) }}</span>
            </span>
          </ng-container>
        </div>
        <div class="col-12 mt-2 file-tag-dd">
          <label class="label" for="fileTagsApply">Select file tags </label>
          <ng-select
            name="fileTagsApply"
            id="region-drop-down"
            class="sfl-track-dropdown"
            [multiple]="true"
            [items]="filesTagList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="fileTagIds"
            #fileTagsApply="ngModel"
            notFoundText="No File Tag Found"
            placeholder="Select File Tag"
            [closeOnSelect]="false"
            (search)="onFilter($event)"
            (ngModelChange)="reorderTags()"
            (close)="filteredAppliedTags = []"
          >
            <ng-template ng-header-tmp *ngIf="filesTagList && filesTagList.length">
              <button type="button" (click)="toggleSelectUnselectAllTags(true)" class="btn btn-sm btn-primary me-2">Select all</button>
              <button type="button" (click)="toggleSelectUnselectAllTags(false)" class="btn btn-sm btn-primary ml-2">Unselect all</button>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
              {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                <span class="ng-value-label">{{ item.name }}</span>
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value" *ngIf="items.length > 2">
                <span class="ng-value-label">+{{ items.length - 2 }} </span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <div class="d-flex justify-content-end align-items-center">
        <button
          class="linear-mode-button me-3"
          nbButton
          status="secondary"
          size="small"
          (click)="addRemoveMultipleFilesTags(false)"
          type="button"
        >
          Remove Tags
        </button>
        <button
          class="linear-mode-button me-3"
          nbButton
          status="primary"
          size="small"
          (click)="addRemoveMultipleFilesTags(true)"
          type="button"
        >
          Apply Tags
        </button>
      </div>
    </div>
  </div>
</ng-template>
