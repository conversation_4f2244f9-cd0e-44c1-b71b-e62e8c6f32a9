import { Pipe, PipeTransform } from '@angular/core';
import moment from 'moment-timezone';
import { StorageService } from '../services/storage.service';

@Pipe({
  name: 'dateToUsersTimezone'
})

/**
 * @whatitdoes it accepts date in UTC and converts it to the user's timezone which is set in their profile
 * @howtouse  <p>{{today | dateToUsersTimezone: 'hh:mm A'}}</p>
 * @brief     by default browser will convert the date to user's local, so we need to override that behaviour
 * @returns   :string time converted to the user's timezone
 */
export class DateToUsersTimezonePipe implements PipeTransform {
  constructor(private readonly storageService: StorageService) {}
  transform(date: Date | string, timeFormat: string, timeZone?: string): string {
    const timeZoneData = this.storageService.get('user').timeZoneData;
    const zone = timeZone ? timeZone : timeZoneData ? timeZoneData.name : null;
    if (date) {
       date = moment.utc(date).format();
      return moment.tz(date, zone).format(timeFormat);
    }
    return '';
  }
}
