import { DecimalPipe } from '@angular/common';
import { Inject, LOCALE_ID, Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'sflDecimal' })
export class TowDecimalPipe implements PipeTransform {
  constructor(@Inject(LOCALE_ID) public locale: string, private _decimalPipe: DecimalPipe) {}
  transform(input: string, isTwoDecimal = true, returnNull = false): string {
    if ((returnNull && !input) || input === 'NaN') {
      return input;
    }
    let with2Decimals = isTwoDecimal && input ? input.toString().match(/^-?\d+(?:\.\d{0,2})?/)[0] : input.toString();
    with2Decimals = this._decimalPipe.transform(with2Decimals, '1.0', 'en-US');
    const numbers = with2Decimals.split('.');
    if (isTwoDecimal) {
      if (numbers[1]) {
        if (numbers[1].length === 1) {
          numbers[1] += '0';
        }
      } else {
        numbers[1] = '00';
      }
    }
    with2Decimals = numbers.join('.');
    return with2Decimals;
  }
}

@Pipe({
  name: 'twoDecimalPlaces'
})
export class TwoDecimalPlacesPipe implements PipeTransform {
  transform(value: number): number {
    // If the value is not a number, return it as is
    if (isNaN(value)) {
      return value;
    }

    // Fix the number to two decimal places and convert it back to a number
    return Math.round(value * 100) / 100;
  }
}
