import { NgModule } from '@angular/core';
import { NotificationsComponent } from './notifications.component';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../../@shared/shared.module';
import { NotificationAddEditComponent } from './notification-add-edit/notification-add-edit.component';

@NgModule({
  declarations: [NotificationsComponent, NotificationAddEditComponent],
  imports: [
    CommonModule,
    SharedModule,
    CommonModule,
    RouterModule.forChild([{ path: '', component: NotificationsComponent, data: { pageTitle: 'Notifications' } }])
  ]
})
export class NotificationsModule {}
