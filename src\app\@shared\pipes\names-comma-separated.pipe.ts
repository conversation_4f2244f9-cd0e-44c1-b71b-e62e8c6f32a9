import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'namesWithBreak'
})
export class NamesWithBreakPipe implements PipeTransform {
  transform(items: any[]): string {
    return items
      .slice(2)
      .map(item => item.name)
      .join(', ');
  }
}

@Pipe({
  name: 'datesWithComma'
})
export class DateWithCommaPipe implements PipeTransform {
  constructor(private readonly datePipe: DatePipe) {}
  transform(items: any[]): string {
    return items
      .slice(2)
      .map(item =>
        item.isReschedule
          ? this.datePipe.transform(item.rescheduleDate, 'MM/dd/yyyy')
          : this.datePipe.transform(item.dateScheduled, 'MM/dd/yyyy')
      )
      .join(', ');
  }
}
