import { Injectable } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup } from '@angular/forms';
import {
  HazardDetailByWorkType,
  WorkStepDetailByWorkType,
  WorkStepsForHazards,
  WorkTypes,
  WorkTypesForWorkStep
} from '../../../@shared/models/jha.model';

@Injectable({
  providedIn: 'root'
})
export class JhaHelperService {
  constructor(private readonly fb: FormBuilder) {}

  initOrPatchWorkType(item: WorkTypes, reportId: string): FormGroup {
    return this.fb.group({
      workTypeId: new FormControl(item.workTypeId),
      workTypeName: new FormControl(item.workTypeName),
      reportId: new FormControl(item.reportId ?? reportId ?? ''),
      id: new FormControl(item.id ?? '')
    });
  }

  initOrPatchListOfWorkSteps(item: WorkStepDetailByWorkType): FormGroup {
    return this.fb.group({
      workStepId: new FormControl(item.workStepId),
      workStep: new FormControl(item.workStep),
      displayWorkStep: new FormControl(item.displayWorkStep),
      equipmentType: new FormControl(item.equipmentType),
      equipmentModel: new FormControl(item.equipmentModel),
      asBuildPageNumber: new FormControl(item.asBuildPageNumber),
      isLotoWorkStep: new FormControl(item.isLotoWorkStep),
      orderValue: new FormControl(item.orderValue),
      isIncludedWorkStep: new FormControl(item.isIncludedWorkStep),
      listOfWorkType: this.fb.array(item.listOfWorkType.map(workType => this.initOrPatchListOfWorkTypeForWorkStep(workType))),
      randomGuid: new FormControl(item.randomGuid)
    });
  }

  initOrPatchListOfWorkTypeForWorkStep(item: WorkTypesForWorkStep): FormGroup {
    return this.fb.group({
      workTypeId: new FormControl(item.workTypeId),
      workType: new FormControl(item.workType)
    });
  }

  initOrPatchListOfHazards(item: HazardDetailByWorkType): FormGroup {
    return this.fb.group({
      hazardId: new FormControl(item.hazardId),
      hazards: new FormControl(item.hazards),
      displayHazards: new FormControl(item.displayHazards),
      riskLevel: new FormControl(item.riskLevel),
      riskLevelId: new FormControl(item.riskLevelId),
      controlBarriers: new FormControl(item.controlBarriers),
      protectiveBarriers: new FormControl(item.protectiveBarriers),
      supportBarriers: new FormControl(item.supportBarriers),
      hazardOrder: new FormControl(item.hazardOrder),
      isLotoHazard: new FormControl(item.isLotoHazard),
      isIncludedHazard: new FormControl(item.isIncludedHazard),
      lotoBarrierMasterId: new FormControl(item.lotoBarrierMasterId),
      listOfWorkStep: this.fb.array(item.listOfWorkStep.map(workStep => this.initOrPatchListOfWorkStepsForHazards(workStep))),
      randomGuid: new FormControl(item.randomGuid)
    });
  }

  initOrPatchListOfWorkStepsForHazards(item: WorkStepsForHazards): FormGroup {
    return this.fb.group({
      workStepId: new FormControl(item.workStepId),
      workStep: new FormControl(item.workStep)
    });
  }

  setOrderOfListOfWorkStepItems(listOfWorkStep: WorkStepDetailByWorkType[]): WorkStepDetailByWorkType[] {
    let i = 1;
    return listOfWorkStep.map((item: WorkStepDetailByWorkType, index: number) => {
      item.orderValue = i++;
      item.orderChange = index === 0;
      return item;
    });
  }

  setOrderOfListOfHazardItems(listOfHazard: HazardDetailByWorkType[]): HazardDetailByWorkType[] {
    let i = 1;
    return listOfHazard.map((item: HazardDetailByWorkType, index: number) => {
      item.hazardOrder = i++;
      item.orderChange = index === 0;
      return item;
    });
  }
}
