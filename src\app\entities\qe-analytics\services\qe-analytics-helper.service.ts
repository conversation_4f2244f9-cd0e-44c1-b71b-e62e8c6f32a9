import { Injectable } from '@angular/core';
import {
  HeatMapColor,
  QEAnalyticsClickEventModuleChartRes,
  QEAnalyticsClickEventTableRes,
  QEAnalyticsEngagementHeatMapChartRes,
  QEAnalyticsKeyMetricsRes,
  QEAnalyticsRefreshCountAndIntervalChartRes,
  QEMenuModuleType
} from '../models/qe-analytics.model';
import {
  QE_ANALYTICS_HEAT_MAP_COLOR_ENUM,
  QE_ANALYTICS_HEAT_MAP_COLOR_MAP,
  QE_ANALYTICS_KEY_METRICS_COLOR_LIST
} from '../models/qe-analytics.enum';
import { QEAnalyticsService } from './qe-analytics.service';

@Injectable({
  providedIn: 'root'
})
export class QEAnalyticsHelperService {
  constructor(private readonly qeAnalyticsService: QEAnalyticsService) {}

  private getParentNamesByMenuIds(qeMenuModuleList: QEMenuModuleType[], menuIds: number[]): string[] {
    const result = new Set<string>();
    const visitedIds = new Set<number>();

    const findParentRecursively = (menuItem: QEMenuModuleType): void => {
      if (!menuItem || visitedIds.has(menuItem.menuId)) return;

      visitedIds.add(menuItem.menuId);
      result.add(menuItem.parentName);

      if (menuItem.parentName && menuItem.menuLevelOrder >= 1) {
        const parent = qeMenuModuleList.find(
          item => item.menuName === menuItem.parentName && item.menuLevelOrder >= 1 && item.menuId !== menuItem.menuId
        );
        if (parent) findParentRecursively(parent);
      }
    };

    menuIds.forEach(id => {
      const menuItem = qeMenuModuleList.find(item => item.menuId === id);
      if (menuItem) findParentRecursively(menuItem);
    });

    return Array.from(result);
  }

  private getHeatMapColor(menuClickedPercentage: number): HeatMapColor {
    const clamped = Math.max(0, Math.min(100, menuClickedPercentage));
    const nearestStep = (Math.floor(clamped / 5) * 5) as QE_ANALYTICS_HEAT_MAP_COLOR_ENUM;
    return QE_ANALYTICS_HEAT_MAP_COLOR_MAP[nearestStep];
  }

  private flattenMenuItems(item: QEAnalyticsClickEventTableRes, menuLevelOrder = 1): QEAnalyticsClickEventTableRes[] {
    const result: QEAnalyticsClickEventTableRes[] = [];
    const itemWithMenuLevelOrder = { ...item, menuLevelOrder };

    result.push(menuLevelOrder === 1 || item.childrens?.length > 0 ? itemWithMenuLevelOrder : itemWithMenuLevelOrder);

    if (item.childrens?.length > 0) {
      item.childrens.forEach(child => {
        result.push(...this.flattenMenuItems(child, menuLevelOrder + 1));
      });
    }
    return result;
  }

  private processMenuItem(
    item: QEAnalyticsClickEventTableRes,
    qeMenuModuleIds: number[],
    parentMenuNames: string[],
    isFilterApplied: boolean
  ): QEAnalyticsClickEventTableRes {
    return {
      ...item,
      heatMapColor: this.getHeatMapColor(item.menuClickedPercentage),
      isFilterApplied: isFilterApplied && (qeMenuModuleIds.includes(item.menuId) || parentMenuNames.includes(item.menuName))
    };
  }

  parseQEAnalyticsTableResponse(qeAnalyticsClickEventTableRes: QEAnalyticsClickEventTableRes[], qeMenuModuleIds: number[]) {
    const qeMenuModuleList = this.qeAnalyticsService.setQEMenuModuleListWithEnumMapping();
    const totalMenuModuleName = qeMenuModuleList.map(item => item.menuName);
    const parentMenuNames = this.getParentNamesByMenuIds(qeMenuModuleList, qeMenuModuleIds);
    const isFilterApplied = qeMenuModuleIds?.length > 0 && qeMenuModuleIds.length !== totalMenuModuleName.length;
    const processedTableResCategories = qeAnalyticsClickEventTableRes
      .map(processedItem => {
        const processedTableResItems = processedItem.childrens
          .flatMap(item => this.flattenMenuItems(item))
          .map(item => this.processMenuItem(item, qeMenuModuleIds, parentMenuNames, isFilterApplied));

        return {
          ...this.processMenuItem(processedItem, qeMenuModuleIds, parentMenuNames, isFilterApplied),
          processedTableResItems
        };
      })
      .map(processedItem => ({
        ...processedItem,
        isFilterApplied: isFilterApplied && processedItem.processedTableResItems.some(item => item.isFilterApplied)
      }));

    return { processedTableResCategories, isFilterApplied };
  }

  parseQEAnalyticsKeyMetricsResponse(qeAnalyticsKeyMetricsRes: QEAnalyticsKeyMetricsRes[]) {
    let colorIndex = 0;

    const qeAnalyticsKeyMetricsData = qeAnalyticsKeyMetricsRes.map(item => {
      const textColor = QE_ANALYTICS_KEY_METRICS_COLOR_LIST[colorIndex];
      colorIndex = (colorIndex + 1) % QE_ANALYTICS_KEY_METRICS_COLOR_LIST.length;

      return {
        ...item,
        textColor
      };
    });

    return qeAnalyticsKeyMetricsData;
  }

  parseQEAnalyticsClickEventModuleChartResponse(qeAnalyticsClickEventModuleChartRes: QEAnalyticsClickEventModuleChartRes[]) {
    const clickEventModuleChartRes = {
      title: {
        text: 'User Module Engagement (Clicks)',
        subtext: 'Total ' + qeAnalyticsClickEventModuleChartRes.map(e => e.value).reduce((prev, next) => prev + next, 0),
        left: 'left',
        padding: [10, 0, 0, 30],
        top: 20,
        textStyle: {
          fontSize: '0.9375rem'
        }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        height: '80%',
        type: 'scroll',
        padding: [100, 0, 0, 30],
        textStyle: {
          width: 135,
          overflow: 'truncate',
          ellipsis: '...'
        }
      },
      tooltip: {
        trigger: 'item'
      },
      toolbox: {
        feature: {
          saveAsImage: { show: true }
        },
        top: 7,
        right: 40
      },
      series: [
        {
          type: 'pie',
          radius: '70%',
          center: ['65%', '47%'],
          label: {
            formatter: '{b} ({c})'
          },
          data: qeAnalyticsClickEventModuleChartRes
        }
      ]
    };
    return clickEventModuleChartRes;
  }

  parseQEAnalyticsEngagementHeatMapChartResponse(qeAnalyticsEngagementHeatMapChartRes: QEAnalyticsEngagementHeatMapChartRes[]) {
    const periods = ['Day', 'Week', 'Month'];
    const heatmapYCategories = periods.filter(
      period => !qeAnalyticsEngagementHeatMapChartRes.every(item => item.value[period.toLowerCase()] === null)
    );
    const heatmapXCategories = qeAnalyticsEngagementHeatMapChartRes.map(item => item.name);
    const heatmapValues = qeAnalyticsEngagementHeatMapChartRes.flatMap((item, colIndex) =>
      heatmapYCategories.map((yLabel, rowIndex) => [colIndex, rowIndex, item.value[yLabel.toLowerCase()].avgClicks])
    );
    const totalTimeSpent = totalSeconds =>
      (Math.floor(totalSeconds / 3600) > 0 ? `${Math.floor(totalSeconds / 3600)}h ` : '') +
        (Math.floor((totalSeconds % 3600) / 60) > 0 ? `${Math.floor((totalSeconds % 3600) / 60)}m ` : '') +
        (totalSeconds % 60 > 0 ? `${totalSeconds % 60}s` : '') || '0s';
    const heatmapTooltipValues = qeAnalyticsEngagementHeatMapChartRes.flatMap((item, colIndex) =>
      heatmapYCategories.map((yLabel, rowIndex) => [colIndex, rowIndex, item.value[yLabel.toLowerCase()].avgTimeSpent])
    );

    const engagementHeatMapChartRes = {
      title: {
        text: 'Engagement Heatmap (Clicks %)',
        left: 'center',
        top: 20,
        textStyle: { fontSize: '0.9375rem' }
      },
      tooltip: {
        trigger: 'item',
        position: 'top',
        formatter: ({ marker, name, dataIndex }) =>
          `<div>${marker} <strong>${name}</strong> <br/> <span style="margin-left: 18px;"><strong>Clicks: ${
            heatmapValues[dataIndex][2]
          }% </strong> </span> <br/> <span style="margin-left: 18px;"><strong>Avg Time: ${totalTimeSpent(
            heatmapTooltipValues[dataIndex][2]
          )} </strong> </span> </div>`
      },
      grid: {
        height: '50%',
        top: '10%'
      },
      dataZoom: [
        {
          type: 'slider',
          xAxisIndex: 0,
          bottom: 40,
          left: 'center',
          height: 30,
          width: 400,
          start: 0,
          end: 30,
          minSpan: [10]
        },
        {
          type: 'inside',
          xAxisIndex: 0,
          start: 0,
          end: 30
        }
      ],
      toolbox: {
        feature: {
          saveAsImage: { show: true }
        },
        top: 7,
        right: 40
      },
      yAxis: {
        type: 'category',
        data: heatmapYCategories,
        splitArea: { show: true }
      },
      xAxis: {
        type: 'category',
        axisLabel: {
          interval: 0,
          rotate: 60
        },
        data: heatmapXCategories,
        splitArea: { show: true }
      },
      visualMap: {
        min: 0,
        max: 100,
        orient: 'vertical',
        right: '30',
        top: 'center',
        itemHeight: '300',
        calculable: true,
        inRange: {
          color: Object.values(QE_ANALYTICS_HEAT_MAP_COLOR_MAP).map(c => c.hexBGColor)
        }
      },
      series: [
        {
          name: '',
          type: 'heatmap',
          data: heatmapValues,
          label: {
            show: true,
            formatter: ({ value }) => value[2] + '%'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0,0,0,0.5)'
            }
          }
        }
      ]
    };

    return engagementHeatMapChartRes;
  }

  parseQEAnalyticsRefreshCountAndIntervalChartResponse(
    qeAnalyticsRefreshCountAndIntervalChartRes: QEAnalyticsRefreshCountAndIntervalChartRes[]
  ) {
    const refreshCountAndIntervalChartRes = qeAnalyticsRefreshCountAndIntervalChartRes.flatMap(item => {
      const dates = item.chartData.map(d => d.date);
      const refreshCounts = item.chartData.map(d => d.value.refreshCount);
      const refreshIntervals = item.chartData.map(d => d.value.refreshInterval);

      return [
        {
          title: { text: `${item.menuName} - Refresh Counts`, left: 'center', top: 20, textStyle: { fontSize: '0.9375rem' } },
          isChartShow: !refreshCounts.every(item => item === null),
          tooltip: {
            trigger: 'item',
            position: 'top'
          },
          grid: {
            height: '50%',
            top: '20%'
          },
          xAxis: {
            type: 'category',
            axisLabel: {
              interval: 0,
              rotate: 60
            },
            data: dates,
            name: 'Date',
            splitArea: { show: true }
          },
          yAxis: { type: 'value', name: 'Count', splitArea: { show: true } },
          toolbox: {
            feature: {
              saveAsImage: { show: true }
            },
            top: 7,
            right: 40
          },
          dataZoom: [
            {
              type: 'slider',
              xAxisIndex: 0,
              bottom: 40,
              left: 'center',
              height: 30,
              width: 400,
              start: 0,
              end: 70,
              minSpan: [7]
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              start: 0,
              end: 70
            }
          ],
          series: [
            {
              data: refreshCounts,
              center: ['50%', '50%'],
              type: 'bar',
              itemStyle: { color: '#87CEEB' },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowColor: 'rgba(0,0,0,0.5)'
                }
              }
            }
          ]
        },
        {
          title: {
            text: `${item.menuName} - Avg Interval Between Refreshes (min)`,
            left: 'center',
            top: 20,
            textStyle: { fontSize: '0.9375rem' }
          },
          isChartShow: !refreshIntervals.every(item => item === null),
          tooltip: {
            trigger: 'item',
            position: 'top'
          },
          grid: {
            height: '50%',
            top: '20%'
          },
          xAxis: {
            type: 'category',
            axisLabel: {
              interval: 0,
              rotate: 60
            },
            data: dates,
            name: 'Date',
            splitArea: { show: true }
          },
          yAxis: { type: 'value', name: 'Minutes', splitArea: { show: true } },
          toolbox: {
            feature: {
              saveAsImage: { show: true }
            },
            top: 7,
            right: 40
          },
          dataZoom: [
            {
              type: 'slider',
              xAxisIndex: 0,
              bottom: 40,
              left: 'center',
              height: 30,
              width: 400,
              start: 0,
              end: 70,
              minSpan: [7]
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              start: 0,
              end: 70
            }
          ],
          series: [
            {
              data: refreshIntervals,
              center: ['50%', '50%'],
              type: 'line',
              smooth: false,
              itemStyle: { color: 'orange' },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowColor: 'rgba(0,0,0,0.5)'
                }
              }
            }
          ]
        }
      ];
    });
    return refreshCountAndIntervalChartRes;
  }
}
