<div class="alert-box" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <h6 class="modal-title">{{ isEdit ? 'Edit Notification' : 'Add Notification' }}</h6>
    <div class="d-flex align-items-center">
      <button type="button" class="close" aria-label="Close" (click)="onCancel()">
        <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
      </button>
    </div>
  </div>
  <form
    name="notificationAddEditForm"
    #notificationAddEditForm="ngForm"
    aria-labelledby="title"
    autocomplete="off"
    (ngSubmit)="notificationAddEditForm.valid && onSubmit()"
  >
    <div class="modal-body">
      <div class="row">
        <div class="col-12 col-sm-4 pe-sm-0 mb-2">
          <label class="label" for="customer">Customer <span class="ms-1 text-danger">*</span></label>
          <ng-select
            id="customer-single-drop-down"
            class="sfl-track-dropdown notification-dropdown"
            name="customer"
            [items]="customerList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="notification.customerId"
            notFoundText="No Customer Found"
            placeholder="Select Customer"
            (change)="onCustomerSelectDeSelect()"
            (clear)="onClearCustomer()"
            sflAutoSearchWithKeyboardName="customer"
            #customerName="ngModel"
            required
          >
          </ng-select>
          <sfl-error-msg
            [control]="customerName"
            [isFormSubmitted]="notificationAddEditForm?.submitted"
            fieldName="Customer Name"
          ></sfl-error-msg>
        </div>

        <div class="col-12 col-sm-4 pe-sm-0 mb-2">
          <label class="label" for="portfolio">Portfolio <span class="ms-1 text-danger">*</span></label>
          <ng-select
            id="portfolio-drop-down"
            class="sfl-track-dropdown notification-dropdown"
            name="Portfolio"
            [multiple]="true"
            [items]="portfolioList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="notification.portfolioIds"
            [closeOnSelect]="false"
            (change)="onPortfolioSelectDeSelect()"
            (clear)="onClearPortfolio()"
            notFoundText="No Portfolio Found"
            placeholder="Select Portfolio"
            sflAutoSearchWithKeyboardName="Portfolio"
            #portfolioName="ngModel"
            required
          >
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input name="item-{{ index }}" id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                <span
                  class="ng-value-label text-truncate"
                  [ngClass]="{
                    'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                    'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                    'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                  }"
                >
                  {{ item.name }}</span
                >
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value h-28px" *ngIf="items.length > 1">
                <span class="ng-value-label">+{{ items.length - 1 }} </span>
              </div>
            </ng-template>
          </ng-select>
          <sfl-error-msg
            [control]="portfolioName"
            [isFormSubmitted]="notificationAddEditForm?.submitted"
            fieldName="Portfolio"
          ></sfl-error-msg>
        </div>

        <div class="col-12 col-sm-4 pe-sm-3 mb-2">
          <label class="label" for="site">Site <span class="ms-1 text-danger">*</span></label>
          <ng-select
            id="site-drop-down"
            class="sfl-track-dropdown notification-dropdown"
            name="Site"
            [multiple]="true"
            [items]="siteList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="notification.siteIds"
            [closeOnSelect]="false"
            notFoundText="No Site Found"
            placeholder="Select Site"
            sflAutoSearchWithKeyboardName="Site"
            #siteName="ngModel"
            required
          >
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input name="item-{{ index }}" id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                <span
                  class="ng-value-label text-truncate"
                  [ngClass]="{
                    'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                    'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                    'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                  }"
                >
                  {{ item.name }}</span
                >
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value h-28px" *ngIf="items.length > 1">
                <span class="ng-value-label">+{{ items.length - 1 }} </span>
              </div>
            </ng-template>
          </ng-select>
          <sfl-error-msg [control]="siteName" [isFormSubmitted]="notificationAddEditForm?.submitted" fieldName="Site Name"></sfl-error-msg>
        </div>

        <div class="col-12 col-sm-4 pe-sm-0 mb-2">
          <label class="label" for="input-notificationTypeId"
            >Notification Type <span class="ms-1 text-danger" *ngIf="notification?.isMissingConfig">*</span></label
          >
          <ng-select
            id="notification-type-drop-down"
            class="sfl-track-dropdown notification-dropdown"
            name="notificationType"
            [multiple]="true"
            [items]="notificationTypeList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="notification.notificationTypeIds"
            [closeOnSelect]="false"
            notFoundText="No Notification Type Found"
            placeholder="Select Notification Type"
            sflAutoSearchWithKeyboardName="Notification Type"
            #notificationType="ngModel"
            [required]="notification?.isMissingConfig"
          >
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input name="item-{{ index }}" id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                <span
                  class="ng-value-label text-truncate"
                  [ngClass]="{
                    'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                    'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                    'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                  }"
                >
                  {{ item.name }}</span
                >
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value h-28px" *ngIf="items.length > 1">
                <span class="ng-value-label">+{{ items.length - 1 }} </span>
              </div>
            </ng-template>
          </ng-select>
          <sfl-error-msg
            [control]="notificationType"
            [isFormSubmitted]="notificationAddEditForm?.submitted"
            fieldName="Notification Type"
          ></sfl-error-msg>
        </div>
        <div class="col-12 col-sm-4 pe-sm-0 mb-2 pt-3 d-flex align-items-center">
          <nb-checkbox [(ngModel)]="notification.includePhotos" name="includePhotos"> Include Photos </nb-checkbox>
        </div>
      </div>
    </div>
    <div class="modal-footer" [ngClass]="isShowActiveToggle && isEdit ? 'd-flex justify-content-between align-items-center' : ''">
      <nb-toggle [(ngModel)]="isActive" name="isToggled" *ngIf="isShowActiveToggle && isEdit"> Active </nb-toggle>
      <div>
        <button class="mx-2" nbButton status="basic" size="medium" type="button" (click)="onCancel()">Back</button>
        <button nbButton status="primary" size="medium" type="submit" [disabled]="!notificationAddEditForm.valid">
          {{ isEdit ? 'Update' : 'Add Notification' }}
        </button>
      </div>
    </div>
  </form>
</div>
