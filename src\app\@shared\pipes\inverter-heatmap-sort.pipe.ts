import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'inverterHeatmapSort'
})
export class InverterHeatmapSortPipe implements PipeTransform {
  transform(array: any, field: string, direction: string): any[] {
    if (!Array.isArray(array)) {
      return;
    }
    array.sort((a: any, b: any) => {
      if (field === 'deviceName') {
        if (direction === 'asc') {
          if (a.order === b.order) {
            return a[field].localeCompare(b[field], undefined, {
              numeric: true,
              sensitivity: 'base'
            });
          }
          return a.order - b.order;
        } else {
          if (a.order === b.order) {
            return b[field].localeCompare(a[field], undefined, {
              numeric: true,
              sensitivity: 'base'
            });
          }
          return b.order - a.order;
        }
      } else {
        if (direction === 'desc') {
          if (a[field] > b[field]) {
            return -1;
          } else if (a[field] < b[field]) {
            return 1;
          } else {
            return 0;
          }
        } else {
          if (a[field] < b[field]) {
            return -1;
          } else if (a[field] > b[field]) {
            return 1;
          } else {
            return 0;
          }
        }
      }
    });
    return array;
  }
}
