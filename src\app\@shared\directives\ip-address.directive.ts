import { Directive, ElementRef, HostListener, Input } from '@angular/core';

@Directive({
  selector: '[appIpAddress]'
})
export class IpAddressDirective {
  @Input('appIpAddress') allowIPv6: boolean = false;

  constructor(private el: ElementRef) {}

  private ipv4PartialRegex = /^(\d{1,3}(\.\d{0,3}){0,3})?$/;
  private ipv6PartialRegex = /^([0-9a-fA-F]{0,4}(:[0-9a-fA-F]{0,4}){0,7})?$/;

  private ipv4FullRegex = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
  private ipv6FullRegex = new RegExp(
    '^(' +
      '([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|' +
      '([0-9a-fA-F]{1,4}:){1,7}:|' +
      '([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|' +
      '([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|' +
      '([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|' +
      '([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|' +
      '([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|' +
      '[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|' +
      ':((:[0-9a-fA-F]{1,4}){1,7}|:)' +
      ')$'
  );

  private isValidPartialIPv6(value: string): boolean {
    return this.ipv6PartialRegex.test(value);
  }

  private isValidPartialIPv4(value: string): boolean {
    const segments = value.split('.');
    if (segments.length > 4) return false;
    for (const seg of segments) {
      if (!/^\d*$/.test(seg)) return false;
      if (seg.length > 0 && +seg > 255) return false;
    }
    return true;
  }

  private isValid(value: string, fromPaste = false): boolean {
    if (this.allowIPv6) {
      return fromPaste
        ? this.ipv6FullRegex.test(value) || this.ipv4FullRegex.test(value)
        : this.isValidPartialIPv6(value) || this.isValidPartialIPv4(value);
    } else {
      return fromPaste ? this.ipv4FullRegex.test(value) : this.isValidPartialIPv4(value);
    }
  }

  @HostListener('input', ['$event'])
  onInput(event: Event) {
    const input = this.el.nativeElement;
    let value = input.value;

    if (value && !this.isValid(value)) {
      input.value = value.slice(0, -1);
      return;
    }
  }

  @HostListener('paste', ['$event'])
  onPaste(event: ClipboardEvent) {
    const pastedInput: string = event.clipboardData?.getData('text') ?? '';
    if (!this.isValid(pastedInput, true)) {
      event.preventDefault();
    }
  }
}
