import { Injectable } from '@angular/core';
import * as CryptoJS from 'crypto-js';
import { CommonFilter } from '../components/filter/common-filter.model';
import { FILTER_PAGE_NAME } from '../components/filter/filter.model';
import { AppConstants } from '../constants';

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  get(key: string) {
    const encryptKey = `${key}Encrypted`;
    const encryptedVal = localStorage.getItem(encryptKey);
    if (encryptedVal) {
      const data = this.getDecryptedValue('sflEncryptDecryptValue', encryptedVal);
      return JSON.parse(data);
    }
    const val = localStorage.getItem(key);
    if (val) {
      return JSON.parse(val);
    }
    return null;
  }

  set(key: string, obj: any, isEncrypt = true) {
    let data: string;
    if (isEncrypt) {
      key += 'Encrypted';
      data = this.getEncryptedValue('sflEncryptDecryptValue', JSON.stringify(obj));
    } else {
      data = JSON.stringify(obj);
    }
    localStorage.setItem(key, data);
  }

  clear(key: string) {
    localStorage.removeItem(key);
    key += 'Encrypted';
    localStorage.removeItem(key);
  }

  clearAll() {
    localStorage.clear();
  }

  getEncryptedValue(keys: string, value: string) {
    return CryptoJS.AES.encrypt(value, keys).toString();
  }

  getDecryptedValue(keys: string, value: string) {
    const decrypted = CryptoJS.AES.decrypt(value, keys);
    return decrypted.toString(CryptoJS.enc.Utf8);
  }

  sessionSetItem(key: string, obj: any, isEncrypt = true) {
    let data: string;
    if (isEncrypt) {
      key += 'Encrypted';
      data = this.getEncryptedValue('sflEncryptDecryptValue', JSON.stringify(obj));
    } else {
      data = JSON.stringify(obj);
    }
    sessionStorage.setItem(key, data);
  }
  sessionGetItem(key: string) {
    const encryptKey = `${key}Encrypted`;
    const encryptedVal = sessionStorage.getItem(encryptKey);
    if (encryptedVal) {
      const data = this.getDecryptedValue('sflEncryptDecryptValue', encryptedVal);
      return JSON.parse(data);
    }
    const val = sessionStorage.getItem(key);
    if (val) {
      return JSON.parse(val);
    }
    return null;
  }

  sessionClearItem(key: string) {
    sessionStorage.removeItem(key);
    key += 'Encrypted';
    sessionStorage.removeItem(key);
  }

  updateSharedFilters(filterModel: any) {
    const sharedFilters = {
      customerIds: Array.isArray(filterModel?.customerIds) && filterModel.customerIds.length ? filterModel.customerIds : [],
      portfolioIds: Array.isArray(filterModel?.portfolioIds) && filterModel.portfolioIds.length ? filterModel.portfolioIds : [],
      siteIds: Array.isArray(filterModel?.siteIds) && filterModel.siteIds.length ? filterModel.siteIds : []
    };
    this.set(AppConstants.SHARED_FILTER_KEY, sharedFilters);
  }

  mergeSharedFiltersIntoModel(model: CommonFilter, filterFor = ''): CommonFilter {
    const shared = this.get(AppConstants.SHARED_FILTER_KEY);
    if (!shared) return model;
    const sharedCustomerIds = (shared.customerIds.length ? shared.customerIds : model.customerIds) || [],
      sharedPortfolioIds = (shared.portfolioIds.length ? shared.portfolioIds : model.portfolioIds) || [],
      sharedSiteIds = (shared.siteIds.length ? shared.siteIds : model.siteIds) || [];
    switch (filterFor) {
      case FILTER_PAGE_NAME.CM_TICKET_AUDIT_LISTING:
        return {
          ...model,
          customerIds: model.customerIds,
          portfolioIds: sharedPortfolioIds,
          siteIds: sharedSiteIds
        };
      case FILTER_PAGE_NAME.SITE_INFO_PORTFOLIO_LISTING:
        return {
          ...model,
          customerIds: sharedCustomerIds,
          portfolioIds: [],
          siteIds: []
        };
      case FILTER_PAGE_NAME.SITE_INFO_SITES_LISTING:
        return {
          ...model,
          customerIds: sharedCustomerIds,
          portfolioIds: sharedPortfolioIds,
          siteIds: []
        };
      case FILTER_PAGE_NAME.PM_SCOPE_LISTING:
        return {
          ...model,
          customerIds: sharedCustomerIds,
          portfolioIds: sharedPortfolioIds,
          siteIds: []
        };
      default:
        return {
          ...model,
          customerIds: sharedCustomerIds,
          portfolioIds: sharedPortfolioIds,
          siteIds: sharedSiteIds
        };
    }
  }

  shouldCallListApi(
    viewPageFilters: any,
    defaultFilters: any,
    localDefaultFilters: any,
    cpsFilters: any,
    relevantKeysForPage: string[]
  ): boolean {
    const hasValidFilter = (filters: any, keys: string[]) => {
      if (!filters) return false;

      return keys.some(key => {
        const value = filters[key];

        if (value === null || value === undefined || value === false) return false;

        if (typeof value === 'boolean') return value === true;

        if (Array.isArray(value)) return value.length > 0;

        return value !== '';
      });
    };

    const defaultKeys = ['portfolioIds', 'siteIds', 'states', 'regionIds', 'subregionIds'];
    const cpsKeys = ['customerIds', 'portfolioIds', 'siteIds'];

    return (
      hasValidFilter(viewPageFilters, relevantKeysForPage) ||
      hasValidFilter(defaultFilters, defaultKeys) ||
      hasValidFilter(localDefaultFilters, defaultKeys) ||
      hasValidFilter(cpsFilters, cpsKeys)
    );
  }

  sessionClearAll() {
    sessionStorage.clear();
  }
}
