import { Http<PERSON><PERSON>, HttpEvent, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { ApiUrl, AppConstants, ErrorMessages } from '../constants';
import { AlertService } from './alert.service';
import { StorageService } from './storage.service';
import { RxJsUtilsService } from '../../@core/utils/rxjs-utils.service';
import { QEAnalyticsGatheringService } from '../../entities/qe-analytics/services/qe-analytics-gathering.service';

@Injectable({
  providedIn: 'root'
})
export class InterceptorService implements HttpInterceptor {
  constructor(
    private readonly router: Router,
    private readonly alertService: AlertService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly storageService: StorageService,
    private readonly httpClient: HttpClient,
    private readonly qeAnalyticsGatheringService: QEAnalyticsGatheringService
  ) {}

  callApiForCheckingValidateToken(): Observable<any> {
    const appTokens = {
      id_Token: this.storageService.get(AppConstants.authenticationToken),
      refresh_Token: this.storageService.get(AppConstants.refreshToken)
    };

    const identifier = environment.serverUrl + ApiUrl.validateToken;
    const apiCall$ = this.httpClient.post(ApiUrl.validateToken, appTokens);

    return appTokens.id_Token && appTokens.refresh_Token && apiCall$.pipe(RxJsUtilsService.shareDuplicate(identifier));
  }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (!req || !req.url || (/^http/.test(req.url) && !(environment.serverUrl && req.url.startsWith(environment.serverUrl)))) {
      return next.handle(req);
    }

    const encryptUserId = this.storageService.get(AppConstants.encryptUserId);
    let authToken = '',
      authRequest;
    const suppressErrorHandling = req.headers.get('suppress-error-handling');

    if (this.router.url.includes('fill-mobile-form')) {
      authToken = this.activatedRoute.snapshot.queryParams['authenticationToken'];
    } else {
      authToken = this.storageService.get(AppConstants.authenticationToken);
    }

    if (authToken) {
      authRequest = req.clone({
        headers: req.headers.set('Authorization', 'Bearer ' + authToken)
      });
    } else if (encryptUserId) {
      authRequest = req.clone({
        headers: req.headers.set('API', encryptUserId)
      });
    } else {
      authRequest = req.clone();
    }
    authRequest = authRequest.clone({
      url: environment.serverUrl + authRequest.url
    });
    return next.handle(authRequest).pipe(
      tap(
        (event: HttpEvent<string>) => {
          this.requestSuccess(event);
        },
        error => {
          return throwError(error);
        }
      ),
      catchError(error => {
        if (suppressErrorHandling) {
          return throwError(() => error);
        }
        if (error.error instanceof Error) {
          // A client-side or network error occurred. Handle it accordingly.
        } else {
          if (!error.status) {
            this.alertService.showErrorToast(ErrorMessages.serverNotAvailable);
            this.qeAnalyticsGatheringService.onQEUserLogoutSessionExpiredEmit().finally(() => {
              localStorage.clear();
              this.router.navigateByUrl('/auth/login');
            });
          } else if (error.status === 405) {
            this.alertService.showErrorToast(error.message);
          } else if (error.status === 400 && error.url.search('downloadQESTFormPDF') > 1) {
            this.alertService.showErrorToast(ErrorMessages.noFileMessage);
          } else if (error.status === 400 && error.url.search('updateWOScheduleDetail') > 1) {
            this.alertService.showErrorToast(ErrorMessages.rescheduleErrorMessage);
          } else if (error.status === 504) {
            this.callApiForCheckingValidateToken().subscribe();
          } else if (error.status === 401) {
            this.alertService.showErrorToast(error.Message);
            this.qeAnalyticsGatheringService.onQEUserLogoutSessionExpiredEmit().finally(() => {
              localStorage.clear();
              this.router.navigateByUrl('/auth/login');
            });
          } else if (error.status === 400 || error.status === 404 || error.status === 500) {
            if (error.status === 500 && error.url.search('downloadPdfReport') > 1) {
              this.alertService.showErrorToast(ErrorMessages.downloadErrorMessage);
            } else if (error.status === 404 && error.url.search('exportAlertReport') > 1) {
              this.alertService.showErrorToast(ErrorMessages.noRecordMessage);
            } else if (error.error.status === 405) {
              this.alertService.showErrorToast(error.error.message);
            } else if (error.error.status === 409) {
              if (!(error.url.includes('enableDisableTwoFactor') && !error.error.message)) {
                this.alertService.showErrorToast(error.error.message);
              }
            } else if (error.error.status === 500) {
              this.alertService.showErrorToast(error.error.message);
            } else if (error.status === 400 && error.url.search('ActiveInActiveQESTForm') > 1) {
              this.alertService.showErrorToast(error.error.message);
            } else if (error.status === 400) {
              this.alertService.showErrorToast(error.error.message);
            } else {
              this.alertService.showErrorToast(ErrorMessages.badRequest);
            }
          } else if (error.status === 400 && (error.url.search('downloadPdfReport') > 1 || error.url.search('downloadPptReport') > 1)) {
            this.alertService.showErrorToast(ErrorMessages.downloadErrorMessage);
          }
          // reset password messge
          else if (error.status === 400) {
            if (error.email) {
              this.alertService.showErrorToast(error.email[0]);
            } else {
              this.alertService.showErrorToast(error.message);
            }
          } else {
            /*
                Generic error from backend
                {
                    private message: string;
                    private applicationStatusCode: number;
                    private httpStatus: number;
                }
              */
            this.alertService.showErrorToast(error.message);
          }
        }
        return throwError(error);
      })
    );
  }

  private requestSuccess(res) {
    return res;
  }
}
