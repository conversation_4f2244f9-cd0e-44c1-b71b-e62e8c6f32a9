<div class="alert-box" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <h4 class="modal-title">Non-Conformance Ticket Generator</h4>

    <button type="button" class="close" aria-label="Close" (click)="onClose.next(isCreated); _bsModalRef.hide()">
      <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
    </button>
  </div>
  <div class="modal-body">
    <div class="form-control-group mt-3">
      <div id="fixed-table" class="table-responsive">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th class="text-center nonConformancetable" *ngIf="!isCreated" id="select">
                <nb-checkbox
                  id="select-all-{{ selectednc }}"
                  class="sfl-track-checkbox"
                  [(ngModel)]="isMasterSel"
                  (change)="selectDeselectAll()"
                  [disabled]="isSelectAllAllowed"
                  name="selectAllSites"
                >
                </nb-checkbox>
              </th>
              <th class="text-center nonConformancetable" *ngIf="isCreated" id="select">Ticket Number</th>
              <th class="text-center nonConformancetable" id="item">Item #</th>
              <th class="nonConformancetable" id="component">Component</th>
              <th class="nonConformancetable" id="location-indentity">Location/Device Name</th>
              <th class="nonConformancetable" id="issuses-observation">Issues/Observation</th>
              <th class="nonConformancetable" id="recommended">Actions/Recommendation</th>
              <th class="nonConformancetable" id="ticket-type">Ticket Type</th>
              <th class="nonConformancetable" id="Priority">Priority</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let ncItem of nonConformances; let i = index">
              <td class="nonConformancetable col-sm text-center" *ngIf="!isCreated">
                <nb-checkbox
                  class="sfl-track-checkbox"
                  id="select + {{ ncItem.ncGuid }}"
                  [checked]="ncItem.isSelected"
                  (change)="singleNCCheckChanged(ncItem)"
                  [(ngModel)]="ncItem.isSelected"
                  [disabled]="ncItem.ticketNumber"
                ></nb-checkbox>
              </td>
              <td class="text-center nonConformancetable col-sm" *ngIf="isCreated">
                <span *ngIf="!ncItem?.ticketNumber && !ncItem?.isSelected"> - </span>
                <span *ngIf="!ncItem?.ticketNumber && ncItem?.isSelected">Failed to Create</span>
                <a
                  *ngIf="ncItem?.ticketNumber"
                  class="pointerTicketNumberLink"
                  [routerLink]="['/entities/ticket/detail/view/' + ncItem?.ticketNumber]"
                >
                  {{ ncItem?.ticketNumber }}
                </a>
              </td>
              <td class="text-center nonConformancetable col-sm">
                {{ ncItem?.order }}
              </td>
              <td class="nonConformancetable col-md">
                {{ ncItem?.componentStr }}
              </td>
              <td class="nonConformancetable col-md">
                {{ ncItem?.location }}
              </td>
              <td class="nonConformancetable col-md">
                <div class="d-flex align-items-center justify-content-between" *ngIf="!ncItem?.isIssueEditable && !isCreated">
                  <span
                    *ngIf="assessmentType === 'SV' ? ncItem?.otherIssue : ncItem?.issue"
                    [nbTooltip]="assessmentType === 'SV' ? ncItem?.otherIssue : ncItem?.issue"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                  >
                    <sfl-read-more [content]="assessmentType === 'SV' ? ncItem?.otherIssue : ncItem?.issue"></sfl-read-more>
                  </span>
                  <a
                    class="listgrid_icons ms-1 text-primary"
                    [ngStyle]="ncItem.ticketNumber ? { 'pointer-events': 'none', opacity: '0.5' } : {}"
                  >
                    <em
                      *ngIf="!ncItem?.isIssueEditable"
                      class="fa fa-edit"
                      id="issueNcItem + {{ ncItem.ncGuid }}"
                      nbTooltip="Edit"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      (click)="ncItem.isIssueEditable = true"
                    ></em>
                  </a>
                </div>
                <div *ngIf="ncItem?.isIssueEditable && !isCreated">
                  <div class="mb-2">
                    <ng-select
                      *ngIf="assessmentType === 'SV'"
                      class="model-dd"
                      id="issuesObservation + {{ ncItem.ncGuid }}"
                      name="issuesObservation"
                      [items]="ncItem.issueList"
                      bindLabel="issueObservation"
                      bindValue="issueObservation"
                      [(ngModel)]="ncItem.issue"
                      (change)="isNCItemUpdated = true; updateActionListBasedOnIssue(ncItem)"
                      #issuesObservation="ngModel"
                      notFoundText="No Issues/Observations Found"
                      placeholder="Select Issue/Observation"
                      [clearable]="true"
                      appendTo="body"
                    >
                    </ng-select>
                  </div>
                  <div *ngIf="ncItem.issue === 'Other' && assessmentType === 'SV'">
                    <input
                      nbInput
                      id="issueNcItem + {{ ncItem.ncGuid }}"
                      name="issueNcItem"
                      #issueNcItem="ngModel"
                      [(ngModel)]="ncItem.otherIssue"
                      placeholder="Issue"
                      (ngModelChange)="isNCItemUpdated = true"
                      maxlength="128"
                      fullWidth
                    />
                  </div>
                  <div *ngIf="assessmentType !== 'SV'">
                    <input
                      nbInput
                      id="issueNcItem + {{ ncItem.ncGuid }}"
                      name="issueNcItem"
                      #issueNcItem="ngModel"
                      [(ngModel)]="ncItem.issue"
                      placeholder="Issue"
                      (ngModelChange)="isNCItemUpdated = true"
                      maxlength="128"
                      fullWidth
                    />
                  </div>
                </div>
                <div *ngIf="isCreated">
                  <span>{{ assessmentType === 'SV' ? ncItem?.otherIssue : ncItem?.issue }}</span>
                </div>
              </td>
              <td class="nonConformancetable col-md">
                <div class="d-flex align-items-center justify-content-between" *ngIf="!ncItem?.isActionEditable && !isCreated">
                  <span
                    *ngIf="assessmentType === 'SV' ? ncItem?.otherAction : ncItem?.actions"
                    [nbTooltip]="assessmentType === 'SV' ? ncItem?.otherAction : ncItem?.actions"
                    nbTooltipPlacement="top"
                    nbTooltipStatus="primary"
                  >
                    <sfl-read-more [content]="assessmentType === 'SV' ? ncItem?.otherAction : ncItem?.actions"></sfl-read-more>
                  </span>
                  <a
                    class="listgrid_icons ms-1 text-primary"
                    [ngStyle]="ncItem.ticketNumber ? { 'pointer-events': 'none', opacity: '0.5' } : {}"
                  >
                    <em
                      *ngIf="!ncItem?.isActionEditable"
                      class="fa fa-edit"
                      id="actionsNcItemIcon + {{ ncItem.ncGuid }}"
                      nbTooltip="Edit"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      (click)="ncItem.isActionEditable = true"
                    ></em>
                  </a>
                </div>
                <div *ngIf="ncItem?.isActionEditable && !isCreated">
                  <div class="mb-2">
                    <ng-select
                      *ngIf="assessmentType === 'SV'"
                      id="input-actions"
                      class="model-dd"
                      name="actionsRecommendation"
                      #actionsRecommendation="ngModel"
                      [items]="ncItem.actionsList"
                      (change)="isNCItemUpdated = true"
                      bindLabel="actionRecommendation"
                      bindValue="actionRecommendation"
                      [(ngModel)]="ncItem.actions"
                      notFoundText="No Actions Found"
                      placeholder="Select Action/Recommendation"
                      [clearable]="true"
                      appendTo="body"
                      required
                    >
                    </ng-select>
                  </div>
                  <div *ngIf="ncItem.actions === 'Other'">
                    <input
                      nbInput
                      id="actionsNcItemInput + {{ ncItem.ncGuid }}"
                      name="issueNcItem"
                      #issueNcItem="ngModel"
                      [(ngModel)]="ncItem.otherAction"
                      placeholder="Issue"
                      (ngModelChange)="isNCItemUpdated = true"
                      maxlength="128"
                      fullWidth
                    />
                  </div>
                  <div *ngIf="assessmentType !== 'SV'">
                    <input
                      nbInput
                      id="actionsNcItemInput + {{ ncItem.ncGuid }}"
                      name="issueNcItem"
                      #issueNcItem="ngModel"
                      [(ngModel)]="ncItem.actions"
                      placeholder="Issue"
                      (ngModelChange)="isNCItemUpdated = true"
                      maxlength="128"
                      fullWidth
                    />
                  </div>
                </div>
                <div *ngIf="isCreated">
                  <span>{{ assessmentType === 'SV' ? ncItem?.otherAction : ncItem?.actions }}</span>
                </div>
              </td>
              <td class="nonConformancetable ticket-type">
                <div>
                  <ng-select
                    name="ticketType"
                    [items]="ticketTypeList"
                    bindLabel="name"
                    bindValue="id"
                    #ticketType="ngModel"
                    [(ngModel)]="ncItem.ticketType"
                    notFoundText="No Ticket Type Found"
                    placeholder="Select Ticket Type"
                    appendTo="body"
                    [disabled]="isCreated || ncItem.ticketNumber"
                    [clearable]="false"
                  >
                  </ng-select>
                </div>
              </td>
              <td class="nonConformancetable ticket-priority">
                <div>
                  <ng-select
                    name="priority"
                    [items]="priorityList"
                    bindLabel="name"
                    bindValue="id"
                    #priority="ngModel"
                    [(ngModel)]="ncItem.priority"
                    notFoundText="No Priority Found"
                    placeholder="Select Priority"
                    appendTo="body"
                    [disabled]="isCreated || ncItem.ticketNumber"
                    [clearable]="false"
                  >
                  </ng-select>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button nbButton status="basic" size="medium" (click)="onCancel(cancelConfirmModelTemplate)" *ngIf="!isCreated">Cancel</button>
    <button nbButton status="primary" size="medium" (click)="onCreateBulkTickets()" [disabled]="!selectedBulkNC.length" *ngIf="!isCreated">
      Create
    </button>
    <button nbButton status="primary" size="medium" (click)="onClose.next(isCreated); _bsModalRef.hide()" *ngIf="isCreated">Close</button>
  </div>
</div>

<ng-template #cancelConfirmModelTemplate>
  <div class="alert-box">
    <div class="modal-header">
      <h6 class="modal-title ModalBody">Confirm</h6>
      <button type="button" class="close" aria-label="Close" (click)="modalRef.hide(); _bsModalRef.setClass('d-block modal-xl')">
        <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
      </button>
    </div>
    <div class="modal-body ModalBody text-center">
      <h5 class="mb-4">Are you sure you want to cancel?</h5>
      <p><strong>Note:</strong> You can generate tickets later in the Work Order.</p>
    </div>
    <div class="modal-footer ModalFooter">
      <button nbButton status="primary" size="small" (click)="closeModel()">Ok</button>
      <button nbButton status="primary" size="small" (click)="modalRef.hide(); _bsModalRef.setClass('d-block modal-xl')">Back</button>
    </div>
  </div>
</ng-template>
