import { AppConstants } from '../constants/app.constant';

export class Customer {
  public id: number;
  public customerName: string;
  public totalSites: number;
  public sumDCSize: number;
  public isAvailability = false;
  public isAutoGeneratePR = false;
  public prGenerateWorkDays: number;
  public taskDetails: TaskDetails = new TaskDetails();
  public truckRoleBaseOnFieldTech = false;
  public listOfAvailabilityExclusionType: ExclusionType[] = [];
  public customerAvailabilityDetail: CustomerAvailabilityModel = new CustomerAvailabilityModel();
  public outageSetting?: CustomerOutageResponse;
  public isCustomerUserSelected?: boolean;
  public customerKey?: string;
  public isActive: boolean;
  public isArchive: boolean;
  public archiveUpdatedData = [];
}
export class CustomerOutageResponse {
  public customerId: number;
  public isParentSetting: boolean;
  public outageId: number;
  public parentSettingId: number;
  public portfolioId: number;
  public siteId: number;
  public powerThreshold: number;
  public settingType: number;
  public zeroGeneration: boolean;
  public triggerCount: number;
  public timeSetting: CustomerOutageTimeSetting[];
  public portfoliosSites: OutagePortfolioSitesSetting[];
}

export class CustomerArchiveResponse {
  public id: number;
  public name: string;
  public lastArchive: Date;
  public lastActive: Date;
  public siteData: SiteData[];
}

export class SiteData {
  public id: number;
  public name: string;
  public lastArchive: Date;
  public lastActive: Date;
  public siteDeviceData: SiteDeviceData[];
}

export class SiteDeviceData {
  public id: number;
  public name: string;
  public lastArchive: Date;
  public lastActive: Date;
}

export enum Month {
  JANUARY = 'January',
  FEBRUARY = 'February',
  MARCH = 'March',
  APRIL = 'April',
  MAY = 'May',
  JUNE = 'June',
  JULY = 'July',
  AUGUST = 'August',
  SEPTEMBER = 'September',
  OCTOBER = 'October',
  NOVEMBER = 'November',
  DECEMBER = 'December'
}

export class CustomerOutageTimeSetting {
  public month: number;
  public startTime: string;
  public endTime: string;
}
export class OutagePortfolioSitesSetting {
  public id: number;
  public isParentSetting: boolean;
  public name: string;
  public zeroGeneration: boolean;
  public sites: OutageSitesSetting[];
}
export class OutageSitesSetting {
  public id: number;
  public isParentSetting: boolean;
  public siteName: string;
  public zeroGeneration: boolean;
}

export class CustomerFilterData {
  public totalCustomer: number;
  public customers: Customer[];
}

export class TaskDetails {
  public siteVisit: number;
  public electricalIV: number;
  public electricalVOC: number;
  public thermal: number;
  public aerialScan: number;
  public inverterPM: number;
  public mvpm: number;
  public vegetation: number;
  public monitoing: number;
  public performanceReporting: number;
  public jha: number;
  public tpm: number;
  public trq: number;
  public mvth: number;
}
export class CustomerAvailabilityModel {
  public id: number;
  public operatingInterval = 2; //1 Means 15 min, 2 means 1 hour
  public irradianceThreshold: number;
  public powerThreshold: number;
  public gracePeriod: number;
  public availabilityGuarantee: number;
  public isAutoGracePeriodExclusion = false;
  public customerId: number;
}

export class CustomerFilter {
  public search: string;
  public page = 0;
  public sortBy = 'CustomerName';
  public direction = 'asc';
  public itemsCount = +AppConstants.rowsPerPage;
}

export class ExclusionType {
  public id = 0;
  public exclusionType: string;
  public exclusionIndex: number;
  public isActive = true;
}

export const CustomerFilterList = {
  search: 'Search'
};

export class PRGenerateLogs {
  id: number;
  requestDate: string;
  requestedBy: number;
  requestedUser: string;
  totalRecords: number;
  processRecords: number;
  pendingRecords: number;
  isAutoRequest: boolean;
  isManualRequest: boolean;
}
