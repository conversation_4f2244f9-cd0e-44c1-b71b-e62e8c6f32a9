import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { QE_MENU_ENUMS } from '../../@shared/constants';
import { ROLE_TYPE } from '../../@shared/enums';
import { QEMenuConfigs } from '../../@shared/models/header-menu.model';
import { ModeWiseGuard } from '../../@shared/services/mode-wise.guard';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { SiteAddEditComponent } from './site-add-edit/site-add-edit.component';
import { SiteListingComponent } from './site-listing/site-listing.component';

const routes: Routes = [
  {
    path: '',
    component: SiteListingComponent,
    data: { pageTitle: 'Sites' }
  },
  {
    path: 'add',
    component: SiteAddEditComponent,
    canActivate: [PermissionGuard],
    data: {
      permittedRoles: [
        ROLE_TYPE.ADMIN,
        ROLE_TYPE.DIRECTOR,
        ROLE_TYPE.PORTFOLIOMANAGER,
        ROLE_TYPE.MANAGER,
        ROLE_TYPE.SUPPORT,
        ROLE_TYPE.FIELDTECH,
        ROLE_TYPE.ANALYST
      ],
      pageTitle: 'Add Site'
    }
  },
  {
    path: ':mode/:id',
    component: SiteAddEditComponent,
    canActivate: [ModeWiseGuard],
    data: {
      edit: [
        ROLE_TYPE.ADMIN,
        ROLE_TYPE.DIRECTOR,
        ROLE_TYPE.PORTFOLIOMANAGER,
        ROLE_TYPE.MANAGER,
        ROLE_TYPE.SUPPORT,
        ROLE_TYPE.FIELDTECH,
        ROLE_TYPE.ANALYST
      ],
      detail: [
        ROLE_TYPE.ADMIN,
        ROLE_TYPE.DIRECTOR,
        ROLE_TYPE.PORTFOLIOMANAGER,
        ROLE_TYPE.MANAGER,
        ROLE_TYPE.SUPPORT,
        ROLE_TYPE.FIELDTECH,
        ROLE_TYPE.ANALYST,
        ROLE_TYPE.CUSTOMER
      ],
      view: [
        ROLE_TYPE.ADMIN,
        ROLE_TYPE.DIRECTOR,
        ROLE_TYPE.PORTFOLIOMANAGER,
        ROLE_TYPE.MANAGER,
        ROLE_TYPE.SUPPORT,
        ROLE_TYPE.FIELDTECH,
        ROLE_TYPE.ANALYST,
        ROLE_TYPE.CUSTOMER
      ],
      pageTitle: 'Update Site',
      qeMenuConfigs: new QEMenuConfigs(QE_MENU_ENUMS.JPT_SI_SITES, true)
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SiteManagementRoutingModule {}
