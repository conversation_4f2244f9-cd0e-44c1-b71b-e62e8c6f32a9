export class Dropdown {
  public id: number;
  public name: string;
  public userRoleId?: number;
  public isArchive?: boolean;
  public isActive?: boolean;
  public siteNumber?: number;
  public riskLevel?: number;
  public riskLevelStr?: string;
  public controlBarriers?: string;
  public protectiveBarriers?: string;
  public supportBarriers?: string;
  public barriersIds?: number[];
  public mfgName?: string;
  public customerId?: string;
  public portfolioId?: string;
}

export class MultiSelectDropdown {
  constructor(id: number, text: string) {
    this.item_id = id;
    this.item_text = text;
  }

  public item_id: number;
  public item_text: string;
}

export class GroupByDropdown {
  public id: number;
  public name: string;
  public type: string;
}
export class TemplateTypeDropdown {
  public templateTypeId: number;
  public templateTypeName: string;
}

export class CommonResponse {
  public entryid: number;
  public id: number;
  public message: string;
  public status: number;
}
