.modal-dialog-right {
  position: fixed;
  margin: auto;
  width: 600px;
  right: 0px;
  overflow-y: auto;
  height: 100%;
}

@media (max-width: 700px) {
  .modal-dialog-right {
    width: 80%;
  }
}

.modal-content {
  height: 100%;
}

.modal-body {
  max-height: calc(100vh - 150px);
  overflow-y: auto;
}

.cdk-global-overlay-wrapper,
.cdk-overlay-container {
  z-index: 99999 !important;
}

.text-reso-comment {
  color: #515151 !important;
}

.pointer {
  cursor: pointer;
}

.pointerTicketNumberLink {
  color: rgb(89, 139, 255) !important;
  cursor: pointer !important;
}

.input-error {
  color: #ff0035;
  float: left;
}
.form-control.ng-invalid {
  border: 1px solid#FF0035;
}
.f-s-13 {
  font-size: 13px;
}

.f-s-12 {
  font-size: 12px;
}

.device-outage-accordion {
  box-shadow: 0 0 1rem 0 #1a1f33 !important;
}

.fault-code-tag-list {
  max-height: 72px;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-wrap: wrap;
  padding-inline: 0.5rem !important;

  nb-tag {
    padding-left: 0.25rem !important;
    border-radius: 2px !important;
  }
}
