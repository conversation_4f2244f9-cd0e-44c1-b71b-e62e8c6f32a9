import { AppConstants } from '../constants';
import { QE_MENU_MODULE_ENUM } from '../enums';

export class Dashboard {
  public customerCount: number;
  public portfolioCount: number;
  public siteCount: number;
  public completed: number;
  public notyet: number;
  public onSite: number;
  public partially: number;
  public late: number;
  public totalCount: number;
  public assesmentDashDtos: AssessmentDashDTOS[];
  public chartDataList: ChartDataList[];
  public clonedChartDataList: ChartDataList[];
}

export class ChartDataList {
  sectionName: string;
  sectionId: number;
  chartDetails: ChartDataDTO[] | ChartConfig[];
  reportSummary: ReportSummary[];
}

export class ChartDataDTO {
  public name: string;
  public data: ChartData[];
}

export class ChartData {
  public name: string;
  public value: number;
}

export class ReportSummary {
  name: string;
  value: number;
  label: string;
  labelTextCss: string;
  cardStripCss: string;
  order: number;
}

export interface ChartConfig {
  tooltip: {
    trigger: string;
    position: string;
    formatter?: string;
    backgroundColor: string;
    borderWidth: number;
    axisPointer: {
      type: string;
      crossStyle: {
        color: string;
      };
    };
    textStyle: {
      color: string;
      fontSize: number;
    };
  };
  color: string[];
  grid: {
    y: number;
  };
  series: {
    name: string;
    type: string;
    radius: string[];
    avoidLabelOverlap: boolean;
    label: {
      color: string;
      fontSize: string;
      position: string;
      formatter: () => string;
    };
    tooltip: {
      valueFormatter: (value: any) => string;
    };
    labelLine: {
      show: boolean;
    };
    data: any[];
  }[];
  isCreateChart?: boolean;
  chartName: string;
}

export class ExportDashboard {
  public asWoCount: number;
  public customerPortfolio: string;
  public id: number;
  public ipmWoCount: number;
  public ivWoCount: number;
  public kWdc: number;
  public mvpmWoCount: number;
  public prWoCount: number;
  public siteName: string;
  public state: string;
  public svWoCount: number;
  public thermWoCount: number;
  public tpmWoCount: number;
  public trqWoCount: number;
  public mvthWoCount: number;
  public vgtWoCount: number;
  public vocWoCount: number;
}

export class ExportScheduleDashboard {
  public id: number;
  public customerName: string;
  public portfolioName: string;
  public siteName: string;
  public assessmentType: string;
  public workOrderNumber: string;
  public woStatus: string;
  public tentativeMonth: string;
  public scheduledDate: string;
  public rescheduledDate: string;
  public dueDate: string;
  public datePerformed: string;
  public fieldTech: string;
  public siteId: number;
  public isActive: boolean;
  public startDate: string;
  public address: string;
  public city: string;
  public state: string;
  public zipCode: string;
  public latitude: number;
  public logitude: number;
  public dcSize: number;
  public acSize: number;
  public numInvs: number;
  public numXFMRs: number;
  public utilityOwnedXFMR: boolean;
  public numberofCombiners: string;
  public numberofModules: string;
  public numberofPanelboards: string;
  public ladderRequired: boolean;
  public liftRequired: boolean;
  public noticeRequired: boolean;
  public pmRequiresTwoTechnicians: boolean;
  public weekendWorkAllowed: boolean;
  public isRoofSite: boolean;
  public isCarportSite: boolean;
  public isGroundFixedSite: boolean;
  public isSingleTrackerSite: boolean;
  public isDualTrackerSite: boolean;
  public isFloatingSite: boolean;
  public isCentralInvSite: boolean;
  public isStringInvSite: boolean;
  public isMicroInvSite: boolean;
  public primaryInvMFG: string;
  public primaryInvCount: number;
  public secondaryInvMFG: string;
  public secondaryInvCount: number;
  public tertiaryInvMFG: string;
  public tertiaryInvCount: number;
  public addOnCost: number;
  public cost: number;
  public notes: string;
  public assessmentId: number;
  public poNumber: string;
  public invoiceNumber: string;
}
export class WorkorderDetails {
  assementType: string;
  data: any;
  id: number;
  label: string;
  workOrderNumber: string;
}

export class AssessmentDashDTOS {
  public id: number;
  public portfolioId: number;
  public portfolioName: string;
  public siteId: number;
  public siteName: string;
  public state: string;
  public startYear: number;
  public siteVisit: [];
  public electricalIV: [];
  public electricalVOC: [];
  public thermal: [];
  public aerialScan: [];
  public inverterPM: [];
  public mvpm: [];
  public vegetation: [];
  public monitoing: [];
  public performanceReporting: [];
  public jha: [];
}

export class MenuDTOS {
  public title: string;
  public id: string;
  public icon: string;
  public route?: string;
  public hasPermission: boolean;
  public qeMenuKey: QE_MENU_MODULE_ENUM;
  public subMenu: ChildMenuDTOS[];
  public defaultRoute: string;
}

export class ChildMenuDTOS {
  public title: string;
  public id: string;
  public icon?: string;
  public route: string;
  public hasPermission: boolean;
  public qeMenuKey: QE_MENU_MODULE_ENUM;
  public queryParams?: any;
  public subMenu: ChildMenuDTOS[];
}

export class DashboardFilter {
  public customerId = null;
  public portfolioId = null;
  public year: number;
  public state = null;
  public reportType: number[] = [];
  public frequency: string[] = [];
  public reportStatus: number = null;
  public woStatus: number = null;
  public arrayType: number[] = [];
  public page: number = 0;
  public sortBy: string = 'SiteName';
  public direction: string = 'asc';
  public itemsCount: number = +AppConstants.rowsPerPage;
}

export const DashboardFilterList = {
  customer: 'Customer',
  portfolio: 'Portfolio',
  state: 'State',
  year: 'Year',
  reportType: 'ReportType',
  reportStatus: 'ReportStatus',
  woStatus: 'WorkOrderStatus',
  frequency: 'FrequencyType',
  arrayType: 'Array Type'
};
