import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiUrl } from '../../../@shared/constants';
import { GetNotificationByIdResponse, NotificationItem, NotificationListResponse, NotificationType } from './notification.model';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  constructor(private readonly http: HttpClient) {}

  getAllNotificationByFilter(obj): Observable<NotificationListResponse> {
    return this.http.post<NotificationListResponse>(ApiUrl.GET_NOTIFICATION_BY_FILTER, obj);
  }

  getNotificationById(id: number): Observable<GetNotificationByIdResponse> {
    return this.http.get<GetNotificationByIdResponse>(`${ApiUrl.NOTIFICATION}/${id}`);
  }

  getNotificationTypes(): Observable<NotificationType[]> {
    return this.http.get<NotificationType[]>(ApiUrl.GET_NOTIFICATION_TYPES);
  }

  getDropdownOptions(): Observable<any> {
    return this.http.get<any>(ApiUrl.DROPDOWN_OPTIONS);
  }

  createNotification(notification: NotificationItem): Observable<any> {
    return this.http.post(ApiUrl.NOTIFICATION, notification);
  }

  updateNotification(notification: NotificationItem): Observable<any> {
    return this.http.put(ApiUrl.NOTIFICATION, notification);
  }
}
