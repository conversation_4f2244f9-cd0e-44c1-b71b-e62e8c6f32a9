import { QE_MENU_ENUMS } from '../constants';
import { MenuDTOS } from './dashboard.model';

export class QEMenuConfigs {
  isVisible: boolean;
  qeMenuEnum: QE_MENU_ENUMS;

  constructor(qeMenuEnum: QE_MENU_ENUMS, isVisible: boolean) {
    this.qeMenuEnum = qeMenuEnum;
    this.isVisible = isVisible;
  }
}

export class JumpToMenuConfig {
  isVisible: boolean;
  jumpToMenuList: MenuDTOS[];
  search: string;
  filteredJumpToMenuList: MenuDTOS[];
  disable: boolean;
  qeMenuEnum: QE_MENU_ENUMS;
  constructor(qeMenuEnum: QE_MENU_ENUMS = QE_MENU_ENUMS.QE_NO_MENU, jumpToMenuList: MenuDTOS[] = [], isVisible: boolean = false) {
    this.qeMenuEnum = qeMenuEnum;
    this.jumpToMenuList = jumpToMenuList;
    this.isVisible = isVisible;
    this.filteredJumpToMenuList = jumpToMenuList;
    this.search = '';
    this.disable = false;
  }
}
