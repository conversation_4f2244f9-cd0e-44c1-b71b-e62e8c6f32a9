<nb-card class="ticket-detail ticketDetailSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header class="d-flex align-items-center">
    <h6 class="w-100" *ngIf="isCreate">Add Ticket</h6>
    <div class="d-flex align-items-center w-100">
      <h6 *ngIf="isDetail || isEdit">{{ ticketDetailModel?.ticketNumber }}</h6>
      <div class="ms-auto d-flex button_list">
        <div *ngIf="isCreate || isEdit">
          <button
            nbButton
            status="primary"
            size="medium"
            type="button"
            id="siteSubmit"
            (click)="isSendEmail = true; ticketForm.onSubmit()"
            class="float-end ms-1 ms-sm-2"
          >
            <span class="d-none d-lg-inline-block">Save & Send Email</span>
            <i class="d-inline-block d-lg-none fa fa-envelope-o"></i>
          </button>
          <button
            nbButton
            status="primary"
            size="medium"
            type="button"
            id="siteSubmit"
            (click)="isSendEmail = false; ticketForm.onSubmit()"
            class="float-end ms-1 ms-sm-2"
          >
            <span class="d-none d-lg-inline-block">Save</span>
            <i class="d-inline-block d-lg-none fa fa-floppy-o"></i>
          </button>
          <button
            *ngIf="isEdit"
            nbButton
            status="primary"
            size="medium"
            type="button"
            id="DropBoxImageGallery"
            (click)="openDropBoxImageGallery(null)"
            [disabled]="ticketDetailModel.imgCount === 0"
          >
            <span class="d-none d-lg-inline-block"
              ><em class="pi pi-images me-2 fs-14"></em>GALLERY ({{ ticketDetailModel.imgCount || 0 }})</span
            >
            <em class="d-inline-block d-lg-none pi pi-images"></em>
          </button>
          <button
            nbButton
            status="danger"
            size="medium"
            type="button"
            id="delete"
            class="ms-2"
            (click)="openBulkBulkCloseReopenModal()"
            *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && isEdit && ticketDetailModel.status !== 3"
          >
            <span class="d-none d-lg-inline-block">Close & Reopen</span>
            <i class="d-inline-block d-lg-none fa-solid fa-square-up-right"></i>
          </button>
          <button
            *ngIf="isCreate"
            nbButton
            status="basic"
            type="button"
            (click)="removeIsPerviousPageTicketList()"
            routerLink="/entities/ticket"
            size="medium"
            class="float-end"
          >
            <span class="d-none d-lg-inline-block">Cancel</span>
            <i class="d-inline-block d-lg-none fa fa-times-circle"></i>
          </button>
        </div>
        <div *ngIf="isDetail || isEdit">
          <button
            *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && isDetail"
            nbButton
            status="primary"
            size="medium"
            type="button"
            id="DropBoxImageGallery"
            class="me-2"
            (click)="openDropBoxImageGallery(null)"
            [disabled]="ticketDetailModel.imgCount === 0"
          >
            <span class="d-none d-lg-inline-block"
              ><em class="pi pi-images me-2 fs-14"></em>GALLERY ({{ ticketDetailModel.imgCount || 0 }})</span
            >
            <em class="d-inline-block d-lg-none pi pi-images"></em>
          </button>
          <button
            nbButton
            status="danger"
            size="medium"
            type="button"
            id="delete"
            class="me-2"
            (click)="openBulkBulkCloseReopenModal()"
            *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && isDetail && ticketDetailModel.status !== 3"
          >
            <span class="d-none d-lg-inline-block">Close & Reopen</span>
            <i class="d-inline-block d-lg-none fa-solid fa-square-up-right"></i>
          </button>
          <button
            *ngIf="isDetail && !checkAuthorisationsFn([roleType.CUSTOMER])"
            nbButton
            status="primary"
            size="medium"
            type="button"
            id="editTicket"
            (click)="isEdit = true; isDetail = false; fromDetailToEdit = true; getALlListsForUpdate(true)"
          >
            <span class="d-none d-lg-inline-block">Edit</span>
            <i class="d-inline-block d-lg-none fa fa-pencil"></i>
          </button>
          <button
            nbButton
            status="primary"
            [ngClass]="{ activeRMA: hasActiveRMA() }"
            size="medium"
            type="button"
            id="workLog"
            class="ms-1 ms-sm-2"
            *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && isDetail"
            (click)="openRmaModel('create', -1, true)"
          >
            <span class="d-none d-lg-inline-block">{{ hasActiveRMA() ? 'RMA' : 'Add RMA' }}</span>
            <i class="d-inline-block d-lg-none fa fa-retweet"></i>
          </button>
          <button
            nbButton
            status="primary"
            size="medium"
            type="button"
            id="workLog"
            class="ms-1 ms-sm-2"
            (click)="onAddComment(item, false)"
          >
            <span class="d-none d-lg-inline-block">Add Comment</span>
            <i class="d-inline-block d-lg-none fa fa-comment"></i>
          </button>
          <button
            nbButton
            status="primary"
            size="medium"
            type="button"
            id="workLog"
            class="ms-1 ms-sm-2"
            *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
            (click)="addActivityLog(item, false)"
          >
            <span class="d-none d-lg-inline-block">Add Activity</span>
            <i class="d-inline-block d-lg-none fa fa-calendar"></i>
          </button>
          <button nbButton status="primary" size="medium" type="button" id="download" class="ms-1 ms-sm-2" (click)="downloadTicketPDF()">
            <span class="d-none d-lg-inline-block">Export PDF</span>
            <i class="d-inline-block d-lg-none fa fa-file-pdf-o"></i>
          </button>
          <button
            nbButton
            status="danger"
            size="medium"
            type="button"
            id="delete"
            class="ms-1 ms-sm-2"
            (click)="deleteTicket()"
            *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && ticketDetailModel.status !== 3"
          >
            <span class="d-none d-lg-inline-block">Delete</span>
            <i class="d-inline-block d-lg-none fa fa-trash"></i>
          </button>
          <button nbButton status="basic" type="button" size="medium" (click)="onBack()" class="ms-1 ms-sm-2">
            <span class="d-none d-lg-inline-block">Back</span> <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <form
      name="siteForm"
      #ticketForm="ngForm"
      aria-labelledby="title"
      autocomplete="off"
      (ngSubmit)="removeSelectedErrors(ticketForm); ticketForm?.form?.valid && saveTicket()"
    >
      <div class="row">
        <div class="col-12">
          <div class="form-group row">
            <div class="col-12">
              <div class="borderBottom">
                <div class="row">
                  <div class="col-12 col-sm-6 borderRight pb-3">
                    <nb-accordion [ngClass]="{ 'h-100': siteInformation }">
                      <nb-accordion-item
                        [expanded]="siteInformation"
                        (collapsedChange)="accordionChange($event, 'siteInformation')"
                        class="border-bottom"
                      >
                        <nb-accordion-item-header class="accordion_head"> Site Information </nb-accordion-item-header>
                        <nb-accordion-item-body>
                          <div class="row">
                            <div class="col-12 col-sm-6 mb-3">
                              <label class="label" for="input-Customer"
                                >Customer<span *ngIf="isCreate || isEdit" class="ms-1 text-danger">*</span></label
                              >
                              <span>
                                <a *ngIf="isDetail" [routerLink]="['/entities/customers/edit/' + ticketDetailModel?.customerId]">
                                  {{ ticketDetailModel.customerName }}
                                </a>
                              </span>
                              <div *ngIf="isCreate || isEdit">
                                <ng-select
                                  name="customer"
                                  [items]="customerList"
                                  (change)="onCustomerSelect($event)"
                                  (clear)="onCustomerDeSelect()"
                                  bindLabel="name"
                                  bindValue="id"
                                  #customer="ngModel"
                                  [(ngModel)]="ticketModel.customerId"
                                  notFoundText="No Customer Found"
                                  placeholder="Select Customer"
                                  appendTo="body"
                                  [clearable]="false"
                                  required
                                >
                                </ng-select>
                                <sfl-error-msg
                                  [control]="customer"
                                  [isFormSubmitted]="ticketForm?.submitted"
                                  fieldName="Customer"
                                ></sfl-error-msg>
                              </div>
                            </div>
                            <div class="col-12 col-sm-6 mb-3">
                              <label class="label" for="input-Portfolio"
                                >Portfolio<span *ngIf="isCreate || isEdit" class="ms-1 text-danger">*</span></label
                              >
                              <span *ngIf="isDetail && !checkAuthorisationsFn([roleType.CUSTOMER])">
                                <a [routerLink]="['/entities/portfolios/edit/' + ticketDetailModel?.portfolioId]">
                                  {{ ticketDetailModel?.portfolioName }}
                                </a>
                              </span>
                              <span *ngIf="isDetail && checkAuthorisationsFn([roleType.CUSTOMER])">
                                {{ ticketDetailModel?.portfolioName }}
                              </span>
                              <div *ngIf="isCreate || isEdit">
                                <ng-select
                                  name="portfolio"
                                  [items]="portfolioList"
                                  (change)="onPortfolioSelect($event)"
                                  (clear)="onPortfolioDeSelect()"
                                  bindLabel="name"
                                  bindValue="id"
                                  #portfolio="ngModel"
                                  [(ngModel)]="ticketModel.portfolioId"
                                  notFoundText="No Portfolio Found"
                                  placeholder="Select Portfolio"
                                  appendTo="body"
                                  [clearable]="false"
                                  required
                                >
                                </ng-select>
                                <sfl-error-msg
                                  [control]="portfolio"
                                  [isFormSubmitted]="ticketForm?.submitted"
                                  fieldName="Portfolio"
                                ></sfl-error-msg>
                              </div>
                            </div>
                            <div class="col-12 col-sm-6 mb-3 mb-sm-0">
                              <label class="label" for="input-name"
                                >Site<span *ngIf="isCreate || isEdit" class="ms-1 text-danger">*</span></label
                              >
                              <span *ngIf="isDetail && !checkAuthorisationsFn([roleType.CUSTOMER])">
                                <a [routerLink]="['/entities/sites/view/' + ticketDetailModel?.siteId]">
                                  {{ ticketDetailModel?.siteName }}
                                </a>
                              </span>
                              <span *ngIf="isDetail && checkAuthorisationsFn([roleType.CUSTOMER])">
                                {{ ticketDetailModel?.siteName }}
                              </span>
                              <div *ngIf="isCreate || isEdit">
                                <ng-select
                                  name="site"
                                  [items]="siteList"
                                  bindLabel="name"
                                  bindValue="id"
                                  (change)="onSiteSelect($event)"
                                  (clear)="onSiteDeSelect()"
                                  #site="ngModel"
                                  [(ngModel)]="ticketModel.siteId"
                                  notFoundText="No Site Found"
                                  placeholder="Select Site"
                                  appendTo="body"
                                  [clearable]="false"
                                  required
                                >
                                </ng-select>
                                <sfl-error-msg [control]="site" [isFormSubmitted]="ticketForm?.submitted" fieldName="Site"></sfl-error-msg>
                              </div>
                            </div>
                            <div class="col-12 col-sm-6">
                              <label class="label" for="input-name">Site ID</label>
                              <span *ngIf="isDetail">{{ ticketDetailModel?.qeSiteId || '-' }}</span>
                              <span *ngIf="isCreate || isEdit">{{ ticketModel?.qeSiteId || '-' }}</span>
                            </div>
                          </div>
                        </nb-accordion-item-body>
                      </nb-accordion-item>
                    </nb-accordion>
                  </div>
                  <div class="col-12 col-sm-6 pb-3">
                    <nb-accordion [ngClass]="{ 'h-100': ticketInformation }">
                      <nb-accordion-item
                        [expanded]="ticketInformation"
                        (collapsedChange)="accordionChange($event, 'ticketInformation')"
                        class="border-bottom"
                      >
                        <nb-accordion-item-header class="accordion_head"> Ticket Information </nb-accordion-item-header>
                        <nb-accordion-item-body>
                          <div class="row">
                            <div class="col-12 col-md-6 mb-3">
                              <label class="label" for="input-Status">Ticket Status</label>
                              <span *ngIf="isDetail">{{ getValue(ticketDetailModel?.status, ticketStatusList) }}</span>
                              <div *ngIf="isCreate || isEdit">
                                <ng-select
                                  name="ticketStatus"
                                  [items]="ticketStatusList"
                                  bindLabel="name"
                                  bindValue="id"
                                  #ticketStatus="ngModel"
                                  (change)="statusChange($event)"
                                  [(ngModel)]="ticketModel.status"
                                  notFoundText="No Ticket Status Found"
                                  placeholder="Select Ticket Status"
                                  appendTo="body"
                                  [clearable]="false"
                                >
                                </ng-select>
                              </div>
                            </div>
                            <div class="col-12 col-md-6 mb-3" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                              <label class="label" for="input-Status">Billing Status</label>
                              <span *ngIf="isDetail">
                                {{ getTicketStatusValue(ticketModel?.ticketBillingStatusID, ticketBillingStatuses, 'description') }}
                              </span>
                              <div *ngIf="ticketModel.status === 3 && !isDetail">
                                <ng-select
                                  name="selectedBillingStatus"
                                  [items]="ticketBillingStatuses"
                                  bindLabel="description"
                                  bindValue="ticketBillingStatusID"
                                  #selectedBillingStatus="ngModel"
                                  [(ngModel)]="ticketModel.ticketBillingStatusID"
                                  notFoundText="No Billing Status Found"
                                  placeholder="Select Billing Status"
                                  appendTo="body"
                                  [clearable]="false"
                                  (change)="updateTicketBillingStatus(ticketModel.ticketBillingStatusID)"
                                >
                                </ng-select>
                              </div>
                              <ng-container *ngIf="!ticketModel?.ticketBillingStatusID">-</ng-container>
                            </div>
                            <div class="col-12 col-md-6 mb-3">
                              <label class="label" for="input-Portfolio">Priority</label>
                              <span *ngIf="isDetail">
                                <img
                                  *ngIf="getValue(ticketDetailModel.priority, priorityList) === 'High'"
                                  nbTooltip="High"
                                  nbTooltipStatus="primary"
                                  src="assets/images/High.svg"
                                  alt="High"
                                />
                                <img
                                  alt="Medium"
                                  *ngIf="getValue(ticketDetailModel.priority, priorityList) === 'Medium'"
                                  nbTooltip="Medium"
                                  nbTooltipStatus="primary"
                                  src="assets/images/Medium.svg"
                                />
                                <img
                                  alt="Low"
                                  *ngIf="getValue(ticketDetailModel.priority, priorityList) === 'Low'"
                                  nbTooltip="Low"
                                  nbTooltipStatus="primary"
                                  src="assets/images/Low.svg"
                                />
                                <img
                                  alt="Safety"
                                  *ngIf="getValue(ticketDetailModel.priority, priorityList) === 'Safety'"
                                  nbTooltip="Safety"
                                  nbTooltipStatus="primary"
                                  src="assets/images/Safety.svg"
                                />
                              </span>
                              <span *ngIf="isDetail" class="ms-2">{{ getValue(ticketDetailModel?.priority, priorityList) }}</span>
                              <div *ngIf="isCreate || isEdit">
                                <ng-select
                                  name="priority"
                                  [items]="priorityList"
                                  bindLabel="name"
                                  bindValue="id"
                                  #priority="ngModel"
                                  [(ngModel)]="ticketModel.priority"
                                  notFoundText="No Priority Found"
                                  placeholder="Select Priority"
                                  appendTo="body"
                                  [clearable]="false"
                                >
                                </ng-select>
                              </div>
                            </div>
                            <div class="col-12 col-md-6 mb-3">
                              <label class="label" for="input-Ticket-Type">Ticket Type</label>
                              <span *ngIf="isDetail">{{ getValue(ticketDetailModel?.ticketTypeId, ticketTypeList) }}</span>
                              <div *ngIf="isCreate || isEdit">
                                <ng-select
                                  name="ticketType"
                                  [items]="ticketTypeList"
                                  bindLabel="name"
                                  bindValue="id"
                                  #ticketType="ngModel"
                                  (change)="ticketDetailModel.ticketTypeName = $event.name"
                                  [(ngModel)]="ticketModel.ticketTypeId"
                                  notFoundText="No Ticket Type Found"
                                  placeholder="Select Ticket Type"
                                  appendTo="body"
                                  [clearable]="false"
                                >
                                </ng-select>
                              </div>
                            </div>
                            <div
                              class="mb-3"
                              [ngClass]="{
                                'col-12 col-xl-3': ticketModel.isBankofHours,
                                'col-12 col-md-6': !ticketModel.isBankofHours
                              }"
                            >
                              <label class="label" for="input-Opened">Opened</label>
                              <span *ngIf="isDetail || isEdit">
                                {{ ticketDetailModel?.open | date : dateFormat }}
                              </span>
                              <div *ngIf="isCreate">
                                <input
                                  nbInput
                                  name="openedDate"
                                  #openedDate="ngModel"
                                  [(ngModel)]="ticketModel.open"
                                  placeholder="Select Opened Date"
                                  fullWidth
                                  [nbDatepicker]="openDate"
                                  (ngModelChange)="openDateChanged($event)"
                                  readonly
                                  autocomplete="off"
                                />
                                <nb-datepicker #openDate></nb-datepicker>
                                <sfl-error-msg
                                  [control]="openedDate"
                                  [isFormSubmitted]="ticketForm?.submitted"
                                  fieldName="Opened date"
                                ></sfl-error-msg>
                              </div>
                            </div>
                            <div
                              class="mb-3"
                              [ngClass]="{
                                'col-12 col-xl-3 mt-xl-3': ticketModel.isBankofHours,
                                'col-12 col-md-6': !ticketModel.isBankofHours
                              }"
                              *ngIf="ticketModel.isBankofHours"
                            >
                              <div class="form-control-group">
                                <label class="me-2 label"
                                  >Contracted Hours
                                  <span *ngIf="(isDetail || isEdit) && ticketModel?.contractedHours">
                                    ({{ ticketModel?.contractedHours }})</span
                                  ></label
                                >
                                <nb-toggle
                                  [(checked)]="ticketModel.isContractedHours"
                                  (change)="contractedHoursToggleChange()"
                                  status="primary"
                                  [disabled]="isDetail || ticketModel?.remainingContractHours === 0"
                                ></nb-toggle>
                              </div>
                            </div>
                            <div
                              [ngClass]="{
                                'col-12 col-xl-6 mt-xl-3': ticketModel.isBankofHours,
                                'col-12 col-md-6': !ticketModel.isBankofHours
                              }"
                            >
                              <label class="label" for="input-Portfolio">
                                Closed <span *ngIf="(isCreate || isEdit) && ticketModel.status === 3" class="ms-1 text-danger">*</span>
                              </label>
                              <span *ngIf="isDetail && ticketDetailModel?.close">
                                {{ ticketDetailModel?.close | date : dateFormat }}
                              </span>
                              <span *ngIf="isDetail && !ticketDetailModel?.close">-</span>
                              <div *ngIf="dateLoaded && (isCreate || isEdit)">
                                <input
                                  nbInput
                                  name="closedDate"
                                  #closedDate="ngModel"
                                  [(ngModel)]="ticketModel.close"
                                  placeholder="Select Closed Date"
                                  fullWidth
                                  [nbDatepicker]="closedDatePicker"
                                  [required]="ticketModel.status === 3"
                                  readonly
                                  autocomplete="off"
                                  [disabled]="ticketModel.status !== 3"
                                  (ngModelChange)="closedDateChanged($event)"
                                />
                                <nb-datepicker #closedDatePicker [min]="minDate"></nb-datepicker>
                                <sfl-error-msg
                                  [control]="closedDate"
                                  [isFormSubmitted]="ticketForm?.submitted"
                                  fieldName="Closed date"
                                ></sfl-error-msg>
                              </div>
                            </div>
                          </div>
                        </nb-accordion-item-body>
                      </nb-accordion-item>
                    </nb-accordion>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-12">
              <div class="borderBottom pb-3">
                <nb-accordion [nbSpinner]="deviceLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
                  <nb-accordion-item [expanded]="deviceInformation" class="border-bottom">
                    <nb-accordion-item-header class="accordion_head">
                      Device Information

                      <button
                        nbButton
                        status="primary"
                        size="medium"
                        type="button"
                        id="workLog"
                        class="addStyle"
                        (click)="$event.stopPropagation(); onAddDevice(ticketModel)"
                        *ngIf="isEdit || isCreate"
                      >
                        Add Device
                      </button>
                    </nb-accordion-item-header>
                    <nb-accordion-item-body>
                      <div *ngIf="!ticketModel.ticketDeviceMaps?.length">No device information</div>
                      <div *ngIf="ticketModel.ticketDeviceMaps?.length" class="m-w-550">
                        <div class="table-responsive">
                          <div class="row flex-nowrap g-0 w-100 pt-2 min-w-900">
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-2 col-md-2 col-lg-1 pe-0 flex-shrink-0'
                                  : 'col col-sm-3 col-md-3 col-lg-3 pe-0 flex-shrink-0'
                              "
                            >
                              <label class="label">Device Type</label>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-2 col-md-2 col-lg-1 pe-0 flex-shrink-0'
                                  : 'col col-sm-2 col-md-2 col-lg-2 pe-0 flex-shrink-0'
                              "
                            >
                              <label class="label">Device</label>
                            </div>
                            <div
                              class="col col-sm-2 col-md-2 col-lg-1 pe-0 flex-shrink-0"
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-2 col-md-2 col-lg-1 pe-0 flex-shrink-0'
                                  : 'col col-sm-3 col-md-3 col-lg-3 pe-0 flex-shrink-0'
                              "
                            >
                              <label class="label">Manufacturer</label>
                            </div>
                            <div
                              class="col col-sm-2 col-md-2 col-lg-1 pe-0 flex-shrink-0"
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-2 col-md-2 col-lg-1 pe-0 flex-shrink-0'
                                  : 'col col-sm-3 col-md-3 col-lg-3 pe-0 flex-shrink-0'
                              "
                            >
                              <label class="label">Model</label>
                            </div>
                            <div
                              class="col col-sm-1 col-md-1 col-lg-0 pe-0 flex-shrink-0"
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-2 col-md-2 col-lg-1 pe-0 flex-shrink-0'
                                  : 'col col-sm-3 col-md-3 col-lg-3 pe-0 flex-shrink-0'
                              "
                            >
                              <label class="label">Fault Code</label>
                            </div>
                            <div
                              class="col col-sm-1 col-md-1 col-lg-0 pe-0 flex-shrink-0"
                              *ngIf="ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)"
                            >
                              <label class="label">Device Outage</label>
                            </div>
                            <div
                              class="col col-sm-1 col-md-1 col-lg-0 pe-0 flex-shrink-0"
                              *ngIf="ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)"
                            >
                              <label class="label">Planned Outage</label>
                            </div>
                            <div
                              class="col col-sm-1 col-md-1 col-lg-1 pe-0 flex-shrink-0"
                              *ngIf="ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)"
                            >
                              <label class="label">Start Date</label>
                            </div>
                            <div
                              class="col col-sm-1 col-md-1 col-lg-1 pe-0 flex-shrink-0"
                              *ngIf="ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)"
                            >
                              <label class="label">End Date</label>
                            </div>
                            <div
                              class="col col-sm-1 col-md-1 col-lg-1 pe-0 flex-shrink-0"
                              *ngIf="ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)"
                            >
                              <label class="label">Override Losses(kw)</label>
                            </div>
                            <div
                              class="col col-sm-2 col-md-2 col-lg-2 pe-0 flex-shrink-0"
                              *ngIf="ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)"
                            >
                              <label class="label">Description<span *ngIf="isCreate || isEdit" class="ms-1 text-danger">*</span></label>
                            </div>
                            <div class="col col-sm-1 col-md-1 col-lg-1 pe-0 flex-shrink-0" *ngIf="isEdit || isCreate">
                              <label
                                [ngClass]="
                                  !(ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps))
                                    ? 'label'
                                    : 'label text-center'
                                "
                                >Actions</label
                              >
                            </div>
                          </div>
                          <div
                            class="row flex-nowrap g-0 w-100 pt-2 min-w-900"
                            *ngFor="let item of this.ticketModel.ticketDeviceMaps; let i = index"
                          >
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-2 col-md-2 col-lg-1 pe-0 flex-shrink-0'
                                  : 'col col-sm-3 col-md-3 col-lg-3 pe-0 flex-shrink-0'
                              "
                            >
                              <span>
                                {{ item?.deviceTypeName ? item?.deviceTypeName : '-' }}
                              </span>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-2 col-md-2 col-lg-1 pe-0 flex-shrink-0'
                                  : 'col col-sm-2 col-md-2 col-lg-2 pe-0 flex-shrink-0'
                              "
                            >
                              <span
                                [ngStyle]="{
                                  color: item?.alertStatus === 'Ticketed' ? '#f0c108' : item?.alertStatus === 'Resolved' ? '#61c98b' : ''
                                }"
                              >
                                {{ item?.label ? item?.label : '-' }}
                              </span>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-2 col-md-2 col-lg-1 pe-0 flex-shrink-0'
                                  : 'col col-sm-3 col-md-3 col-lg-3 pe-0 flex-shrink-0'
                              "
                            >
                              <span>{{ item?.manufacturer ? item.manufacturer : '-' }}</span>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-2 col-md-2 col-lg-1 pe-0 flex-shrink-0'
                                  : 'col col-sm-3 col-md-3 col-lg-3 pe-0 flex-shrink-0'
                              "
                            >
                              <span>{{ item?.siteDeviceName ? item?.siteDeviceName : '-' }}</span>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-1 col-md-1 col-lg-0 pe-0 flex-shrink-0'
                                  : 'col col-sm-auto col-md-auto col-lg-auto pe-0 flex-shrink-0'
                              "
                            >
                              <span *ngIf="!isEdit && item.faultCodes.length">
                                <ng-container
                                  *ngFor="let code of item.faultCodes | slice : 0 : (item.show ? item.faultCodes?.length : 3); last as last"
                                >
                                  <span>{{ code }}</span
                                  >{{ last ? '' : ', ' }}
                                </ng-container>
                                <a
                                  *ngIf="item.faultCodes?.length > 3"
                                  class="text-primary link cursor-pointer small"
                                  nbTooltip="{{ item?.faultCodes.join(', ') }}"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="primary"
                                  (click)="item.show = !item.show"
                                >
                                  {{ item.show ? '  ' + ' ...- ' : '  ' + '+' + (item?.faultCodes.length - 3) + ' more' }}</a
                                >
                              </span>
                              <div *ngIf="!item.faultCodes.length">-</div>
                              <span *ngIf="isEdit">
                                <nb-tag-list (tagRemove)="onTagRemove($event, item)" class="fault-code-tag-list">
                                  <nb-tag *ngFor="let code of item.faultCodes" [text]="code" [size]="tiny" removable></nb-tag>
                                  <input
                                    type="text"
                                    nbTagInput
                                    (tagAdd)="onTagAdd($event, item)"
                                    (keydown.enter)="$event.preventDefault(); $event.stopPropagation()"
                                    fullWidth
                                    [placeholder]="item.faultCodes?.length ? '' : 'Enter fault code'"
                                    maxlength="100"
                                  />
                                </nb-tag-list>
                              </span>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-1 col-md-1 col-lg-0 pe-0 flex-shrink-0'
                                  : 'col col-sm-auto col-md-auto col-lg-auto pe-0 flex-shrink-0'
                              "
                            >
                              <nb-checkbox
                                *ngIf="ticketModel.isGADSReporting || item.comments"
                                name="{{ 'useAsTicketContact- ' + i }}"
                                [checked]="item.isADeviceOutage"
                                [(ngModel)]="item.isADeviceOutage"
                                (ngModelChange)="onDeviceChange($event, item)"
                                [disabled]="isDetail || !ticketModel.isGADSReporting"
                              ></nb-checkbox>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-1 col-md-1 col-lg-0 pe-0 flex-shrink-0'
                                  : 'col col-sm-auto col-md-auto col-lg-auto pe-0 flex-shrink-0'
                              "
                            >
                              <nb-checkbox
                                *ngIf="ticketModel.isGADSReporting || item.comments"
                                name="{{ 'useAsTicketContact-' + i }}"
                                [checked]="item.isPlannedDowntime"
                                [(ngModel)]="item.isPlannedDowntime"
                                [disabled]="(!item.deviceId && !item.isADeviceOutage) || isDetail || !ticketModel.isGADSReporting"
                              ></nb-checkbox>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-1 col-md-1 col-lg-1 pe-2 flex-shrink-0'
                                  : 'col col-sm-auto col-md-auto col-lg-auto pe-0 flex-shrink-0'
                              "
                            >
                              <ng-container *ngIf="ticketModel.isGADSReporting || item.comments">
                                <input
                                  nbInput
                                  id="{{ 'startDateTime-' + i }}"
                                  name="{{ 'startDateTime-' + i }}"
                                  #startDateTime="ngModel"
                                  class="form-control"
                                  [(ngModel)]="item.startDateTimeUTCDate"
                                  (ngModelChange)="onActivityStartDateChange(item)"
                                  placeholder="Select Start Date & Time"
                                  fullWidth
                                  [nbDatepicker]="activityDeviceStartDateTime"
                                  readonly
                                  autocomplete="off"
                                  nbDateTimePickerValidator
                                  [minDateTime]="item.minStartDateTimeUTCDate"
                                  [maxDateTime]="item.maxStartDateTimeUTCDate"
                                  [disabled]="(!item.deviceId && !item.isADeviceOutage) || isDetail || !ticketModel.isGADSReporting"
                                />
                                <nb-date-timepicker
                                  [min]="item.minStartDateTimeUTCDate"
                                  [max]="item.maxStartDateTimeUTCDate"
                                  #activityDeviceStartDateTime
                                  [format]="dateTimeFormat"
                                  twelveHoursFormat
                                ></nb-date-timepicker>
                              </ng-container>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-1 col-md-1 col-lg-1 pe-2 flex-shrink-0'
                                  : 'col col-sm-auto col-md-auto col-lg-auto pe-0 flex-shrink-0'
                              "
                            >
                              <ng-container *ngIf="ticketModel.isGADSReporting || item.comments">
                                <input
                                  nbInput
                                  class="form-control"
                                  id="{{ 'endDateTime-' + i }}"
                                  name="{{ 'endDateTime-' + i }}"
                                  #endDateTime="ngModel"
                                  [(ngModel)]="item.endDateTimeUTCDate"
                                  placeholder="Select End Date & Time"
                                  fullWidth
                                  readonly
                                  autocomplete="off"
                                  [nbDatepicker]="activityDeviceEndDateTime"
                                  nbDateTimePickerValidator
                                  [minDateTime]="item.minEndDateTimeUTCDate"
                                  [maxDateTime]="item.maxEndDateTimeUTCDate"
                                  [disabled]="
                                    (item.startDateTimeUTCDate && !item.deviceId && !item.isADeviceOutage) ||
                                    isDetail ||
                                    !ticketModel.isGADSReporting ||
                                    !item.startDateTimeUTCDate
                                  "
                                />
                                <nb-date-timepicker
                                  [min]="nbMinEndDateTimeUTCDate(item.minEndDateTimeUTCDate)"
                                  [max]="nbMaxEndDateTimeUTCDate(item.maxEndDateTimeUTCDate)"
                                  #activityDeviceEndDateTime
                                  [format]="dateTimeFormat"
                                  twelveHoursFormat
                                ></nb-date-timepicker>
                                <div
                                  *ngIf="
                                    (endDateTime.invalid && (endDateTime.dirty || endDateTime.touched)) ||
                                    ticketModel.isGADSReporting ||
                                    item.comments
                                  "
                                >
                                  <div
                                    class="input-error f-s-13"
                                    *ngIf="
                                      item.endDateTimeUTCDate &&
                                      (endDateTime.errors?.minDateTimeError || endDateTime.errors?.maxDateTimeError)
                                    "
                                  >
                                    <ng-container *ngIf="endDateTime.errors?.minDateTimeError">
                                      End Date & Time should be greater or same as {{ item.minEndDateTimeUTCDate | date : dateTimeFormat }}.
                                    </ng-container>
                                    <ng-container *ngIf="endDateTime.errors?.maxDateTimeError">
                                      End Date & Time should be less or same as {{ item.maxEndDateTimeUTCDate | date : dateTimeFormat }}.
                                    </ng-container>
                                  </div>
                                </div>
                              </ng-container>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-1 col-md-1 col-lg-1 pe-2 flex-shrink-0'
                                  : 'col col-sm-auto col-md-auto col-lg-auto pe-0 flex-shrink-0'
                              "
                            >
                              <ng-container *ngIf="ticketModel.isGADSReporting || item.comments">
                                <input
                                  id="{{ 'overrideLosesKW-' + i }}"
                                  name="{{ 'overrideLosesKW-' + i }}"
                                  fullWidth
                                  class="form-control"
                                  #overrideLosesKW="ngModel"
                                  [(ngModel)]="item.overrideLosesKW"
                                  nbInput
                                  [NeedDifferentValue]="item.acNameplateKW"
                                  OnlyNumber
                                  [disabled]="(!item.deviceId && !item.isADeviceOutage) || isDetail || !ticketModel.isGADSReporting"
                                />
                                <div *ngIf="overrideLosesKW.invalid && (overrideLosesKW.dirty || overrideLosesKW.touched)">
                                  <div class="input-error f-s-13" *ngIf="overrideLosesKW.errors?.needDifferentValue">
                                    Please enter different value from AC Nameplate.
                                  </div>
                                </div>
                              </ng-container>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-2 col-md-2 col-lg-2 pe-0 flex-shrink-0'
                                  : 'col col-sm-auto col-md-auto col-lg-auto pe-0 flex-shrink-0'
                              "
                            >
                              <ng-container *ngIf="ticketModel.isGADSReporting || item.comments">
                                <input
                                  nbInput
                                  fullWidth
                                  class="form-control"
                                  id="{{ 'comments-' + i }}"
                                  name="{{ 'comments-' + i }}"
                                  #comments="ngModel"
                                  [(ngModel)]="item.comments"
                                  maxlength="1000"
                                  [required]="item.isADeviceOutage"
                                  [disabled]="(!item.deviceId && !item.isADeviceOutage) || isDetail || !ticketModel.isGADSReporting"
                                />
                                <sfl-error-msg
                                  [control]="comments"
                                  [isFormSubmitted]="ticketForm?.submitted"
                                  fieldName="Description"
                                ></sfl-error-msg>
                              </ng-container>
                            </div>
                            <div
                              [ngClass]="
                                ticketModel.isGADSReporting || hasDeviceComments(ticketModel.ticketDeviceMaps)
                                  ? 'col col-sm-1 col-md-1 col-lg-1 pe-2 flex-shrink-0 text-center'
                                  : 'col col-sm-auto col-md-auto col-lg-auto pe-0 flex-shrink-0'
                              "
                              *ngIf="isEdit || isCreate"
                            >
                              <div class="d-flex justify-content-center">
                                <a class="text-danger ms-3 listgrid-icon">
                                  <em
                                    class="fa fa-trash"
                                    nbTooltip="Delete"
                                    (click)="deleteDevice(item.siteDeviceId)"
                                    nbTooltipPlacement="top"
                                    nbTooltipStatus="danger"
                                  ></em>
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </nb-accordion-item-body>
                  </nb-accordion-item>
                </nb-accordion>
              </div>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-12">
              <div class="borderBottom">
                <div class="row">
                  <div class="col-12 col-sm-8 borderRight pb-3">
                    <nb-accordion [ngClass]="{ 'h-100': issueAccordion }">
                      <nb-accordion-item
                        [expanded]="issueAccordion"
                        (collapsedChange)="accordionChange($event, 'issueAccordion')"
                        class="border-bottom"
                      >
                        <nb-accordion-item-header class="accordion_head">
                          Issue<span *ngIf="isCreate || isEdit" class="ms-1 text-danger">*</span>
                        </nb-accordion-item-header>
                        <nb-accordion-item-body>
                          <span *ngIf="isDetail" [innerText]="ticketDetailModel.issue"></span>
                          <div *ngIf="isCreate || isEdit">
                            <textarea
                              nbInput
                              rows="8"
                              name="issue"
                              id="issue"
                              #issue="ngModel"
                              [(ngModel)]="ticketModel.issue"
                              placeholder="Issue"
                              maxlength="5120"
                              required
                              fullWidth
                            >
                            </textarea>
                            <sfl-error-msg [control]="issue" [isFormSubmitted]="ticketForm?.submitted" fieldName="Issue"></sfl-error-msg>
                          </div>

                          <div class="col-12 mt-3" *ngIf="hasReopenedFromTicket()">
                            <div class="d-flex" *ngIf="showIssueBlock && (ticketModel.customerId || ticketDetailModel.customerId)">
                              <label class="label">Link Ticket </label>
                            </div>

                            <div
                              class="mb-2"
                              *ngIf="(isDetail || isEdit) && ticketDetailModel?.reletedTicketsDetails.length && showIssueBlock"
                            >
                              <div
                                class="mb-2"
                                *ngFor="let reletedTicket of ticketDetailModel?.reletedTicketsDetails; let linkIndex = index"
                              >
                                <div id="fixed-table" setTableHeight class="col-12 table-responsive mt-3 table-card-view">
                                  <table class="table table-hover table-bordered" aria-describedby="Ticket List">
                                    <thead>
                                      <tr>
                                        <th scope="col">Number</th>
                                        <th scope="col">UserName</th>
                                        <th scope="col">Created Date</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr *ngFor="let ticket of reletedTicket?.tickets; let index = index">
                                        <ng-container *ngIf="ticket.reopenedDirectionType === reopenedDirectionType.ReopenedFrom">
                                          <td data-title="Number">
                                            <span class="ticketMediumPriority" *ngIf="ticket?.customerId !== ticketModel?.customerId">
                                              <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                                              ></span
                                            >
                                            <a
                                              target="_blank"
                                              [routerLink]="['../' + ticket.ticketId]"
                                              [ngClass]="ticket?.customerId !== ticketModel?.customerId ? 'm-l-10' : ''"
                                              >{{ ticket.ticketNumber }}</a
                                            >
                                          </td>
                                          <td data-title="user name">{{ ticket?.createdBy }}</td>
                                          <td data-title="createdDate">{{ ticket?.createdDate | date : dateTimeFormat }}</td>
                                        </ng-container>
                                      </tr>
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                        </nb-accordion-item-body>
                      </nb-accordion-item>
                    </nb-accordion>
                  </div>
                  <div class="col-12 col-sm-4 pb-3">
                    <nb-accordion
                      [ngClass]="{ 'h-100': impactAccordion }"
                      [nbSpinner]="exclusionLoading"
                      nbSpinnerStatus="primary"
                      nbSpinnerSize="large"
                    >
                      <nb-accordion-item
                        [expanded]="impactAccordion"
                        (collapsedChange)="accordionChange($event, 'impactAccordion')"
                        class="border-bottom"
                      >
                        <nb-accordion-item-header class="accordion_head"> Impact </nb-accordion-item-header>
                        <nb-accordion-item-body>
                          <div class="row">
                            <div class="col-12 col-lg-6 mb-3">
                              <label class="label" for="input-Portfolio"
                                >Production Loss<span *ngIf="isCreate || isEdit" class="ms-1 text-danger">*</span></label
                              >
                              <span *ngIf="isDetail">{{
                                getValue(ticketDetailModel?.productionLoss, productionLossList, 'value') ?? '--'
                              }}</span>
                              <div *ngIf="isCreate || isEdit">
                                <ng-select
                                  name="productionLoss"
                                  [items]="productionLossList"
                                  (change)="prductionLossChange($event)"
                                  bindLabel="value"
                                  bindValue="id"
                                  #productionLoss="ngModel"
                                  [(ngModel)]="ticketModel.productionLoss"
                                  notFoundText="No Production Loss Found"
                                  placeholder="Select Production Loss"
                                  appendTo="body"
                                  [clearable]="false"
                                  required="true"
                                >
                                </ng-select>
                              </div>
                            </div>
                            <div class="col-12 col-lg-6 mb-3" *ngIf="ticketModel.productionLoss !== 1">
                              <label class="label" for="input-Portfolio">Affected kWac</label>
                              <span *ngIf="isDetail">{{ ticketDetailModel?.affectedkWac || '-' }}</span>
                              <div class="d-flex align-items-center" *ngIf="isCreate || isEdit">
                                <input
                                  nbInput
                                  name="affectedkW"
                                  #affectedkW="ngModel"
                                  [(ngModel)]="ticketModel.affectedkWac"
                                  placeholder="Affected kWac"
                                  fullWidth
                                  sflValidators
                                />
                                <span class="ms-auto ps-1">kW</span>
                              </div>
                            </div>
                            <div class="col-12 col-lg-6 mb-3" *ngIf="ticketModel.productionLoss === 0 || ticketModel.productionLoss === 1">
                              <label class="label" for="input-Portfolio">Loss Type </label>
                              <span *ngIf="isDetail">{{ ticketDetailModel?.lossTypeStr || '-' }}</span>
                              <div *ngIf="isCreate || isEdit">
                                <ng-select
                                  name="lossType"
                                  [items]="lossTypeList"
                                  bindLabel="name"
                                  bindValue="id"
                                  #lossType="ngModel"
                                  [(ngModel)]="ticketModel.lossType"
                                  notFoundText="No Loss Type Found"
                                  placeholder="Select Loss Type"
                                  appendTo="body"
                                  [clearable]="true"
                                >
                                </ng-select>
                              </div>
                            </div>
                            <div class="col-12 col-lg-6 mb-3" *ngIf="ticketModel.productionLoss === 0">
                              <label class="label" for="input-Portfolio">Est. kWh Loss</label>
                              <span *ngIf="isDetail">{{ ticketDetailModel?.estKWhLoss || '-' }}</span
                              ><span *ngIf="isDetail && ticketDetailModel?.estKWhLoss">KWh</span>
                              <div class="d-flex align-items-center" *ngIf="isCreate || isEdit">
                                <input
                                  nbInput
                                  name="estLoss"
                                  #estLoss="ngModel"
                                  [(ngModel)]="ticketModel.estKWhLoss"
                                  placeholder="Est. kWh Loss"
                                  fullWidth
                                  sflValidators
                                />
                                <span class="ms-auto ps-1">kWh</span>
                              </div>
                            </div>
                            <div
                              class="col-12"
                              *ngIf="
                                ((isDetail && ticketDetailModel?.hasExclusion) || isCreate || isEdit) && ticketModel.productionLoss !== 1
                              "
                            >
                              <label class="label" for="input-Portfolio">Exclusions</label>
                              <span *ngIf="isDetail && ticketDetailModel?.hasExclusion">{{
                                ticketDetailModel?.hasExclusion ? 'Yes' : 'No'
                              }}</span>
                              <div *ngIf="isCreate || isEdit">
                                <nb-radio-group
                                  class="d-flex"
                                  name="exclusions"
                                  [(ngModel)]="ticketModel.hasExclusion"
                                  (ngModelChange)="changeExclusions(ticketModel?.ticketNumber, ticketModel?.hasExclusion)"
                                >
                                  <nb-radio [value]="true">Yes</nb-radio>
                                  <nb-radio [value]="false">No</nb-radio>
                                </nb-radio-group>
                              </div>
                            </div>
                            <div class="col-12" *ngIf="ticketModel?.hasExclusion && exclusionTypeList?.length > 0">
                              <label class="label" for="input-Portfolio"
                                >Exclusion Type<span *ngIf="isCreate || isEdit" class="ms-1 text-danger">*</span></label
                              >
                              <span *ngIf="isDetail && ticketDetailModel?.hasExclusion && exclusionTypeList?.length > 0">{{
                                exclusionTypeData
                              }}</span>
                              <div *ngIf="isCreate || isEdit">
                                <ng-select
                                  name="exclusionTypeID"
                                  [items]="exclusionTypeList || []"
                                  bindLabel="name"
                                  bindValue="id"
                                  [(ngModel)]="ticketModel.exclusionTypeId"
                                  #exclusion="ngModel"
                                  notFoundText="No Exclusion Type Found"
                                  placeholder="Select Exclusion Type"
                                  appendTo="body"
                                  [required]="ticketModel?.hasExclusion && exclusionTypeList?.length > 0"
                                >
                                </ng-select>
                                <sfl-error-msg
                                  *ngIf="ticketModel?.hasExclusion"
                                  [control]="exclusion"
                                  [isFormSubmitted]="ticketForm?.submitted"
                                  fieldName="Exclusion"
                                ></sfl-error-msg>
                              </div>
                            </div>
                          </div>
                        </nb-accordion-item-body>
                      </nb-accordion-item>
                    </nb-accordion>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row mt-3" *ngIf="!isCreate">
            <div class="col-12">
              <div class="borderBottom pb-3">
                <nb-accordion [nbSpinner]="activityLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
                  <nb-accordion-item [expanded]="ticketActivityInformation" class="border-bottom">
                    <nb-accordion-item-header class="accordion_head"> Activity Log </nb-accordion-item-header>
                    <nb-accordion-item-body>
                      <div *ngIf="!ticketActivityLogs.length && !activityLoading">No activity log</div>
                      <div *ngIf="ticketActivityLogs.length" class="m-w-700">
                        <div class="row">
                          <div
                            [ngClass]="{
                              'col-3': !checkAuthorisationsFn([roleType.CUSTOMER]),
                              'col-5': checkAuthorisationsFn([roleType.CUSTOMER])
                            }"
                          ></div>
                          <div class="col-1 pe-0 text-center">
                            <label class="label">Truck Roll</label>
                          </div>
                          <div class="col-2 pe-0 text-center">
                            <label class="label">Truck Roll Number</label>
                          </div>
                          <div class="col-1 pe-0 text-center" *ngIf="ticketModel.isContractedHours">
                            <label class="label">Contracted Hours</label>
                          </div>
                          <div class="col-1 pe-0 text-center" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                            <label class="label">Hours</label>
                          </div>
                          <div class="col-1 pe-0 text-center" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                            <label class="label">Material Cost</label>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <label class="label">Resolved</label>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <label class="label">Fault Code</label>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <label class="label">Device</label>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <label class="label">Actions</label>
                          </div>
                        </div>
                        <div class="row mt-3" *ngFor="let item of ticketActivityLogs">
                          <div
                            [ngClass]="{
                              'col-3': !checkAuthorisationsFn([roleType.CUSTOMER]),
                              'col-5': checkAuthorisationsFn([roleType.CUSTOMER])
                            }"
                          >
                            <div class="d-flex">
                              <em class="fas fa-circle p-l-2"></em>
                              <div>
                                <div class="ms-2 d-flex">
                                  <strong>
                                    {{ item.date | date : dateFormat }}
                                  </strong>
                                  <strong
                                    class="ms-1"
                                    *ngIf="item?.ticketFieldTechDto?.length && !checkAuthorisationsFn([roleType.CUSTOMER])"
                                    >-</strong
                                  >
                                  <div *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                                    <span class="ms-1" [innerHTML]="getUniqueUserNameWithTotalHours(item.ticketFieldTechDto)"> </span>
                                  </div>
                                </div>
                                <div class="ms-2" [innerText]="item.description"></div>
                                <div class="mt-2" *ngIf="item.ticketFieldTechDto.length">
                                  <strong class="ms-1">Services</strong>
                                  <ng-container *ngFor="let fieldTech of item.ticketFieldTechDto; let i = index">
                                    <p class="ms-1 mb-0">
                                      {{ fieldTech.operationService }}
                                      <span *ngIf="item?.ticketFieldTechDto?.length && !checkAuthorisationsFn([roleType.CUSTOMER])">
                                        <strong class="ms-1">-</strong>
                                        {{ fieldTech.userName }}
                                        ({{ fieldTech.hours }}h)<span class="me-1" *ngIf="i < item.ticketFieldTechDto.length - 1">,</span>
                                      </span>
                                    </p>
                                  </ng-container>
                                </div>
                                <div
                                  *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER]) && item.ticketActivityMaterials.length"
                                  class="mt-2"
                                >
                                  <strong class="ms-1">Materials</strong>
                                  <ng-container *ngIf="costTypeList.length">
                                    <ng-container *ngFor="let costTypeItem of costTypeList; let i = index">
                                      <ng-container
                                        *ngIf="
                                          setTicketActivityMaterials(
                                            item.ticketActivityMaterials,
                                            costTypeItem.costTypeID
                                          ) as ticketActivityMaterials
                                        "
                                      >
                                        <ng-container *ngIf="ticketActivityMaterials && ticketActivityMaterials.length > 0">
                                          <p class="ms-1 mb-0">
                                            <strong class="me-1">{{ costTypeItem.costTypeName }}:</strong><br />
                                            <ng-container *ngFor="let material of ticketActivityMaterials; let i = index">
                                              ({{ material.material }}) - {{ material.materialCost | currency }}
                                              <!-- to be used -->
                                              <!-- <span class="me-1" *ngIf="i < ticketActivityMaterials.length - 1">,</span> -->
                                              <br />
                                            </ng-container>
                                          </p>
                                        </ng-container>
                                      </ng-container>
                                    </ng-container>
                                  </ng-container>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <div>{{ item.truckRole ? 'Yes' : 'No' }}</div>
                          </div>
                          <div class="col-2 pe-0 text-center">
                            <div>
                              <a
                                *ngIf="item?.truckRollNumber; else noTruckRollNumber"
                                [routerLink]="['/entities/ticket/truckroll-gallery/' + item?.truckRollNumber]"
                              >
                                {{ item?.truckRollNumber }}
                              </a>
                              <ng-template #noTruckRollNumber>
                                <span>-</span>
                              </ng-template>
                            </div>
                          </div>
                          <div class="col-1 pe-0 text-center" *ngIf="ticketModel.isContractedHours">
                            <nb-toggle
                              [(checked)]="item.isActivityContractedHours"
                              status="primary"
                              [disabled]="isDetail || ticketModel.isContractedHours === 0"
                              (change)="contractedHoursToggleChange(item.id, item.isActivityContractedHours)"
                            ></nb-toggle>
                          </div>
                          <div class="col-1 pe-0 text-center" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                            <span>{{ item.totalHours || '-' }}</span>
                          </div>
                          <div class="col-1 pe-0 text-center" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                            <span *ngIf="item?.totalMaterialCost">{{ item.totalMaterialCost | currency }}</span>
                            <span *ngIf="!item?.totalMaterialCost">-</span>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <div>{{ item.isResolve ? 'Yes' : 'No' }}</div>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <div *ngIf="item.ticketActivityFaultCodeDeviceMaps.length">
                              <ng-container *ngFor="let faultCodeItem of item.ticketActivityFaultCodeDeviceMaps">
                                <div class="mb-3">
                                  <ng-container
                                    *ngFor="
                                      let code of faultCodeItem.faultCodes
                                        | slice : 0 : (faultCodeItem.faultCodes.show ? faultCodeItem.faultCodes?.length : 3);
                                      last as last
                                    "
                                  >
                                    <span>{{ code }}</span
                                    >{{ last ? '' : ', ' }}
                                  </ng-container>
                                  <a
                                    *ngIf="faultCodeItem.faultCodes?.length > 3"
                                    class="text-primary link cursor-pointer small"
                                    nbTooltip="{{ faultCodeItem.faultCodes.join(', ') }}"
                                    nbTooltipPlacement="top"
                                    nbTooltipStatus="primary"
                                    (click)="faultCodeItem.faultCodes.show = !faultCodeItem.faultCodes.show"
                                  >
                                    {{
                                      faultCodeItem.faultCodes.show
                                        ? '  ' + ' ...- '
                                        : '  ' + '+' + (faultCodeItem.faultCodes.length - 3) + ' more'
                                    }}</a
                                  >
                                  <div *ngIf="!faultCodeItem.faultCodes.length">-</div>
                                </div>
                              </ng-container>
                            </div>
                            <div *ngIf="!item.ticketActivityFaultCodeDeviceMaps || !item.ticketActivityFaultCodeDeviceMaps.length">-</div>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <div *ngIf="item.ticketActivityFaultCodeDeviceMaps.length">
                              <ng-container *ngFor="let faultCodeItem of item.ticketActivityFaultCodeDeviceMaps">
                                <div class="mb-3">
                                  <ng-container
                                    *ngFor="
                                      let device of faultCodeItem.faultCodeDevices
                                        | slice : 0 : (faultCodeItem.show ? faultCodeItem.faultCodeDevices?.length : 3);
                                      last as last
                                    "
                                  >
                                    <span>{{ device.siteDeviceName }}</span
                                    >{{ last ? '' : ', ' }}
                                  </ng-container>
                                  <a
                                    *ngIf="faultCodeItem.faultCodeDevices?.length > 3"
                                    class="text-primary link cursor-pointer small"
                                    nbTooltip="{{ getFullDevicesList(faultCodeItem.faultCodeDevices) }}"
                                    nbTooltipPlacement="top"
                                    nbTooltipStatus="primary"
                                    (click)="faultCodeItem.show = !faultCodeItem.show"
                                  >
                                    {{
                                      faultCodeItem.show
                                        ? '  ' + ' ...- '
                                        : '  ' + '+' + (faultCodeItem.faultCodeDevices.length - 3) + ' more'
                                    }}</a
                                  >
                                  <div *ngIf="!faultCodeItem.faultCodeDevices.length">-</div>
                                </div>
                              </ng-container>
                            </div>
                            <div *ngIf="!item.ticketActivityFaultCodeDeviceMaps || !item.ticketActivityFaultCodeDeviceMaps.length">-</div>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <div class="d-flex justify-content-center align-items-center">
                              <span class="me-2" [ngClass]="{ isNoImages: item.imgCount === 0 }">{{ item.imgCount }}</span>
                              <span class="me-2 mt_1 cursor-pointer" [ngClass]="{ isNoImages: item.imgCount === 0 }">
                                <em
                                  class="pi pi-images"
                                  (click)="item.imgCount > 0 ? openDropBoxImageGallery(item) : null"
                                  nbTooltip="Open Gallery"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="primary"
                                ></em>
                              </span>
                              <a class="cursor-pointer" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                                <em
                                  class="fa fa-edit"
                                  (click)="addActivityLog(item, true)"
                                  nbTooltip="Edit"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="primary"
                                ></em>
                              </a>
                              <a class="text-danger ms-2 cursor-pointer" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                                <em
                                  class="fa fa-trash"
                                  (click)="deleteActivityLog(item.id)"
                                  nbTooltip="Delete"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="danger"
                                ></em>
                              </a>
                            </div>
                          </div>
                        </div>
                        <div class="row mt-3 borderTop pt-2">
                          <div
                            [ngClass]="{
                              'col-3': !checkAuthorisationsFn([roleType.CUSTOMER]),
                              'col-5': checkAuthorisationsFn([roleType.CUSTOMER])
                            }"
                          ></div>
                          <div class="col-1 pe-0 text-center">
                            <div>{{ getUniqueTruckRollNumbersTotal(ticketActivityLogs, 'truckRoleCount') }}</div>
                          </div>
                          <div class="col-2 pe-0 text-center"></div>
                          <div class="col-1 pe-0 text-center" *ngIf="ticketModel.isContractedHours"></div>
                          <div class="col-1 pe-0 text-center" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                            <span>{{ getTotal(ticketActivityLogs, 'totalHours') }}</span>
                          </div>
                          <div class="col-1 pe-0 text-center" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                            <span>{{ getTotal(ticketActivityLogs, 'totalMaterialCost') | currency }}</span>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <div>
                              {{ resolvedVal === true ? 'Yes' : 'No' }}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="col-12 mt-3" *ngIf="hasReopenedAsTicket()">
                        <div class="d-flex" *ngIf="showIssueBlock && (ticketModel.customerId || ticketDetailModel.customerId)">
                          <label class="label">Link Ticket </label>
                        </div>

                        <div class="mb-2" *ngIf="(isDetail || isEdit) && ticketDetailModel?.reletedTicketsDetails.length && showIssueBlock">
                          <div class="mb-2" *ngFor="let reletedTicket of ticketDetailModel?.reletedTicketsDetails; let linkIndex = index">
                            <div id="fixed-table" setTableHeight class="col-12 table-responsive mt-3 table-card-view">
                              <table class="table table-hover table-bordered" aria-describedby="Ticket List">
                                <thead>
                                  <tr>
                                    <th scope="col">Number</th>
                                    <th scope="col">User Name</th>
                                    <th scope="col">Created Date</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr *ngFor="let ticket of reletedTicket?.tickets; let index = index">
                                    <ng-container *ngIf="ticket.reopenedDirectionType === reopenedDirectionType.ReopenedAs">
                                      <td data-title="Number">
                                        <span class="ticketMediumPriority" *ngIf="ticket?.customerId !== ticketModel?.customerId">
                                          <i class="fa fa-exclamation-triangle" aria-hidden="true"></i
                                        ></span>
                                        <a
                                          target="_blank"
                                          [routerLink]="['../' + ticket.ticketId]"
                                          [ngClass]="ticket?.customerId !== ticketModel?.customerId ? 'm-l-10' : ''"
                                          >{{ ticket.ticketNumber }}</a
                                        >
                                      </td>
                                      <td data-title="user name">{{ ticket?.createdBy }}</td>

                                      <td data-title="createdDate">{{ ticket?.createdDate | date : dateTimeFormat }}</td>
                                    </ng-container>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      </div>
                    </nb-accordion-item-body>
                  </nb-accordion-item>
                </nb-accordion>
              </div>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-12">
              <div class="borderBottom pb-3">
                <nb-accordion>
                  <nb-accordion-item [expanded]="attachments" class="border-bottom attachment_section">
                    <nb-accordion-item-header class="accordion_head"> Attachments </nb-accordion-item-header>
                    <nb-accordion-item-body>
                      <div class="row">
                        <div>
                          <nb-tabset fullWidthv (changeTab)="onAttachmentTabChange($event.tabTitle)">
                            <nb-tab tabTitle="Images" [nbSpinner]="false" nbSpinnerStatus="primary" nbSpinnerSize="large">
                              <div class="photo-carousel">
                                <div class="d-flex align-items-start justify-content-end mb-3">
                                  <button
                                    *ngIf="!isCreate"
                                    nbButton
                                    status="primary"
                                    size="small"
                                    type="button"
                                    id="DropBoxImageGallery"
                                    class="me-2"
                                    (click)="openDropBoxImageGallery(null)"
                                    [disabled]="imageAttachments?.totalCount === 0"
                                  >
                                    <span class="d-flex"
                                      ><em class="pi pi-images me-2"></em>GALLERY ({{ imageAttachments?.totalCount }})</span
                                    >
                                  </button>
                                  <label
                                    for="fileImageUpload"
                                    class="file-upload-btn cursor-pointer float-end me-2"
                                    *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                  >
                                    <span class="d-none d-lg-inline-block">
                                      <em class="fa fa-upload"></em>
                                      Upload
                                    </span>
                                    <em class="d-inline-block d-lg-none fa fa fa-upload"></em>
                                    <input
                                      id="fileImageUpload"
                                      type="file"
                                      #imageInput
                                      accept="image/*"
                                      multiple
                                      style="display: none"
                                      (change)="uploadFilesToGallery($event, 'image')"
                                    />
                                  </label>
                                </div>
                                <div class="main-image-gallery">
                                  <div
                                    id="fixed-table"
                                    setTableHeight
                                    [isFilterDisplay]="true"
                                    class="col-12 table-responsive table-card-view"
                                  >
                                    <table class="table table-hover table-borderless" aria-describedby="Ticket List">
                                      <thead *ngIf="createFileUploadList.length || imageAttachments?.fileGallery?.length">
                                        <tr>
                                          <th id="ImageName">Image Name</th>
                                          <th id="Uploaded">Uploaded By</th>
                                          <th id="Date">Date</th>
                                          <th id="Action" class="text-center">Action</th>
                                        </tr>
                                      </thead>
                                      <tbody *ngIf="!isCreate">
                                        <ng-container *ngIf="imageAttachments?.fileGallery?.length">
                                          <tr
                                            *ngFor="
                                              let img of imageAttachments?.fileGallery
                                                | paginate
                                                  : {
                                                      id: 'images',
                                                      itemsPerPage: imagesPaginationParams.itemsCount,
                                                      currentPage: imagesPaginationParams.currentPage,
                                                      totalItems: imageAttachments?.totalCount
                                                    }
                                            "
                                          >
                                            <td data-title="Image Name">
                                              <div class="d-flex align-items-center">
                                                <img
                                                  [src]="img.thumbnailUrl ? img.thumbnailUrl : img.fileUrl"
                                                  class="img-preview"
                                                  appImageLoaderDir
                                                  onError="src='assets/images/no-image-found.jpg'"
                                                />
                                                <a [href]="img.fileUrl" target="_blank">
                                                  {{ img.fileName }}
                                                </a>
                                              </div>
                                            </td>
                                            <td data-title="Uploaded By">{{ img.createdBy }}</td>
                                            <td data-title="Date">{{ img.createdDate | date : fullDateFormat }}</td>
                                            <td data-title="Action" class="text-center">
                                              <div class="d-flex align-items-center justify-content-center">
                                                <em
                                                  class="fa fa-download text-primary cursor-pointer me-3"
                                                  (click)="downloadDropBoxFile(img.id, img.fileName)"
                                                ></em>
                                                <em
                                                  *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                                  class="fa fa-trash text-danger cursor-pointer"
                                                  (click)="deleteDropBoxFile(img.id, 'Images')"
                                                ></em>
                                              </div>
                                            </td>
                                          </tr>
                                        </ng-container>
                                      </tbody>
                                      <tbody *ngIf="isCreate">
                                        <ng-container *ngFor="let img of createFileUploadList">
                                          <tr *ngIf="img.fileType === 'image'">
                                            <td data-title="Image Name">
                                              <div class="d-flex align-items-center">
                                                <em aria-hidden="true" class="pi pi-images me-2 pdf-icon text-light cursor-pointer"></em>
                                                <span>
                                                  {{ img.fileName }}
                                                </span>
                                              </div>
                                            </td>
                                            <td data-title="Uploaded By">{{ img.createdBy }}</td>
                                            <td data-title="Date">{{ img.createdDate | date : fullDateFormat }}</td>
                                            <td data-title="Action" class="text-center">
                                              <div
                                                class="d-flex align-items-center justify-content-center"
                                                *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                              >
                                                <em
                                                  class="fa fa-trash text-danger cursor-pointer"
                                                  (click)="deleteDropBoxFile(img.id, 'Images', true)"
                                                ></em>
                                              </div>
                                            </td>
                                          </tr>
                                        </ng-container>
                                      </tbody>
                                    </table>
                                    <ng-container
                                      *ngIf="
                                        (!imageAttachments?.fileGallery?.length && !isCreate) || (!createFileUploadList?.length && isCreate)
                                      "
                                    >
                                      <p class="no-record text-center">No Data Found</p>
                                    </ng-container>
                                  </div>
                                  <div class="mt-2 d-md-flex align-items-center" *ngIf="imageAttachments?.fileGallery?.length && !isCreate">
                                    <div class="d-flex align-items-center">
                                      <label class="mb-0">Items per page:</label>
                                      <ng-select
                                        class="ms-2"
                                        [(ngModel)]="imagesPaginationParams.pageSize"
                                        [clearable]="false"
                                        [searchable]="false"
                                        (change)="onChangeSize('Images')"
                                        name="imagePageSize"
                                        #imagePageSize="ngModel"
                                        appendTo="body"
                                      >
                                        <ng-option value="5">5</ng-option>
                                        <ng-option value="10">10</ng-option>
                                        <ng-option value="50">50</ng-option>
                                        <ng-option value="100">100</ng-option>
                                      </ng-select>
                                    </div>
                                    <strong class="ms-md-3">Total: {{ imageAttachments?.totalCount }}</strong>
                                    <div class="ms-md-auto ms-sm-0">
                                      <pagination-controls
                                        id="images"
                                        (pageChange)="onPageChange($event, 'Images')"
                                        class="paginate ticket-attachment"
                                      ></pagination-controls>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </nb-tab>
                            <nb-tab tabTitle="Files" [nbSpinner]="attachmentsLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
                              <div class="photo-carousel">
                                <div class="d-flex align-items-start justify-content-end mb-3">
                                  <!-- 4293: Field Tech, PL and Analyst should have the ability to upload files into Tickets  -->
                                  <button
                                    *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                    nbButton
                                    status="primary"
                                    size="small"
                                    type="button"
                                    id="addFiles"
                                    class="me-2"
                                    (click)="openFileUploadSidePanel(false, null)"
                                  >
                                    <span class="d-flex"><em class="pi pi-plus me-2"></em>Add Files</span>
                                  </button>
                                </div>
                                <div class="main-image-gallery">
                                  <div
                                    id="fixed-table"
                                    setTableHeight
                                    [isFilterDisplay]="true"
                                    class="col-12 table-responsive table-card-view"
                                  >
                                    <table class="table table-hover table-bordered" aria-describedby="Ticket List">
                                      <thead *ngIf="createFileUploadList.length || fileAttachments?.fileGallery?.length">
                                        <tr>
                                          <th id="FileName">File Name</th>
                                          <th id="Tags">Tags</th>
                                          <th id="Note">Note</th>
                                          <th id="UploadedBy">Uploaded By</th>
                                          <th id="Date">Date</th>
                                          <th id="Action" class="text-center">Action</th>
                                        </tr>
                                      </thead>
                                      <tbody *ngIf="!isCreate">
                                        <ng-container *ngIf="fileAttachments?.fileGallery?.length">
                                          <tr
                                            *ngFor="
                                              let document of fileAttachments?.fileGallery
                                                | paginate
                                                  : {
                                                      id: 'documents',
                                                      itemsPerPage: filesPaginationParams.itemsCount,
                                                      currentPage: filesPaginationParams.currentPage,
                                                      totalItems: fileAttachments?.totalCount
                                                    }
                                            "
                                          >
                                            <td data-title="File Name">
                                              <div class="d-flex align-items-center">
                                                <em aria-hidden="true" class="pi pi-file me-2 pdf-icon text-light cursor-pointer"></em>
                                                <a [href]="document.fileUrl" target="_blank">
                                                  {{ document.fileName }}
                                                </a>
                                              </div>
                                            </td>
                                            <td data-title="tags">
                                              <ng-container *ngIf="document?.fileTagTxt?.length">
                                                <span
                                                  class="tag-info-badge fw-bold"
                                                  *ngFor="let tagName of document?.fileTagTxt | slice : 0 : 5"
                                                >
                                                  <span class="px-2">
                                                    {{ tagName }}
                                                  </span>
                                                </span>
                                                {{
                                                  document?.fileTagTxt?.length > 5 ? '+' + (document?.fileTagTxt?.length - 5) + ' More' : ''
                                                }}
                                              </ng-container>
                                              <ng-container *ngIf="!document?.fileTagTxt?.length">N/A</ng-container>
                                            </td>
                                            <td data-title="Note">
                                              <div
                                                *ngIf="document.notes"
                                                nbTooltip="{{
                                                  document.notes.length > 600 ? (document.notes | slice : 0 : 600) + '...' : document.notes
                                                }}"
                                                nbTooltipPlacement="top"
                                                nbTooltipStatus="primary"
                                              >
                                                <sfl-read-more [content]="document.notes"></sfl-read-more>
                                              </div>
                                              <span *ngIf="!document.notes">N/A</span>
                                            </td>
                                            <td data-title="Uploaded By">{{ document.createdBy }}</td>
                                            <td data-title="Date">{{ document.createdDate | date : fullDateFormat }}</td>
                                            <td data-title="Action" class="text-center">
                                              <div class="d-flex align-items-center justify-content-center">
                                                <em
                                                  *ngIf="isEdit"
                                                  class="fa fa-edit text-primary cursor-pointer me-3"
                                                  (click)="openFileUploadSidePanel(true, document)"
                                                ></em>
                                                <em
                                                  class="fa fa-download text-primary cursor-pointer me-3"
                                                  (click)="downloadDropBoxFile(document.id, document.fileName)"
                                                ></em>
                                                <em
                                                  *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                                  class="fa fa-trash text-danger cursor-pointer"
                                                  (click)="deleteDropBoxFile(document.id, 'Files')"
                                                ></em>
                                              </div>
                                            </td>
                                          </tr>
                                        </ng-container>
                                      </tbody>
                                      <tbody *ngIf="isCreate">
                                        <ng-container *ngFor="let document of createFileUploadList">
                                          <tr *ngIf="document.fileType === 'document'">
                                            <td data-title="File Name">
                                              <div class="d-flex align-items-center">
                                                <em aria-hidden="true" class="pi pi-file me-2 pdf-icon text-light cursor-pointer"></em>
                                                <span>
                                                  {{ document.fileName }}
                                                </span>
                                              </div>
                                            </td>
                                            <td data-title="tags">
                                              <ng-container *ngIf="document?.fileTagTxt?.length">
                                                <span
                                                  class="tag-info-badge fw-bold"
                                                  *ngFor="let tagName of document?.fileTagTxt | slice : 0 : 5"
                                                >
                                                  <span class="px-2">
                                                    {{ tagName }}
                                                  </span>
                                                </span>
                                                {{
                                                  document?.fileTagTxt?.length > 5 ? '+' + (document?.fileTagTxt?.length - 5) + ' More' : ''
                                                }}
                                              </ng-container>
                                              <ng-container *ngIf="!document?.fileTagTxt?.length">N/A</ng-container>
                                            </td>
                                            <td data-title="Note">
                                              <div
                                                *ngIf="document.notes"
                                                nbTooltip="{{ document.notes }}"
                                                nbTooltipPlacement="top"
                                                nbTooltipStatus="primary"
                                              >
                                                <sfl-read-more [content]="document.notes"></sfl-read-more>
                                              </div>
                                              <span *ngIf="!document.notes">N/A</span>
                                            </td>
                                            <td data-title="Uploaded By">{{ document.createdBy }}</td>
                                            <td data-title="Date">{{ document.createdDate | date : fullDateFormat }}</td>
                                            <td data-title="Action" class="text-center">
                                              <div
                                                class="d-flex align-items-center justify-content-center"
                                                *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                              >
                                                <em
                                                  class="fa fa-trash text-danger cursor-pointer"
                                                  (click)="deleteDropBoxFile(document.id, 'Files', true)"
                                                ></em>
                                              </div>
                                            </td>
                                          </tr>
                                        </ng-container>
                                      </tbody>
                                    </table>
                                    <ng-container
                                      *ngIf="
                                        (!fileAttachments?.fileGallery?.length && !isCreate) || (!createFileUploadList?.length && isCreate)
                                      "
                                    >
                                      <p class="no-record text-center">No Data Found</p>
                                    </ng-container>
                                  </div>
                                  <div class="mt-2 d-md-flex align-items-center" *ngIf="fileAttachments?.fileGallery?.length && !isCreate">
                                    <div class="d-flex align-items-center">
                                      <label class="mb-0">Items per page: </label>
                                      <ng-select
                                        class="ms-2"
                                        [(ngModel)]="filesPaginationParams.pageSize"
                                        [clearable]="false"
                                        [searchable]="false"
                                        (change)="onChangeSize('Files')"
                                        name="documentsPageSize"
                                        #documentsPageSize="ngModel"
                                        appendTo="body"
                                      >
                                        <ng-option value="5">5</ng-option>
                                        <ng-option value="10">10</ng-option>
                                        <ng-option value="50">50</ng-option>
                                        <ng-option value="100">100</ng-option>
                                      </ng-select>
                                    </div>
                                    <strong class="ms-md-3">Total: {{ fileAttachments?.totalCount }}</strong>
                                    <div class="ms-md-auto ms-sm-0">
                                      <pagination-controls
                                        id="documents"
                                        (pageChange)="onPageChange($event, 'Files')"
                                        class="paginate ticket-attachment"
                                      ></pagination-controls>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </nb-tab>
                            <nb-tab tabTitle="Videos" [nbSpinner]="false" nbSpinnerStatus="primary" nbSpinnerSize="large">
                              <div class="video-tab row">
                                <div class="text-end mb-3">
                                  <label
                                    for="fileVideoUpload"
                                    class="file-upload-btn cursor-pointer float-end me-2"
                                    *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                  >
                                    <span class="d-none d-lg-inline-block">
                                      <em class="fa fa-upload"></em>
                                      Upload
                                    </span>
                                    <em class="d-inline-block d-lg-none fa fa fa-upload"></em>
                                    <input
                                      id="fileVideoUpload"
                                      #videoInput
                                      type="file"
                                      accept="video/*"
                                      multiple
                                      style="display: none"
                                      (change)="uploadFilesToGallery($event, 'video')"
                                    />
                                  </label>
                                </div>
                                <div
                                  id="fixed-table"
                                  setTableHeight
                                  [isFilterDisplay]="true"
                                  class="col-12 table-responsive table-card-view"
                                >
                                  <table class="table table-hover table-borderless" aria-describedby="Ticket List">
                                    <thead *ngIf="createFileUploadList.length || videoAttachments?.fileGallery?.length">
                                      <tr>
                                        <th id="VideoName">Video Name</th>
                                        <th id="Uploaded">Uploaded By</th>
                                        <th id="Date">Date</th>
                                        <th id="Action" class="text-center">Action</th>
                                      </tr>
                                    </thead>
                                    <tbody *ngIf="!isCreate">
                                      <ng-container *ngIf="videoAttachments?.fileGallery?.length">
                                        <tr
                                          *ngFor="
                                            let vid of videoAttachments?.fileGallery
                                              | paginate
                                                : {
                                                    id: 'videos',
                                                    itemsPerPage: videoPaginationParams.itemsCount,
                                                    currentPage: videoPaginationParams.currentPage,
                                                    totalItems: videoAttachments?.totalCount
                                                  }
                                          "
                                        >
                                          <td data-title="Video Name">
                                            <div class="d-flex align-items-center">
                                              <em aria-hidden="true" class="pi pi-video me-2 pdf-icon text-light cursor-pointer"></em>
                                              <a [href]="vid.fileUrl" target="_blank">
                                                {{ vid.fileName }}
                                              </a>
                                            </div>
                                          </td>
                                          <td data-title="Uploaded By">{{ vid.createdBy }}</td>
                                          <td data-title="Date">{{ vid.createdDate | date : fullDateFormat }}</td>
                                          <td data-title="Action" class="text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                              <em
                                                class="fa fa-download text-primary cursor-pointer me-3"
                                                (click)="downloadDropBoxFile(vid.id, vid.fileName)"
                                              ></em>
                                              <em
                                                *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                                class="fa fa-trash text-danger cursor-pointer"
                                                (click)="deleteDropBoxFile(vid.id, 'Videos')"
                                              ></em>
                                            </div>
                                          </td>
                                        </tr>
                                      </ng-container>
                                    </tbody>
                                    <tbody *ngIf="isCreate">
                                      <ng-container *ngFor="let vid of createFileUploadList">
                                        <tr *ngIf="vid.fileType === 'video'">
                                          <td data-title="Video Name">
                                            <div class="d-flex align-items-center">
                                              <em aria-hidden="true" class="pi pi-video me-2 pdf-icon text-light cursor-pointer"></em>
                                              <span>
                                                {{ vid.fileName }}
                                              </span>
                                            </div>
                                          </td>
                                          <td data-title="Uploaded By">{{ vid.createdBy }}</td>
                                          <td data-title="Date">{{ vid.createdDate | date : fullDateFormat }}</td>
                                          <td data-title="Action" class="text-center">
                                            <div
                                              class="d-flex align-items-center justify-content-center"
                                              *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                                            >
                                              <em
                                                class="fa fa-trash text-danger cursor-pointer"
                                                (click)="deleteDropBoxFile(vid.id, 'Videos', true)"
                                              ></em>
                                            </div>
                                          </td>
                                        </tr>
                                      </ng-container>
                                    </tbody>
                                  </table>
                                  <ng-container
                                    *ngIf="
                                      (!videoAttachments?.fileGallery?.length && !isCreate) || (!createFileUploadList?.length && isCreate)
                                    "
                                  >
                                    <p class="no-record text-center">No Data Found</p>
                                  </ng-container>
                                </div>
                                <div class="mt-2 d-md-flex align-items-center" *ngIf="videoAttachments?.fileGallery?.length && !isCreate">
                                  <div class="d-flex align-items-center">
                                    <label class="mb-0">Items per page: </label>
                                    <ng-select
                                      class="ms-2"
                                      [(ngModel)]="videoPaginationParams.pageSize"
                                      [clearable]="false"
                                      [searchable]="false"
                                      (change)="onChangeSize('Videos')"
                                      name="videoPageSize"
                                      #videoPageSize="ngModel"
                                      appendTo="body"
                                    >
                                      <ng-option value="5">5</ng-option>
                                      <ng-option value="10">10</ng-option>
                                      <ng-option value="50">50</ng-option>
                                      <ng-option value="100">100</ng-option>
                                    </ng-select>
                                  </div>
                                  <strong class="ms-md-3">Total: {{ videoAttachments?.totalCount }}</strong>
                                  <div class="ms-md-auto ms-sm-0">
                                    <pagination-controls
                                      id="videos"
                                      (pageChange)="onPageChange($event, 'Videos')"
                                      class="paginate ticket-attachment"
                                    ></pagination-controls>
                                  </div>
                                </div>
                              </div>
                            </nb-tab>
                          </nb-tabset>
                        </div>
                        <!-- <div class="col-12 mb-3">
                          <div class="form-control-group">
                            <label class="label" for="input-address">Dropbox Link</label>
                            <a href="{{ ticketDetailModel.dropBoxLink }}" *ngIf="isDetail && ticketDetailModel.dropBoxLink" target="_blank">
                              {{ ticketDetailModel.dropBoxLink }}
                            </a>
                            <span *ngIf="isDetail && !ticketDetailModel.dropBoxLink"> - </span>
                            <div *ngIf="isCreate || isEdit">
                              <input
                                nbInput
                                name="dropBoxLink"
                                #dropBoxLink="ngModel"
                                [(ngModel)]="ticketModel.dropBoxLink"
                                placeholder="Dropbox Link"
                                fullWidth
                              />
                            </div>
                          </div>
                        </div> -->
                        <div class="col-12">
                          <div class="d-flex" *ngIf="showIssueBlock && (ticketModel.customerId || ticketDetailModel.customerId)">
                            <label class="label">Link Ticket </label>
                            <em
                              nbtooltip="Link Ticket"
                              nbtooltipplacement="top"
                              nbtooltipstatus="text-primary"
                              aria-hidden="true"
                              class="fa fa-plus ms-auto cursor-pointer"
                              (click)="linkNewTicket()"
                            ></em>
                          </div>
                          <div class="mb-2" *ngIf="isCreate && ticketModel?.reletedTickets?.length">
                            <div class="col-12 row mt-2">
                              <div class="col-12">
                                <div class="contact-header row">
                                  <div class="col-sm-5 pe-0">
                                    <label class="label">Issue<span class="ms-1 text-danger">*</span></label>
                                  </div>
                                  <div class="col-sm-5"><label class="label">Action</label></div>
                                </div>
                                <div
                                  class="contact-section row mb-2"
                                  *ngFor="let reletedTicket of ticketModel?.reletedTickets; let linkIndex = index"
                                >
                                  <div class="col-sm-5 pe-0">
                                    <div class="d-flex align-items-center">
                                      <span class="ticketMediumPriority me-2" *ngIf="reletedTicket?.customerId !== ticketModel?.customerId">
                                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                                      </span>
                                      <div class="w-fill-available">
                                        <ng-select
                                          name="{{ 'ticket-' + linkIndex }}"
                                          [items]="ticketList | async"
                                          bindLabel="name"
                                          bindValue="id"
                                          #ticket="ngModel"
                                          [(ngModel)]="reletedTicket.relatedTicketId"
                                          [trackByFn]="trackByFn"
                                          [minTermLength]="2"
                                          [loading]="ticketLoading"
                                          typeToSearchText="Please enter 2 or more characters"
                                          [typeahead]="ticketInput"
                                          notFoundText="No Ticket Found"
                                          placeholder="Select Ticket"
                                          appendTo="body"
                                          [clearable]="false"
                                          (change)="checkRelatedTicket(reletedTicket.relatedTicketId, linkIndex)"
                                          required
                                        >
                                        </ng-select>
                                        <sfl-error-msg
                                          [control]="ticket"
                                          [isFormSubmitted]="ticketForm?.submitted"
                                          fieldName="Ticket"
                                        ></sfl-error-msg>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="col-sm-5 d-fex align-items-center">
                                    <em
                                      class="fa fa-trash text-danger px-2 pointerReportLink"
                                      (click)="removeLinkTicket(reletedTicket.id, linkIndex)"
                                    ></em>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="mb-2"
                            *ngIf="(isDetail || isEdit) && ticketDetailModel?.reletedTicketsDetails.length && showIssueBlock"
                          >
                            <div class="mb-2" *ngFor="let reletedTicket of ticketDetailModel?.reletedTicketsDetails; let linkIndex = index">
                              <div id="fixed-table" setTableHeight class="col-12 table-responsive mt-3 table-card-view">
                                <table class="table table-hover table-bordered" aria-describedby="Ticket List">
                                  <thead>
                                    <tr>
                                      <th scope="col">Number</th>
                                      <th scope="col">Customer (Portfolio)</th>
                                      <th scope="col">Site</th>
                                      <th scope="col">Device</th>
                                      <th class="w-30" scope="col">Issue</th>
                                      <th scope="col">Opened</th>
                                      <th scope="col">Closed</th>
                                      <th scope="col">Truck Rolls</th>
                                      <th scope="col">Priority</th>
                                      <th scope="col">Status</th>
                                      <th scope="col">Action</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr *ngFor="let ticket of reletedTicket?.tickets; let index = index">
                                      <td data-title="Number">
                                        <span class="ticketMediumPriority" *ngIf="ticket?.customerId !== ticketModel?.customerId">
                                          <i class="fa fa-exclamation-triangle" aria-hidden="true"></i
                                        ></span>
                                        <a
                                          target="_blank"
                                          [routerLink]="['../' + ticket.ticketId]"
                                          [ngClass]="ticket?.customerId !== ticketModel?.customerId ? 'm-l-10' : ''"
                                          >{{ ticket.ticketNumber }}</a
                                        >
                                      </td>
                                      <td data-title="Customer (Portfolio)">{{ ticket.customerPortfolio }}</td>
                                      <td data-title="Site">{{ ticket.site }}</td>
                                      <td data-title="Device">{{ ticket.device }}</td>
                                      <td data-title="Issue" class="td-custom-width">
                                        <span
                                          class="cursor-pointer max-lines"
                                          nbTooltip="{{ ticket?.ticketSummery }}"
                                          nbTooltipPlacement="top"
                                          nbTooltipStatus="primary"
                                        >
                                          {{ ticket.ticketSummery }}
                                        </span>
                                      </td>
                                      <td data-title="Opened">{{ ticket?.openDate | date : dateFormat }}</td>
                                      <td data-title="Closed">{{ ticket?.closedDate | date : dateFormat }}</td>
                                      <td data-title="Truck Rolls" class="text-end">
                                        <span>{{ ticket?.truckRoll }} </span>
                                      </td>
                                      <td data-title="Priority" class="text-center">
                                        <img
                                          *ngIf="ticket.ticketPriority === 'High'"
                                          nbTooltip="High"
                                          nbTooltipStatus="primary"
                                          src="assets/images/High.svg"
                                          alt="High"
                                        />
                                        <img
                                          alt="Medium"
                                          *ngIf="ticket.ticketPriority === 'Medium'"
                                          nbTooltip="Medium"
                                          nbTooltipStatus="primary"
                                          src="assets/images/Medium.svg"
                                        />
                                        <img
                                          alt="Low"
                                          *ngIf="ticket.ticketPriority === 'Low'"
                                          nbTooltip="Low"
                                          nbTooltipStatus="primary"
                                          src="assets/images/Low.svg"
                                        />
                                      </td>
                                      <td data-title="Status" class="text-center">
                                        {{ ticket?.ticketStatus }}
                                      </td>
                                      <td data-title="Action">
                                        <a class="text-danger px-2 listgrid-icon" (click)="removeLinkTicket(ticket.id, linkIndex, index)">
                                          <em
                                            class="fa fa-trash text-danger ms-auto cursor-pointer"
                                            nbtooltip="Delete Link Ticket"
                                            nbTooltipPlacement="top"
                                            nbTooltipStatus="danger"
                                          ></em>
                                        </a>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </nb-accordion-item-body>
                  </nb-accordion-item>
                </nb-accordion>
              </div>
            </div>
          </div>
          <div class="row mt-3" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
            <div class="col-12">
              <div class="borderBottom pb-3">
                <nb-accordion>
                  <nb-accordion-item [expanded]="deviceInformation" class="border-bottom">
                    <nb-accordion-item-header class="accordion_head">
                      RMA
                      <button
                        nbButton
                        status="primary"
                        size="medium"
                        type="button"
                        id="workLog"
                        class="addStyle"
                        (click)="$event.stopPropagation(); openRmaModel('create')"
                        *ngIf="isEdit || isCreate"
                      >
                        Add RMA
                      </button>
                    </nb-accordion-item-header>
                    <nb-accordion-item-body>
                      <div *ngIf="!ticketModel?.ticketRMAs?.length">No RMA information</div>
                      <div *ngIf="ticketModel?.ticketRMAs?.length" class="m-w-1000">
                        <div class="row">
                          <div class="col-2 pe-0">
                            <label class="label">Device Serial Number</label>
                          </div>
                          <div class="col-2 pe-0">
                            <label class="label">Return Tracking Number</label>
                          </div>
                          <div class="col-1 pe-0">
                            <label class="label">Start Date</label>
                          </div>
                          <div class="col-1 pe-0">
                            <label class="label">Complete Date</label>
                          </div>
                          <div class="col-2 pe-0 text-center">
                            <label class="label">Device Name</label>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <label class="label">Manufacturer</label>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <label class="label">RMA Number</label>
                          </div>
                          <div class="col-1 pe-0 text-center">
                            <label class="label">RMA Complete</label>
                          </div>
                          <div class="col-1 pe-0 text-center" *ngIf="isEdit">
                            <label class="label">Actions</label>
                          </div>
                        </div>
                        <div class="row mt-3" *ngFor="let item of ticketModel?.ticketRMAs; let i = index">
                          <ng-container *ngIf="!item.isDeleted">
                            <div class="col-2 pe-0">
                              <ng-container *ngIf="item?.deviceSerialNumber; else noSerialNumber">
                                <div nbTooltip="{{ item?.deviceSerialNumber }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
                                  <sfl-read-more [content]="item?.deviceSerialNumber"></sfl-read-more>
                                </div>
                              </ng-container>
                              <ng-template #noSerialNumber>
                                {{ item.deviceAttachments.length ? 'Attached' : '-' }}
                              </ng-template>
                            </div>
                            <div class="col-2 pe-0">
                              <div>
                                <ng-container *ngIf="item?.returnTrackingNumber; else noSerialNumber">
                                  <div nbTooltip="{{ item?.returnTrackingNumber }}" nbTooltipPlacement="top" nbTooltipStatus="primary">
                                    <sfl-read-more [content]="item?.returnTrackingNumber"></sfl-read-more>
                                  </div>
                                </ng-container>
                                <ng-template #noSerialNumber>
                                  {{ item.returnAttachments.length ? 'Attached' : '-' }}
                                </ng-template>
                              </div>
                            </div>
                            <div class="col-1 pe-0">
                              <div>{{ item?.startDate ? (item?.startDate | date : dateFormat) : '-' }}</div>
                            </div>
                            <div class="col-1 pe-0">
                              <div>{{ item?.completeDate ? (item?.completeDate | date : dateFormat) : '-' }}</div>
                            </div>
                            <div class="col-2 pe-0 text-center">
                              <div>{{ item?.deviceName ? item?.deviceName : '-' }}</div>
                            </div>
                            <div class="col-1 pe-0 text-center">
                              <span>{{ item?.deviceMFG ? item.deviceMFG : '-' }}</span>
                            </div>
                            <div class="col-1 pe-0 text-center">
                              <span class="w-100 d-inline-block text-break">{{ item?.rmaNumber ? item?.rmaNumber : '-' }}</span>
                            </div>
                            <div class="col-1 pe-0 text-center">
                              <span>{{ item?.isRMAComplete ? 'Yes' : 'No' }}</span>
                            </div>
                            <div class="col-1 pe-0 text-center" *ngIf="isCreate || isEdit">
                              <div class="d-flex justify-content-center">
                                <a class="px-2 ms-3 listgrid-icon">
                                  <em
                                    class="fa fa-edit"
                                    nbTooltip="Edit"
                                    (click)="openRmaModel('edit', i)"
                                    nbTooltipPlacement="top"
                                    nbTooltipStatus="primary"
                                  >
                                  </em>
                                </a>
                                <a class="text-danger listgrid-icon">
                                  <em
                                    class="fa fa-trash"
                                    nbTooltip="Delete"
                                    (click)="deleteRmaDetails(i)"
                                    nbTooltipPlacement="top"
                                    nbTooltipStatus="danger"
                                  ></em>
                                </a>
                              </div>
                            </div>
                          </ng-container>
                        </div>
                      </div>
                    </nb-accordion-item-body>
                  </nb-accordion-item>
                </nb-accordion>
              </div>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-12">
              <div class="borderBottom pb-3">
                <nb-accordion>
                  <nb-accordion-item [expanded]="attachments" class="border-bottom">
                    <nb-accordion-item-header class="accordion_head"> JHA Details </nb-accordion-item-header>
                    <nb-accordion-item-body>
                      <div class="row" *ngIf="this.jhaDetail.length">
                        <div class="col-2" *ngFor="let item of getTypeBaseFile(this.jhaDetail, false); let index = index">
                          <div>
                            <a *ngIf="item.url" href="{{ item.url }}" target="_blank">
                              <img class="file-img cursor-pointer image-ticket-page" src="./assets/images/jha.png" alt="ticket-file" />
                            </a>
                            <span *ngIf="!item.url">
                              <img class="file-img cursor-pointer image-ticket-page" src="./assets/images/jha.png" alt="ticket-file" />
                            </span>
                          </div>
                          <div class="d-flex mt-2">
                            <label
                              class="imageFilename cursor-pointer"
                              nbTooltip="{{ item?.name }}"
                              nbTooltipPlacement="top"
                              nbTooltipStatus="primary"
                              >{{ item?.name }}</label
                            >
                            <span class="ms-auto cursor-pointer">
                              <em
                                nbtooltip="Download"
                                nbtooltipplacement="top"
                                nbtooltipstatus="text-primary"
                                aria-hidden="true"
                                class="fa fa-download text-primary"
                                (click)="downloadJhaReport(item?.id, item?.name)"
                              ></em>
                              <em
                                (click)="deleteJhaReport(item.id)"
                                nbtooltip="Delete"
                                nbtooltipplacement="top"
                                nbtooltipstatus="text-danger"
                                aria-hidden="true"
                                class="fa fa-times-circle text-danger ps-2"
                              ></em>
                            </span>
                          </div>
                        </div>
                      </div>
                      <span *ngIf="this.jhaDetail.length === 0"> No files found. </span>
                    </nb-accordion-item-body>
                  </nb-accordion-item>
                </nb-accordion>
              </div>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-12">
              <div class="borderBottom pb-3">
                <nb-accordion
                  [ngClass]="{ 'h-100': estimateInformation }"
                  [nbSpinner]="estimationLoading"
                  nbSpinnerStatus="primary"
                  nbSpinnerSize="large"
                >
                  <nb-accordion-item
                    [expanded]="estimateInformation"
                    (collapsedChange)="accordionChange($event, 'estimateInformation')"
                    class="border-bottom"
                  >
                    <nb-accordion-item-header class="accordion_head d-flex justify-content-between align-items-center">
                      <span>Approvals and Estimates</span>
                      <div class="d-flex gap-2" *ngIf="isCreate || isEdit">
                        <button
                          nbButton
                          status="primary"
                          size="medium"
                          type="button"
                          id="workLog"
                          class="addStyle me-2"
                          *ngIf="checkAuthorisationsFn([roleType.PORTFOLIOMANAGER, roleType.ADMIN, roleType.DIRECTOR])"
                          (click)="$event.stopPropagation(); onAddApproval(null, true, null)"
                        >
                          Add Approval
                        </button>
                        <button
                          nbButton
                          status="primary"
                          size="medium"
                          type="button"
                          id="workLog"
                          class="addStyle"
                          (click)="$event.stopPropagation(); onAddEstimate(ticketModel, 'create', null)"
                        >
                          Add Estimate
                        </button>
                      </div>
                    </nb-accordion-item-header>
                    <nb-accordion-item-body>
                      <div *ngIf="estTableData.length === 0 || estApprovalData.length === 0" class="text-center">
                        No Approval or Estimation
                      </div>
                      <div class="m-w-550 estimation-table" *ngIf="estTableData.length > 0">
                        <table class="table table-borderless">
                          <thead>
                            <tr>
                              <th>Estimate Number</th>
                              <th>Additional Hours</th>
                              <th>Estimate Total</th>
                              <th>Status</th>
                              <th>Customer PO</th>
                              <th>Approved By</th>
                              <th>Approved On</th>
                              <th>Attachment</th>
                              <th *ngIf="isCreate || isEdit || (isDetail && checkAuthorisationsFn([roleType.CUSTOMER]))">Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            <ng-container *ngFor="let item of estTableData; let i = index">
                              <tr *ngIf="!item?.isDeleted">
                                <td>{{ item.estNumber }}</td>
                                <td>{{ removeComma(item.additionalHours | number : '1.2-2') }}</td>
                                <td>$ {{ item.estimateTotal | number : '1.2-2' }}</td>
                                <td>{{ getStatusLabel(item.estimateStatus) }}</td>
                                <td>{{ item.customerPO }}</td>
                                <td>{{ item.approvedByName }}</td>
                                <td>{{ item.approvedOn | date : dateFormat }}</td>
                                <td>
                                  <span *ngIf="item?.ticketEstimateAttachments">
                                    <label
                                      class="text-pre-wrap"
                                      *ngIf="
                                        item?.ticketEstimateAttachments[0]?.fileName && !item?.ticketEstimateAttachments[0]?.documentUrl
                                      "
                                    >
                                      {{ item.ticketEstimateAttachments[0].fileName }}
                                    </label>
                                    <label
                                      class="text-pre-wrap text-decoration-underline cursor-pointer"
                                      *ngIf="
                                        item?.ticketEstimateAttachments[0]?.fileName && item?.ticketEstimateAttachments[0]?.documentUrl
                                      "
                                      (click)="goToLink(item?.ticketEstimateAttachments[0]?.documentUrl)"
                                    >
                                      {{ item.ticketEstimateAttachments[0].fileName }}
                                    </label>
                                  </span>
                                </td>
                                <td>
                                  <div class="ms-auto">
                                    <em
                                      *ngIf="isCreate || isEdit || (isDetail && checkAuthorisationsFn([roleType.CUSTOMER]))"
                                      (click)="onAddEstimate(item, 'edit', i)"
                                      class="fa fa-edit text-primary"
                                      nbTooltip="Edit"
                                      nbTooltipPlacement="top"
                                      nbTooltipStatus="primary"
                                    >
                                    </em>
                                    <em
                                      *ngIf="isCreate || isEdit"
                                      class="fa fa-trash text-danger ps-2"
                                      nbTooltip="Delete"
                                      nbTooltipPlacement="top"
                                      nbTooltipStatus="danger"
                                      (click)="onClickRemoveRecord(item, i)"
                                    >
                                    </em>
                                  </div>
                                </td>
                              </tr>
                            </ng-container>
                            <tr class="borderTop pt-2">
                              <td>Total</td>
                              <td>{{ removeComma(getTotalData('additionalHour') | number : '1.2-2') }}</td>
                              <td>$ {{ getTotalData('estimateTotal') | number : '1.2-2' }}</td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td *ngIf="isCreate || isEdit"></td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div
                        *ngIf="
                          checkAuthorisationsFn([[roleType.PORTFOLIOMANAGER, roleType.MANAGER, roleType.ADMIN, roleType.DIRECTOR]]) ||
                          estApprovalData.length > 0
                        "
                        class="m-w-550 mt-3 estimation-table"
                      >
                        <table class="table table-borderless">
                          <thead>
                            <tr>
                              <th>Approval</th>
                              <th>Approval Sent</th>
                              <th>Submitted By</th>
                              <th>Status</th>
                              <th>Approved By</th>
                              <th>Approved On</th>
                              <th>Denied By</th>
                              <th>Denied On</th>
                              <th
                                *ngIf="
                                  isCreate || isEdit || (isDetail && checkAuthorisationsFn([[roleType.PORTFOLIOMANAGER, roleType.MANAGER]]))
                                "
                              >
                                Action
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            <ng-container *ngFor="let approval of estApprovalData; let i = index">
                              <tr *ngIf="!approval?.isDeleted">
                                <td>{{ i + 1 }}</td>
                                <td>{{ approval.createdDate | date : dateFormat }}</td>
                                <td>{{ approval.createdByName }}</td>
                                <td>{{ getStatusNameById(approval.ticketEstimateApprovalStatusID) }}</td>
                                <td>{{ approval.ticketEstimateApprovalStatusID === 3 ? approval.actionByName : '--' }}</td>
                                <td>
                                  {{ approval.ticketEstimateApprovalStatusID === 3 ? (approval.actionOn | date : dateFormat) : '--' }}
                                </td>
                                <td>{{ approval.ticketEstimateApprovalStatusID === 2 ? approval.actionByName : '--' }}</td>
                                <td>
                                  {{ approval.ticketEstimateApprovalStatusID === 2 ? (approval.actionOn | date : dateFormat) : '--' }}
                                </td>
                                <td>
                                  <div class="ms-auto">
                                    <em
                                      *ngIf="
                                        isCreate ||
                                        (isEdit &&
                                          checkAuthorisationsFn([
                                            roleType.PORTFOLIOMANAGER,
                                            roleType.MANAGER,
                                            roleType.ADMIN,
                                            roleType.DIRECTOR
                                          ]))
                                      "
                                      (click)="onAddApproval(approval, false, i)"
                                      class="fa fa-edit text-primary"
                                      nbTooltip="Edit"
                                      nbTooltipPlacement="top"
                                      nbTooltipStatus="primary"
                                    >
                                    </em>
                                    <em
                                      *ngIf="
                                        isCreate ||
                                        (isEdit && checkAuthorisationsFn([roleType.PORTFOLIOMANAGER, roleType.ADMIN, roleType.DIRECTOR]))
                                      "
                                      class="fa fa-trash text-danger ps-2"
                                      nbTooltip="Delete"
                                      nbTooltipPlacement="top"
                                      nbTooltipStatus="danger"
                                      (click)="onClickRemoveRecord(approval, i, true)"
                                    >
                                    </em>
                                  </div>
                                </td>
                              </tr>
                            </ng-container>
                          </tbody>
                        </table>
                      </div>
                    </nb-accordion-item-body>
                  </nb-accordion-item>
                </nb-accordion>
              </div>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-12">
              <div class="borderBottom">
                <div class="borderBottom pb-3">
                  <nb-accordion [ngClass]="{ 'h-100': otherInformation }">
                    <nb-accordion-item
                      [expanded]="otherInformation"
                      (collapsedChange)="accordionChange($event, 'otherInformation')"
                      class="border-bottom"
                    >
                      <nb-accordion-item-header class="accordion_head"> Other Information </nb-accordion-item-header>
                      <nb-accordion-item-body>
                        <div class="row">
                          <div class="col-12 col-lg-6 mb-sm-3 mb-md-0">
                            <div class="form-control-group">
                              <label class="label" for="input-address">Customer Contact Email</label>
                              <div>
                                <span *ngIf="isDetail && ticketDetailModel?.customerContact.length">
                                  <a
                                    *ngFor="let item of ticketDetailModel?.customerContact; let i = index"
                                    href="mailto: {{ item.customerEmail }}"
                                  >
                                    {{ item?.customerEmail }}<span *ngIf="i < ticketDetailModel?.customerContact.length - 1">,</span>
                                  </a>
                                </span>
                                <span *ngIf="isDetail && !ticketDetailModel?.customerContact.length"> - </span>
                                <textarea
                                  nbInput
                                  rows="7"
                                  name="contact"
                                  id="contact"
                                  #contact="ngModel"
                                  [(ngModel)]="customerContact"
                                  *ngIf="isCreate || isEdit"
                                  fullWidth
                                >
                                </textarea>
                              </div>
                            </div>
                            <div class="mt-1" *ngIf="isCreate || isEdit">
                              <label class="label">* Separate multiple emails with commas.</label>
                            </div>
                          </div>
                          <div class="col-12 col-lg-6 mb-3 mb-sm-0">
                            <div class="row">
                              <div class="col-12 mb-3">
                                <div class="form-control-group">
                                  <label class="label" for="input-address">OEM Case Number</label>
                                  <span *ngIf="isDetail">{{ ticketDetailModel.oemCaseNumber || '-' }}</span>
                                  <div *ngIf="isCreate || isEdit">
                                    <input
                                      nbInput
                                      name="oemNumber"
                                      #oemNumber="ngModel"
                                      [(ngModel)]="ticketModel.oemCaseNumber"
                                      placeholder="OEM Case Number"
                                      maxlength="128"
                                      fullWidth
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </nb-accordion-item-body>
                    </nb-accordion-item>
                  </nb-accordion>
                </div>
              </div>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-12">
              <div class="borderBottom">
                <div class="borderBottom pb-3">
                  <nb-accordion
                    class="mb-2 ticket-note"
                    [nbSpinner]="ticketNotesLoading"
                    *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                  >
                    <nb-accordion-item [expanded]="true" class="border-bottom">
                      <nb-accordion-item-header class="accordion_head">Ticket Notes </nb-accordion-item-header>
                      <nb-accordion-item-body>
                        <sfl-notes-listing
                          [entityId]="ticketId"
                          [entityTypeId]="entityTypeId"
                          [entityTypeName]="entityTypeName"
                          [isEntityEditMode]="isEdit"
                          [isEntityViewMode]="isDetail"
                          [isEntityCreateMode]="isCreate"
                          (notesListingLoadingEvent)="ticketNotesLoading = $event"
                        ></sfl-notes-listing>
                      </nb-accordion-item-body>
                    </nb-accordion-item>
                  </nb-accordion>
                </div>
              </div>
            </div>
          </div>
          <div class="row mt-3" *ngIf="isDetail || isEdit">
            <div class="col-12">
              <nb-tabset fullWidthv (changeTab)="changeTab($event)">
                <nb-tab
                  *ngIf="ticketModel?.hasExclusion"
                  tabTitle="Exclusions"
                  [nbSpinner]="exclusionsLoading"
                  nbSpinnerStatus="primary"
                  nbSpinnerSize="large"
                >
                  <div class="row">
                    <div class="col-12">
                      <div class="form-control-group">
                        <div class="d-flex pe-0">
                          <button
                            type="button"
                            nbButton
                            status="primary"
                            size="small"
                            class="ms-auto"
                            (click)="addExclusion(null)"
                            *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                          >
                            Add Exclusion
                          </button>
                        </div>
                        <div class="row mb-3" *ngIf="ticketModel?.hasExclusion">
                          <div class="col-12 pt-2 ps-0 pe-0">
                            <label class="label">Exclusions</label>
                            <strong>{{ ticketModel?.hasExclusion === true ? 'Yes' : 'No' }}</strong>
                          </div>
                        </div>
                        <div *ngIf="(isDetail || isEdit) && ticketDetailModel?.ticketExclusions?.length" class="mt-2">
                          <div
                            class="row mb-3"
                            *ngFor="let item of ticketDetailModel?.ticketExclusions; let i = index"
                            [ngClass]="{ borderTop: i }"
                          >
                            <div class="col-12 pt-2 ps-0 pe-0 d-flex" *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])">
                              <strong>Exclusion #{{ i < 10 ? '0' + (i + 1) : i + 1 }}</strong>
                              <div class="ms-auto cursor-pointer">
                                <em
                                  class="fa fa-edit text-primary"
                                  nbTooltip="Edit"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="primary"
                                  (click)="addExclusion(item)"
                                ></em>
                                <em
                                  class="fa fa-trash text-danger ps-2"
                                  nbTooltip="Delete"
                                  nbTooltipPlacement="top"
                                  nbTooltipStatus="danger"
                                  (click)="deleteExclusion(item?.id)"
                                ></em>
                              </div>
                            </div>
                            <div class="col-3 ps-0 pe-0 pt-2">
                              <label class="label">Exclusion From</label>
                              <strong>{{ item?.from | date : dateFormat }}</strong>
                            </div>
                            <div class="col-3 ps-0 pe-0 pt-2">
                              <label class="label">Exclusion To</label>
                              <strong>{{ item?.to | date : dateFormat }}</strong>
                            </div>
                            <div class="col-6 ps-0 pe-0 pt-2">
                              <label class="label">Notes</label>
                              <span [innerText]="item?.notes"></span>
                            </div>
                          </div>
                        </div>
                        <span *ngIf="!ticketDetailModel?.ticketExclusions?.length">No exclusion details found.</span>
                      </div>
                    </div>
                  </div>
                </nb-tab>
                <nb-tab tabTitle="Comments" [nbSpinner]="commentLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
                  <div class="row">
                    <div class="col-12">
                      <ul>
                        <li class="comment-section mt-2" *ngFor="let item of ticketComments">
                          <div class="float-end cursor-pointer" *ngIf="item?.userId === userId">
                            <em
                              class="fa fa-edit text-primary px-2"
                              nbTooltip="Edit"
                              nbTooltipPlacement="top"
                              nbTooltipStatus="primary"
                              (click)="onAddComment(item, true)"
                            ></em>
                            <em
                              class="fa fa-trash text-danger px-2"
                              nbTooltip="Delete"
                              nbTooltipPlacement="top"
                              nbTooltipStatus="danger"
                              (click)="deleteComment(item?.id)"
                            ></em>
                          </div>
                          <div>
                            <strong>{{ item?.userName }}</strong>
                            <span class="label ms-2"> added a comment</span>
                            <span class="label ms-2"> - </span>
                            <span class="label ms-2">{{ item?.createdDate | date : dateTimeFormat }}</span>
                            <span class="label ms-2" *ngIf="item?.isEdited"> - </span>
                            <span class="label text-danger ms-2" *ngIf="item?.isEdited"> edited </span>
                            <div [innerText]="item?.comment"></div>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div class="row col-12 text-center" *ngIf="!ticketComments.length">
                    <label>No comment found</label>
                  </div>
                </nb-tab>
                <nb-tab
                  tabTitle="History"
                  [nbSpinner]="actionLoading"
                  nbSpinnerStatus="primary"
                  nbSpinnerSize="large"
                  *ngIf="!checkAuthorisationsFn([roleType.CUSTOMER])"
                >
                  <div class="row">
                    <div class="col-12 history-section">
                      <nb-accordion class="mb-3" *ngFor="let item of ticketActions">
                        <nb-accordion-item [expanded]="historyAccordion" class="border-bottom">
                          <nb-accordion-item-header class="accordion_head">
                            <strong>{{ item?.username }}</strong>
                            <div class="label mb-0 ms-2">{{ item?.action }}</div>
                            <span class="label mb-0 ms-2"> - </span>
                            <span class="label mb-0 ms-2">{{ item?.createdDate | date : dateTimeFormat }}</span>
                          </nb-accordion-item-header>
                          <nb-accordion-item-body>
                            <div class="mt-2" *ngIf="item?.actionSummary">
                              <div class="row">
                                <div class="col-2"><label class="label mb-0">Field</label></div>
                                <div class="col-5 text-center"><label class="label mb-0">Original Value</label></div>
                                <div class="col-5 text-center"><label class="label mb-0">New Value</label></div>
                              </div>
                              <div class="mt-2" *ngFor="let actionSummary of item?.actionSummaryJson">
                                <div class="row">
                                  <div class="col-2">{{ actionSummary?.Label }}</div>
                                  <div class="col-5 text-center">{{ actionSummary?.Value?.Old || '-' }}</div>
                                  <div class="col-5 text-center">{{ actionSummary?.Value?.New || '-' }}</div>
                                </div>
                              </div>
                            </div>
                          </nb-accordion-item-body>
                        </nb-accordion-item>
                      </nb-accordion>
                    </div>
                  </div>
                  <div class="row col-12 text-center" *ngIf="!ticketActions.length">
                    <label>No actions found</label>
                  </div>
                </nb-tab>
              </nb-tabset>
            </div>
          </div>
        </div>
        <div class="col-12 mt-2" *ngIf="isCreate || isEdit">
          <button
            nbButton
            status="primary"
            size="medium"
            type="button"
            id="siteSubmit"
            (click)="isSendEmail = true; ticketForm.onSubmit()"
            class="float-end m-1"
          >
            Save & Send Email
          </button>
          <button
            nbButton
            status="primary"
            size="medium"
            type="button"
            id="siteSubmit"
            (click)="isSendEmail = false; ticketForm.onSubmit()"
            class="float-end m-1"
          >
            Save
          </button>
          <button nbButton status="basic" type="button" routerLink="/entities/ticket" size="medium" class="float-end m-1">Cancel</button>
        </div>
      </div>
    </form>
  </nb-card-body>
</nb-card>
<ng-template #no_file_content>No files found.</ng-template>

<ng-template #addAlertTemplate>
  <div class="alert-box">
    <div class="modal-header">
      <h4 class="modal-title">Add Alert(s) in Ticket</h4>
      <button type="button" class="close" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>Based on the configuration below are the Alerts to add</p>
      <div id="fixed-table" setTableHeight class="col-12 table-responsive mt-3 table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Ticket List">
          <thead>
            <tr>
              <th scope="col" class="text-center">
                <div>
                  <nb-checkbox [(ngModel)]="isMasterSel" (change)="selectDeselectAll()" [checked]="getSelectedValue()"></nb-checkbox>
                </div>
              </th>
              <th scope="col">Device Name</th>
              <th scope="col">Alert Start Date</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let alert of alertsList">
              <td data-title="Select" class="text-center">
                <nb-checkbox [(ngModel)]="alert.isSelected" [checked]="alert.isSelected"></nb-checkbox>
              </td>
              <td data-title="Device Name">{{ alert?.deviceName }}</td>
              <td data-title="Alert Start Date">{{ alert?.startDate }}</td>
            </tr>
            <tr *ngIf="!alertsList.length">
              <td colspan="3" class="no-record text-center">No Data Found</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="modal-footer">
      <button status="primary" size="medium" (click)="submitAlertsForTickets()" nbButton>Submit</button>
      <button status="basic" size="medium" (click)="onCancel()" nbButton>Cancel</button>
    </div>
  </div>
</ng-template>
