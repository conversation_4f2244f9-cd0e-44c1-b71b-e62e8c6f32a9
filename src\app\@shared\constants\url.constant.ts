export class ApiUrl {
  public static readonly login = '/authenticate';
  public static readonly refreshToken = '/refresh-token';
  public static readonly account = '/account';
  public static readonly validateToken = '/validate-token';
  public static readonly FORGOT_PASSWORD = '/account/reset-password/init';
  public static readonly RESET_PASSWORD = '/account/reset-password/finish';
  public static readonly CHECK_RESET_PASSWORD_TOKEN = '/account/validateresetpasswordlink';
  public static readonly GET_CHANGE_PASSWORD_USER = '/account/change-password';
  public static readonly VERSION_API = '/account/getApiVersion';
  public static readonly GET_ALL_SECURITY_QUESTIONS = '/account/getSecurityQuestion';
  public static readonly UPDATE_PREFERENCE = '/DefaultUserFilterSelection';
  // States
  public static readonly GET_STATES = '/state';

  //Notification
  public static readonly GET_ALL_NOTIFICATIONS = '/Notifications/GetAllNotifications';
  public static readonly MARK_AS_READ_UNREAD = '/Notifications/notificationMarkReadUnread';
  public static readonly GET_NOTIFICATION_COUNT = '/Notifications/getnotificationcount';
  public static readonly GET_NOTIFICATION_MORE_DETAILS = '/Notifications/getauditforupdatedticket';
  public static readonly GET_NOTIFICATION_PREFERENCE_SECTION = '/Notifications/getusernotificationpreference';
  public static readonly UPDATE_NOTIFICATION_PREFERENCE = '/Notifications/usernotificationpreference';
  public static readonly ALLOW_NOTIFICATIONS = '/Notifications/userAllowNotification';
  // User
  public static readonly USERS = '/user';
  public static readonly CREATE_USER = '/user/create';
  public static readonly GET_ID_USER = '/user/get/';
  public static readonly DELETE_USER = '/user/delete/';
  public static readonly ACTIVE_USER = '/user/enableuser';
  public static readonly EDIT_USER = '/user';
  public static readonly EDIT_USER_PROFILE = '/user/UpdateUserProfile';
  public static readonly GET_ROLES = '/role';
  public static readonly GET_PORTFOLIO = '/portfolio/GetPortfolios?isActive=';
  public static readonly USER_CHANGE_PASSWORD = '/changepasswordbyadmin';
  public static readonly GET_ALL_USER = '/user/getAllUsers';
  public static readonly GET_ALL_PORTFOLIOMANGER = '/user/getportfoliousers';
  public static readonly GET_ALL_ANALYST = '/user/getanalystusers';
  public static readonly GET_ALL_TIMEZONE = '/user/getTimeZone';
  public static readonly GET_ALL_DATA_SOURCE = '/site/GetAllAutomationDataSourceNPartner';
  public static readonly GET_CUSTOMER_APIS = '/customerapi/GetCustomerAPIs';
  public static readonly GET_CUSTOMER_HISTORY_LOG = '/customerapi/GetCustomerAPIActionLogs';
  public static readonly DISABLE_CUSTOMER_APIS = '/customerapi/DisableCustomerAPI';
  public static readonly ENABLE_CUSTOMER_APIS = '/customerapi/EnableCustomerAPI';
  public static readonly REGENERATE_CUSTOMER_APIS = '/customerapi/RegenerateCustomerAPI';
  public static readonly REGENERATE_CUSTOMER_USER_API = '/customer/RegenerateCustomerAPI';
  public static readonly CUSTOMER_USER_API = '/customerapi/CustomerUserAPI';
  public static readonly CUSTOMER_API_DASHBOARD = '/customerapi/CustomerAPIDashboard';
  public static readonly CUSTOMER_API_USERS = '/customerapi/GetCustomerAPIUsers';
  public static readonly GET_DEFAULT_FILTER_SECTION = '/DefaultUserFilterSelection';
  public static readonly UPDATE_USER_AVATAR = '/user/updateuseravatar';
  public static readonly ENABLE_DISABLE_TWO_FACTOR = '/user/enableDisableTwoFactor';
  public static readonly GET_USER_SECURITY_QUESTIONS = '/user/getUsersSecurityQuestions';
  public static readonly ADD_UPDATE_USER_SECURITY_QUESTIONS = '/user/addUpdateUserSecurityQuestions';
  public static readonly GET_USER_AUTHENTICATION_AUDIT_LOGS = '/user/getAuthenticationAuditLog/';
  public static readonly GET_USERS_BY_ROLES = '/user/getUsersByRoles';
  public static readonly GET_USER_FILTERS = '/user/getUserFilters';

  // logs
  public static readonly GET_LOGS = '/logs?startDate=';
  public static readonly DELETE_LOGS = '/logs?startDate=';

  // portfolio
  public static readonly PORTFOLIOS = '/portfolio';
  public static readonly PORTFOLIOS_BY_FILTER = '/portfolio/portfoliobyfilter';
  public static readonly CREATE_PORTFOLIO = '/portfolio';
  public static readonly GET_BY_ID_PORTFOLIO = '/portfolio/';
  public static readonly ACTIVE_PORTFOLIO = '/portfolio/enableportfolio';
  public static readonly DELETE_PORTFOLIOS = '/portfolio/';
  public static readonly GET_ALL_PORTFOLIO_BY_CUSTOMERID = '/customer/getportfoliobycustomer?isActive=';
  public static readonly GET_ALL_PORTFOLIO_BY_CUSTOMERID1 = '/customer/getportfoliobycustomer?isActive=';
  public static readonly DELETE_PORTFOLIOS_CONTACT = '/portfolio/deleteportfoliocustomercontact';
  public static readonly GET_PORTFOLIO_ARCHIVE = '/portfolio/getPortfolioArchiveData';
  public static readonly UPDATE_PORTFOLIO_ARCHIVE_STATUS = '/portfolio/updatePortfolioArchiveStatus';

  // site
  public static readonly CREATE_SITE = '/site';
  public static readonly SITES_BY_FILTER = '/site/getallbyfilter';
  public static readonly ACTIVE_SITE = '/site/enablesite';
  public static readonly GET_BY_ID_SITE = '/site/';
  public static readonly DELETE_SITES = '/site/';
  public static readonly DELETE_DATASOURCE = '/site/updateAutomationSiteStatus';
  public static readonly GET_ALL_SITE = '/site/getall';
  public static readonly GET_ALL_SITE_BY_PORTFOLIOID = '/site/getbyportfolioid?isActive=';
  public static readonly SITE_IMAGE_UPLOAD_IMAGES = '/site/uploadsiteimage';
  public static readonly SITE_IMAGE_DELETE_IMAGES = '/site/deletesiteimage?siteImageId=';
  public static readonly DELETE_SITE_CONTACT = '/site/deletesitecustomercontact';
  public static readonly GET_PERFORMANCE = '/sitedevice/getdevicebytype';
  public static readonly GET_DATASOURCE = '/site/GetAutomationDataSourceDetail';
  public static readonly GET_SITELIST = '/site/GetAutomationSiteList';
  public static readonly GET_PRIMARYDEVICES = '/site/GetAutomationSitePrimaryDevices';
  public static readonly GET_AUTOMATION_DATASOURCE = '/site/GetAutomationDataSourceDetail';
  public static readonly GET_SITE = '/site/GetAutomationSiteDetailAsPerDataSource';
  public static readonly GET_AUTOMATION_DEVICE_LIST = '/sitedevice/getAutomationDeviceList';
  public static readonly GET_AUTOMATION_DEVICE_LIST_UPDATE = '/sitedevice/updateAutomationDeviceList';
  public static readonly GET_DATA_SOURCE_LIST = '/site/dataSourcePartner';
  public static readonly GET_AUTOMATION_SITE_LIST = '/site/automationSiteDropdown';
  public static readonly GET_SITE_TIMEZONE_LONGITUDE_LATITUDE = '/site/getsitetimezonebaseonlogitudelatitude';
  public static readonly GET_FIPS_CODE_BY_LONGITUDE_LATITUDE = '/site/getfipscodebasedonlogitudelatitude';
  public static readonly GET_SITE_HISTORY_DATA = '/site/getSiteAuditLog';
  public static readonly GET_SITE_NOTE = '/site/getSiteNote';
  public static readonly GET_ALL_SITE_NOTES = '/site/getAllSiteNote';
  public static readonly ADD_SITE_NOTE = '/site/addSiteNote';
  public static readonly UPDATE_SITE_NOTE = '/site/updateSiteNote';
  public static readonly DELETE_SITE_NOTE = '/site/deleteSiteNote';
  public static readonly GET_NERC_SITE_TYPE = '/site/getNERCSiteType';
  public static readonly GET_QE_SERVICE_TYPE_DROPDOWN = '/site/getQEServiceTypeDropdown';
  public static readonly DOWNLOAD_SITE_TEMPLATE = '/site/downloadSiteTemplate';
  public static readonly UPLOAD_SITES = '/site/uploadSites';
  public static readonly EXPORT_SITES_TEMPLATE = '/site/exportSitesTemplate';
  public static readonly UPLOAD_SITES_UPDATES = '/site/updateSitesUpload';
  public static readonly GET_LATEST_DEVICE_VALUE = '/automation/availability/refetchSiteDeviceDataTimeRange';

  //Site Photo Library
  public static readonly PHOTO_GALLERY_UPLOAD_IMAGES = '/SitePhotoLibrary/create';
  public static readonly GET_PHOTO_GALLERY_PHOTO_IMAGES = '/SitePhotoLibrary/getSitePhotoImages';
  public static readonly PHOTO_GALLERY_DELETE_IMAGES = '/SitePhotoLibrary/delete?photoid=';
  public static readonly SELECT_PHOTO_AS_A_KEY_PHOTO = '/SitePhotoLibrary/setAsKeyImagesForSiteinfo';
  public static readonly GET_PM_PHOTO_IMAGES = '/SitePhotoLibrary/getPMSitePhotoImages';
  public static readonly MOVE_PM_PHOTO_IMAGES_TO_MASTER = '/SitePhotoLibrary/movePMImagesToMaster';
  public static readonly GET_CM_PHOTO_IMAGES = '/SitePhotoLibrary/getCMSitePhotoImages';
  public static readonly MOVE_CM_PHOTO_IMAGES_TO_MASTER = '/SitePhotoLibrary/moveCMImagesToMaster';
  public static readonly GET_SITE_ARCHIVE = '/site/getSiteArchiveData';
  public static readonly UPDATE_SITE_ARCHIVE_STATUS = '/site/updateSiteArchiveStatus';
  // assessment
  public static readonly CREATE_ASSESSMENT = '/assesment';
  public static readonly UPDATE_ASSESSMENT = '/assesment/Update';
  public static readonly ASESSMENT_BY_FILTER = '/assesment/getallbyfilter';
  public static readonly GET_ALL_FREQUENCY = '/assesment/getfrequency';
  public static readonly GET_BY_ID_ASSESSMENT = '/assesment/';
  public static readonly DELETE_ASSESSMENT = '/assesment/';
  public static readonly CHECK_BI_ENNIAL = '/assesment/checkbiennial?siteId=';
  public static readonly GET_ALL_DEFAULT_CUSTOMER = '/assesment/gettaskassessment/';
  public static readonly UPLOAD_FILE_IMPORTASSESSMENT = '/assesment/importassessment';
  public static readonly IS_VGT_SCOPE_AVAILBLE_FOR_SITE = '/assesment/IsVGTScopeAvailable';

  // workOrder
  public static readonly WORK_BY_FILTER = '/WorkOrder/getallbyfilter';
  public static readonly GET_WORK_ORDER = '/WorkOrder/getworkorder';
  public static readonly CREATE_WORK_ORDER = '/WorkOrder';
  public static readonly GET_FIELD_TECH = '/WorkOrder/getfielduser';
  public static readonly GET_ALL_FIELD_TECH = '/WorkOrder/getallfielduser';
  public static readonly GET_WORK_ORDER_STATUS = '/WorkOrder/getwostatus';
  public static readonly DELETE_WORK_ORDER = '/WorkOrder/deletewo';
  public static readonly DELETE_OEM_DOCUMENT_LINK = '/WorkOrder/deleteDocumentLink';
  public static readonly GET_ALL_WORK_ORDER_BY_SITEID = '/WorkOrder/getbysiteId/';
  public static readonly SEND_REPORT_FOR_REVIEW = '/WorkOrder/submitreporttocustomer/';
  public static readonly RESTORE_WORKORDER = '/WorkOrder/restoreworkorder/';
  public static readonly RECREATE_WORKORDER = '/WorkOrder/recreateworkorder';
  public static readonly RESTORE_ALL_WORKORDER = '/WorkOrder/restoreallwo/';
  public static readonly WORKORDER_AUDIT_LOG = '/WorkOrder/getWorkOrderAuditLogs/';
  public static readonly UNLOCK_WORKORDER = '/WorkOrder/unlockWorkOrder';
  public static readonly LOCK_WORKORDER = '/WorkOrder/lockWorkOrder';
  public static readonly WORKORDER_LOCKING_STATUS = '/WorkOrder/getWoLockingStatus';
  public static readonly GET_WORKORDER_BY_ID = '/WorkOrder/getWorkOrderById';
  // dashboard
  public static readonly GET_DASHBOARD = '/Dashboard';
  public static readonly GET_DASHBOARD_SCHEDULE_VIEW = '/Dashboard/scheduleView';
  public static readonly GET_BY_ID_DASHBOARD_WODETAIL = '/mobile/v2/getwodetail/';
  public static readonly EXPORT_DASHBOARD = '/Dashboard/getExportDataDashboard';
  public static readonly UPDATE_WO_SCHEDULE_DETAIL = '/WorkOrder/updateWOScheduleDetail';
  public static readonly EXPORT_SCHEDULE_DASHBOARD = '/Dashboard/getExportScheduleViewData';
  public static readonly IMPORT_SCHEDULE_DASHBOARD = '/Dashboard/importScheduleValueFromExcel';
  public static readonly BULK_RESCHEDULE_ACTION = '/WorkOrder/updateBulkWOScheduleDetail';

  //Hazard
  public static readonly HAZARD = '/hazards';
  public static readonly ENABLE_HAZARD = '/hazards/enablehazards';

  // dashboard
  public static readonly GET_SITE_DASHBOARD = '/Dashboard/getSiteDashboard';

  // customer
  public static readonly CREATE_CUSTOMER = '/customer/Create';
  public static readonly CUSTOMERS_BY_FILTER = '/customer/getbyfilter';
  public static readonly GET_BY_ID_CUSTOMER = '/customer/';
  public static readonly UPDATE_CUSTOMER = '/customer';
  public static readonly DELETE_CUSTOMERS = '/customer/';
  public static readonly GET_CUSTOMER = '/customer/getall';
  public static readonly GET_AVAILIBILITY_CUSTOMER = '/customer/getall';
  public static readonly CUSTOMER_OUTAGE = '/customer/getOutage';
  public static readonly ADD_UPDATE_CUSTOMER_OUTAGE = '/customer/addUpdateOutage';
  public static readonly GET_PR_GENERATE_LOGS = '/AutomationReport/getprgeneratelogs';
  public static readonly CHECK_AUTO_MAP_PR_WORK_ORDER = '/AutomationReport/checkautomapprworkorder';
  public static readonly GET_CUSTOMER_ARCHIVE = '/customer/getCustomerArchiveData';
  public static readonly UPDATE_CUSTOMER_ARCHIVE_STATUS = '/customer/updateCustomerArchiveStatus';

  // note -- Customer, Portfolio, CM_Tickets
  public static readonly GET_NOTE = '/Notes/getNote';
  public static readonly GET_ALL_NOTES = '/Notes/getAllNotes';
  public static readonly ADD_NOTE = '/Notes/addUpdateNotes';
  public static readonly UPDATE_NOTE = '/Notes/addUpdateNotes';
  public static readonly DELETE_NOTE = '/Notes/deleteNote';
  public static readonly GET_NOTES_TAGS_LIST = '/QESTFiles/getFilesTagDDL';

  // Report
  // Site Visit Report
  public static readonly GET_ALL_REPORT_LIST = '/SiteVistReport/getallbyfilter';
  public static readonly GET_ALL_IMAGE_GALLERY = '/Imagegallery/';
  public static readonly GET_ALL_JHA_IMAGE_GALLERY = '/Imagegallery/jhaimagegallery?id=';
  public static readonly GET_ALL_SITEVISIT_DATA_BY_ID = '/SiteVistReport/getReportDetail?id=';
  public static readonly SAVE_UPDATE_REPORT_LIST = '/SiteVistReport/updateReport';
  public static readonly UPLOAD_IMAGES = '/GeneralImage/uploadimage';
  public static readonly UPLOAD_RISER_IMAGES = '/riserpoles/riserpolesuploadimage';
  public static readonly DELETE_GENERALIMAGES = '/GeneralImage/deleteimageByFileId?fileId=';
  public static readonly GET_ALL_NON_CONFORM_DATA_BY_ID = '/getnonconformancebyid?id=';
  public static readonly NON_CONFORMANCE_UPLOAD_IMAGES = '/uploadncimage';
  public static readonly NON_CONFORMANCE_UPDATE = '/updatenonconformance';
  public static readonly NON_CONFORMANCE_SORTY = '/getNCByFilter?reportId=';
  public static readonly SITE_REPORT_GENERATE = '/SiteVistReport/generatefinalreport?workorderId=';
  public static readonly MVPM_REPORT_GENERATE = '/SiteVistReport/generateMVPMfinalreport?workorderId=';
  public static readonly SITE_REPORT_GENERATE_PPT = '/SiteVistReport/generatefinalreport?workorderId=';
  public static readonly NON_CONFORMANCE_CREATE = '/createnonconformance';
  public static readonly GET_COMPONENT_LIST = '/getcomponentlist';
  public static readonly GET_NC_IMAGE_COORDINATES = '/getnccoordinates?workorderId=';
  public static readonly UPDATE_NC_COORDINATES = '/updatenccoordinates';
  public static readonly SAVE_NC_UPDATED_IMAGE = '/uploadreportimage';
  public static readonly DELETE_REPORTS = '/SiteVistReport/deletereportsbywoid/';
  public static readonly GET_REPORT_SITE_IMAGE_COORDINATES = '/getreportcoordinates?reportId=';
  public static readonly SITE_MAP_CHECK_IMAGES = '/SiteVistReport/checksiteimages?workorderId=';
  public static readonly SITE_REPORT_PDF_DOWNLOAD = '/SiteVistReport/downloadPdfReport?workorderId=';
  public static readonly SITE_REPORT_PPT_DOWNLOAD = '/SiteVistReport/downloadPptReport?workorderId=';
  public static readonly UPLOAD_PPT_REPORT = '/SiteVistReport/uploadReport';
  public static readonly GET_ALL_GENERAL_IMAGES = '/GeneralImage/checkReportGIImages?id=';
  public static readonly DELETE_NONCONFORMANCE = '?id=';
  public static readonly UPDATED_EDIT_IMAGES = '/Imagegallery/saverotateimages';
  public static readonly IPM_UPLOAD_IMAGES = '/IPM/uploadimage';
  public static readonly UPLOAD_EQUIPMENT_TRANS_STATUS = '/EquipmentStatus/uploadtransdevice';
  public static readonly EQUIPMENT_UPLOAD_IMAGES = '/EquipmentStatus/uploadEquipmentImages';
  public static readonly EQUIPMENT_NAME_PLATE_IMAGE_UPLOAD_IMAGES = '/EquipmentStatus/uploadEquipmentnameplateImages';
  public static readonly UPLOAD_EQUIPMENT_MV_STATUS = '/EquipmentStatus/uploadmvdevice';
  public static readonly UPLOAD_EQUIPMENT_PR_STATUS = '/EquipmentStatus/uploadprdevice';
  public static readonly GET_ALL_EQUIPMENT_STATUS_DATA_BY_ID = '/EquipmentStatus/getbyId?id=';
  public static readonly UPLOAD_EQUIPMENT_TRANS_STATUS_UPDATE = '/EquipmentStatus/updatetransequipmentstatus';
  public static readonly UPLOAD_EQUIPMENT_PR_STATUS_UPDATE = '/EquipmentStatus/updateprequipmentstatus';
  public static readonly UPLOAD_EQUIPMENT_MV_STATUS_UPDATE = '/EquipmentStatus/updatemvequipmentstatus';
  public static readonly DELETE_EQUIPMENT_STATUS = '/EquipmentStatus/delete?id=';
  public static readonly GET_WORKORDER_LIST = '/OtherReport/getworkorders';
  public static readonly CHANGE_WORKORDER = '/OtherReport/movereport';
  public static readonly GENERATE_NEW_REPORT = '/mobile/v2/uploadreportdata';
  public static readonly CREATE_NC_BULK_TICKET = '/nonConformance/createNCBulkTicket';
  public static readonly GET_NC_COMPONENTS_LIST = '/nonConformance/getNCComponents';
  public static readonly GET_NC_ISSUE_LIST = '/nonConformance/getNCIssueObservationMaster';
  public static readonly GET_NC_ACTION_RECOMMENDATIONS = '/nonConformance/getNCActionRecommendationMaster';
  public static readonly ADD_UPDATE_NC_ISSUE = '/nonConformance/manageNCIssueObservation';
  public static readonly DELETE_NC_ISSUE = '/nonConformance/deleteNCIssueObservationById/';
  public static readonly ACTIVE_INACTIVE_NC_ISSUE = '/nonConformance/activeInActiveNCIssueObservationById/';
  public static readonly ACTIVE_INACTIVE_ACTION_RECOMMENDATION = '/nonConformance/activeInActiveNCActionRecommendationById/';
  public static readonly DELETE_NC_ACTION_RECOMMENDATION = '/nonConformance/deleteNCActionRecommendationById/';
  public static readonly ADD_UPDATE_NC_ACTION = '/nonConformance/manageNCActionRecommendation';
  public static readonly ADD_UPDATE_NC_ACTION_RECOMMENDATION = '/nonConformance/manageNCActionRecommendation';
  public static readonly ADD_UPDATE_NC_COMPONENT = '/nonConformance/manageNCComponent';
  public static readonly ACTIVE_INACTIVE__NC_COMPONENT = '/nonConformance/activeInActiveNCComponentById/';
  public static readonly DELETE_NC_COMPONENT = '/nonConformance/deleteNCComponentById/';
  public static readonly UPDATE_WORK_ORDER_STATUS = '/mobile/v1/updatewostatus';

  // VGT Report
  public static readonly GENERATE_VGT_REPORT = '/VGTReport/generatereport?workorderId=';
  public static readonly UPLOAD_VGT_IMAGES = '/VGTReport/uploadimage';
  public static readonly GET_ALL_VGT_DATA_BY_ID = '/VGTReport/getReportDetail?id=';
  public static readonly SAVE_UPDATE_VGT_REPORT_LIST = '/VGTReport/UpdateReportDetail';

  // JHA Report
  public static readonly GET_ALL_JHA_REPORT_LIST = '/jha/getallbyfilter';
  public static readonly GET_ALL_JHA_DATA_BY_ID = '/jha?id=';
  public static readonly SAVE_UPDATE_JHA_REPORT_LIST = '/jha/updatejhareport';
  public static readonly JHA_REPORT_PDF_DOWNLOAD = '/jha/downloadPdfReport?reportId=';
  public static readonly JHA_REPORT_GENERATE = '/jha/generatefinalreport?reportId=';
  public static readonly DELETE_JHA_REPORTS = '/SiteVistReport/deletereportsbyreportid?reportId=';
  public static readonly DELETE_JHA_WORKTYPE = '/jha/reportworktype';
  public static readonly DELETE_JHA_CREW = '/jha/reportpa';
  public static readonly GET_SITECONTACT = '/jha/reportsiteinfo';
  public static readonly UPLOAD_JHA = '/jha/UploadJHA3Report';
  public static readonly UPDATE_JHA = '/jha/updatejhareportdetail';
  public static readonly UPLOAD_JHA_SIGN = '/mobile/v1/uploadsignaturedata';
  public static readonly GET_JHA_UPLOAD = '/jha/getjha3reportdetail';
  public static readonly GET_JHA_CREW_LEAD = '/user/getwithoutusercustomer';
  public static readonly GET_JHA_PREVIEW = '/jha/getjha3reportstring';
  public static readonly GET_JHA_PREVIEW_BY_ID = '/jha';
  public static readonly SEND_EMAIL = '/jha/sendJHA3ReportMail';

  // Other Report
  public static readonly GET_REPORT_TYPE = '/OtherReport/getreporttype';
  public static readonly CREATE_REPORT = '/OtherReport';
  public static readonly GET_ALL_WORK_ORDER_BY_SITEID_REPORTTYPE = '/OtherReport/getworkorderbyfilter?siteId=';
  public static readonly GET_ALL_WORK_ORDER_BY_SITEID_REPORTTYPE_LISTING = '/OtherReport/getworkorderbyreportfilter';
  public static readonly GET_ALL_NEW_REPORT_LIST = '/OtherReport/getallbyfilter';
  public static readonly GET_REPORT_PORTFOLIO = '/portfolio/getportfoliosbyreportfilter';
  public static readonly GET_REPORT_SITE = '/site/getbyreportfilter';
  public static readonly VIEW_REPORT = '/OtherReport/UploadReport';
  public static readonly GET_ALL_REPORT_UPLODED = '/OtherReport?id=';
  public static readonly ALL_REPORT_PDF_DOWNLOAD = '/OtherReport/downloadpdfileByFileId?fileId=';
  public static readonly ALL_REPORT_PPT_DOWNLOAD = '/OtherReport/downloadpptFileByFileId?fileId=';
  public static readonly DELETE_UPLOADED_REPORTS = '/OtherReport/deleteByFileId?fileId=';
  public static readonly UPDATE_INCLUDEREPORT = '/Imagegallery/imageincludereprotByFileId?fileId=';
  public static readonly DELETED_REPORTS_RESTORE = '/OtherReport/restoredocByFileId?fileId=';
  public static readonly SV_DELETED_REPORTS_RESTORE = '/SiteVistReport/restorereportsbyworkorderId?id=';
  public static readonly JHA_DELETED_REPORTS_RESTORE = '/SiteVistReport/restorereportsbyreportid?reportId=';
  public static readonly DELETE_IPMIMAGES = '/IPM/deleteimageByFileId?fileId=';
  public static readonly UPLOAD_IPM_IMAGES = '/IPM/uploadimage';
  public static readonly PREVIEW_REPORT = '/SiteVistReport/getpdfstring';
  public static readonly DELETE_MERGE_NONCONFORMANCE = '/mergeanddeletecoordinate';
  public static readonly DELETE_IPMIMAGES_GROUPBY = '/IPM/deleteipm';
  public static readonly EXPORT_NC_BY_FILTER = '/exportncitem';
  public static readonly ARCHIEVE_UNARCHIEVE_REPORT_BY_ID = '/SiteVistReport/archivereportsbyreportid?reportId=';
  public static readonly BULK_ARCHIEVE_UNARCHIEVE = '/OtherReport/bulkarchive ';
  public static readonly DOWNLOAD_REPORT = '/OtherReport/downloadReport ';
  public static readonly GET_API_ERROR_LOG = '/SchedulerAuditLog/getSchedulerAuditLog';
  public static readonly GET_DATA_SOURCE_LIST_API_ERROR_PAGE = '/SchedulerAuditLog/GetDataSourcePartner';
  public static readonly GET_REPORT_ZIP_FILE_REQUEST = '/OtherReport/getreportzipfilerequest';
  public static readonly DOWNLOAD_ZIP_FILE_REQUEST = '/OtherReport/';
  public static readonly DELETE_OIL_ELECTRICAL_REPORT = '/OtherReport/deleteOilElectricalReportByFileId?fileId=';
  public static readonly ALL_REPORT_PDF_DOWNLOAD_OIL_ELECTRICAL = '/OtherReport/downloadOilElectricalfileByFileId?fileId=';
  // Equipment
  public static readonly GET_DEVICE_TYPE = '/equipment/getdevicetype';
  public static readonly CREATE_EQUIPMENT = '/equipment';
  public static readonly GET_EQUIPMENT = '/equipment/getequipmentbyfilter';
  public static readonly DELETE_EQUIPMENT = '/equipment/';
  public static readonly EDIT_EQUIPMENT = '/equipment/getbyid/';
  public static readonly UPLOAD_PDF = '/equipment/uploadDeviceManual';
  public static readonly DELETE_PDF = '/equipment/deleteDeviceManualDocument';

  // site device
  public static readonly GET_SITE_DEVICE_TYPE = '/sitedevice/getdevicetypes';
  public static readonly GET_SITE_DEVICE_MODEL = '/sitedevice/getmodel';
  public static readonly GET_SITE_DEVICE_MFG = '/sitedevice/getmfg';
  public static readonly GET_DEVICE_LIST = '/sitedevice/getall';
  public static readonly GET_DEVICE_CREATE = '/sitedevice/create';
  public static readonly GET_DEVICE_EDIT = '/sitedevice/update';
  public static readonly GET_DEVICE_TYPE_DATA = '/sitedevice/getsitedevicemodel';
  public static readonly GET_DEVICE_MODEL = '/sitedevice/getequiqmentbydevicetype';
  public static readonly GET_DEVICE_MODEL_DETAIL = '/sitedevice/getequipmentinfobyid';
  public static readonly GET_DEVICE_TYPE_BY_ID = '/sitedevice/edit';
  public static readonly DELETE_DEVICE = '/sitedevice/delete';
  public static readonly GET_SITE_DEVICE_NAME = '/sitedevice/getdevicebydevicetypeid';
  public static readonly GET_DEVICE_MVPM_TYPES = '/sitedevice/getdeviceMVPMtypes';
  public static readonly GET_DEVICE_PERFORMANCE = '/sitedevice/GetSiteDevicePerformanceUnitReccord';
  public static readonly GET_DEVICE_QE = '/sitedevice/GetAutomationQEDevices';
  public static readonly GET_DEVICE_CUSTOMER = '/sitedevice/GetAutomationQEDeviceToImport';
  public static readonly GET_DEVICE_QENAME = '/sitedevice/GetQEDevicesForSite';
  public static readonly IMPORT_DEVICES = '/sitedevice/ImportAutomationSiteDevices';
  public static readonly GET_DATA_SOURCE = '/sitedevice/getDataSourceSiteList';
  public static readonly DOWNLOAD_DEVICE_TEMPLATE = '/sitedevice/downloadDeviceTemplate';
  public static readonly IMPORT_BULK_DEVICES = '/sitedevice/importDevices';
  public static readonly EXPORT_SITE_AUTOMATION_DEVICES = '/sitedevice/exportSiteDevicesTemplate';
  public static readonly UPLOAD_SITE_AUTOMATION_DEVICES = '/sitedevice/uploadSiteDeviceUpdate';

  //Barrier
  public static readonly BARRIER = '/Barrier';
  public static readonly BARRIER_TYPE = '/Barrier?isFromList=true';

  // ticket
  public static readonly GET_TICKETS_LIST = '/ticket/getall';
  public static readonly GET_TICKETS_LIST_BY_ID = '/ticket/getallmyticket';
  public static readonly GET_TICKET_DETAIL_BY_TICKET_NUMBER = '/ticket/getbyTicketNumber';
  public static readonly GET_TICKET_DETAIL_BY_TICKET_ID = '/ticket/getbyid';
  public static readonly GET_DEVICETYPE_BY_ID = '/ticket/getdevicetypeonsiteid';
  public static readonly GET_TICKETS_COMMENT_BY_ID = '/ticketcomment';
  public static readonly GET_TICKETS_CLOSE_BY_ID = '/ticket/closeticket';
  public static readonly DELETE_TICKET_BY_TICKET_NUMBER = '/ticket/delete/';
  public static readonly GET_ALL_FIELDTECH_USERS = '/user/getfieldtechusers';
  public static readonly ADD_TICKET = '/ticket/create';
  public static readonly UPDATE_TICKET = '/ticket/update';
  public static readonly SEND_TICKET = '/ticket/sendmail';
  public static readonly ADD_ACTIVITY_LOG = '/activity';
  public static readonly DELETE_RESOLUTION_TICKET_BY_ID = '/ticketresolution/delete/';
  public static readonly TICKET_ATTACHMENT = '/ticketattachment/uploadattachment';
  public static readonly TICKET_ATTACHMENT_EST = '/ticketattachment/uploadestimateattachment';
  public static readonly TICKET_ATTACHMENT_DELETE = '/ticketattachment/deleteattachment';
  public static readonly TICKET_EST_ATTACHMENT_DELETE = '/ticketattachment/deleteestimateattachment';
  public static readonly GET_TICKETS_OPEN_BY_ID = '/ticket/openticket';
  public static readonly TICKET_DOWNLOAD = '/ticketattachment/downloadattachment/';
  public static readonly TICKET_ESTIMATE_DOWNLOAD = '/ticketattachment/downloadestimateattachment/';
  public static readonly ACTIVITY_LOG_UPDATE = '/ticketworklog/update';
  public static readonly DELETE_ACTIVITY_LOG_TICKET_BY_ID = '/activity/';
  public static readonly COMMENT_UPDATE = '/ticketcomment';
  public static readonly DELETE_COMMENT_TICKET_BY_ID = '/ticketcomment/delete/';
  public static readonly CREATE_TICKETS_EXCLUSIONS_BY_ID = '/ticketexclusion';
  public static readonly EXCLUSIONS_UPDATE = '/ticketexclusion';
  public static readonly DELETE_EXCLUSIONS_TICKET_BY_ID = '/ticketexclusion/';
  public static readonly DELETE_BULK_EXCLUSIONS_TICKET_BY_TICKET_NUMBER = '/ticketexclusion/deletebulkexclusion';
  public static readonly TICKET_COMMENT_BY_TICKET_NUMBER = '/ticketcomment/getbyticketNumber';
  public static readonly TICKET_ACTIVITY_LOG_BY_TICKET_NUMBER = '/activity/getbyTicketNumber';
  public static readonly TRUCK_ROLL_BY_TRUCK_ROLL_NUMBER = '/activity/getTruckRollGallery';
  public static readonly TRUCK_ROLL_GALLERY = '/activity/getTruckRollGalleryFiles';
  public static readonly EXPORT_TRUCK_ROLL_PDF = '/activity/exportTruckRollReport';
  public static readonly TICKET_ACTION_BY_TICKET_NUMBER = '/TicketAction/getbyticketnumber';
  public static readonly GET_ALL_TICKET_CUSTOMER = '/ticket/getallcustomer ';
  public static readonly GET_ALL_LOSS_TYPE = '/ticket/getlosstype';
  public static readonly GET_ALL_EXCLUSION_BY_TICKET_NUMBER = '/ticketexclusion';
  public static readonly CHANGE_EXCLUSION_STATUS_BY_ID = '/ticketexclusion/updateexclusionflag';
  public static readonly GET_SEARCH_TICKETS_LIST = '/ticket/getallgobelsearchticket';
  public static readonly GET_ALL_QE_USERS = '/user/getwithoutusercustomer';
  public static readonly TICKET_PDF_DOWNLOAD = '/ticket/exportTicketData/';
  public static readonly GET_ALL_CUSTOMER_CONTACT_BY_SITEID = '/ticket/getcustomercontactbysiteid/';
  public static readonly GET_ALL_LINK_TYPE = '/ticket/getrelatedticketreason';
  public static readonly GET_ALL_TICKET_NUMBER = '/ticket/getticketdrp';
  public static readonly CREATE_LINK_TYPE = '/ticket/createticketrelated';
  public static readonly DELETE_LINK_TYPE = '/ticket/deleteticketrelated';
  public static readonly GET_JHALIST = '/jha/getwoticketlist/';
  public static readonly GET_ALL_JHA = '/jha/getjhareportbysiteId?siteId=';
  public static readonly GET_ALL_JHA_ADD_ACTIVITY = '/jha/getjhareportbysiteIdanddate';
  public static readonly GET_JHA_REPORT = '/WorkOrderTicket/linkreportbyticketnumber/';
  public static readonly UPLOAD_RMA_ATTACHMENT = '/ticketRMA/uploadRMAAttachment';
  public static readonly DOWNLOAD_RMA_ATTACHMENT = '/ticketRMA/downloadRMAAttachment';
  public static readonly ADD_RMA_DETAIL = '/ticketRMA/addUpdateTicketMapRMA';
  public static readonly GET_RMA_DETAIL_LIST = '/ticketRMA/getTicketRMAByTicketNumber';
  public static readonly ADD_OUTAGE_TICKET = '/ticket/addOutageTicket';
  public static readonly GET_TRUCK_ROLL_BY_SITE = '/activity/getTruckRollBySite';
  public static readonly GET_TICKET_NUMBER_BY_TRUCK_ROLL_ID = '/activity/getTicketNumberByTruckRoll';
  public static readonly ADD_UPDATE_TICKET_ESTIMATES = '/ticket/addupdateTicketEstimate';
  public static readonly TICKET_ESTIMATE_BY_TICKET_NUMBER = '/ticket/getTicketEstimatebyTicketNumber';
  public static readonly GET_WO_INVERTER_LIST_FOR_QEST_FORMS = '/ticket/getsitedevicedetail';
  public static readonly GET_TICKET_BILLING_STATUSES = '/ticket/getTicketBillingStatus';
  public static readonly GET_ALL_TICKET_TYPE = '/ticket/getTicketType';
  public static readonly GET_ALL_TICKET_ACTIVITY_COST_TYPE = '/ticket/getCostType';
  public static readonly UPDATE_TICKET_BULK_ACTION = '/ticket/bulkUpdateTickets';
  public static readonly CREATE_TICKET_BULK_ACTION = '/ticket/bulkCreateTickets';
  public static readonly CLOSE_RE_OPEN_TICKET_BULK_ACTION = '/ticket/bulkCloseReopenTickets';
  public static readonly CONTRACTED_HOURS_TOGGLE = '/ticket/contractedHoursToggle';
  public static readonly GET_DEVICE_LIST_FOR_TICKET_ACTIVITY = '/activity/deviceListForTicketActivity';
  public static readonly CHECK_DEVICE_OUTAGE_FOR_SELECTED_TICKETS = '/ticket/checkDeviceOutageForSelectedTickets';
  public static readonly CREATE_ALERTS_TICKETS = '/ticket/createAlertsTickets';

  // Ticket Estimate Approval
  public static readonly ADD_UPDATE_TICKET_EST_APPROVAL = '/TicketEstimateApproval/addUpdateTicketEstimateApproval';
  public static readonly DELETE_EST_APPROVAL = '/TicketEstimateApproval/deleteTicketEstimateApproval/';
  public static readonly GET_APPROVAL_STATUS = '/TicketEstimateApproval/getAllTicketEstimateApprovalStatus';
  public static readonly GET_EST_APPROVAL_LIST_BY_TICKET_NUMBER = '/TicketEstimateApproval/getTicketEstimateApprovalsByTicketNumber/';
  public static readonly APPROVE_DENIED_TICKET_EST_APPROVAL = '/TicketEstimateApproval/validateNPerformAction';

  // CM Report
  public static readonly GET_AUDIT_DISPATCH_DETAIL = '/ticketauditdispatch';
  public static readonly SEND_AUDIT_DISPATCH_REPORT_LIST = '/ticketauditdispatch/AuditReportSend';
  public static readonly GET_TICKET_MAP_REPORT_LIST = '/ticketreport';
  public static readonly GET_EMPLOYEE_MAP_REPORT_LIST = '/ticketreport/getemployees';
  public static readonly GET_TICKET_MAP_REPORT_DETAIL = '/ticketreport/getbysitid';
  public static readonly GET_EXCLUSION_TICKETS_LIST = '/ticketexclusion/exclusionreport';
  public static readonly GET_TICKETS_BILLING_LIST = '/ticketbillingreport';
  public static readonly GET_TICKETS_PERFORMANCE = '/ticketreport/getprreportticketlist';
  public static readonly GET_TICKETS_PERFORMANCE_PDF = '/ticketreport/exportticketlistingdata';
  public static readonly GET_TICKETS_TRUCK_ROLL_LIST = '/ticketreport/gettruckrollreports';
  public static readonly GET_BILLING_EXCEL = '/ticketbillingreport/exportToExcel';
  public static readonly GET_API_RMA_REPORT = '/ticketRMA/getTicketMapRMAReport';
  public static readonly GET_RMA_BY_ID = '/ticketRMA/getTicketRMADetailById';
  public static readonly GET_BILLING_ESTIMATES_LIST = '/ticketbillingreport/getEstimateData';
  public static readonly GET_BILLING_DISPATCHES_LIST = '/ticketbillingreport/getAllDispatchData';
  public static readonly GET_BILLING_ESTIMATES_BULK_ACTION = '/ticketbillingreport/updateEstimatesStatus';
  public static readonly GET_BILLING_DISPATCHES_EXCEL = '/ticketbillingreport/exportNonDetailExcel';
  // Ticket Dashboard
  public static readonly GET_TICKET_DASHBOARD = '/ticketdashboard';

  //Site Audit Report
  public static readonly GET_ALL_NEW_SITE_AUDIT_REPORT_LIST = '/siteauditreport/getAll';
  public static readonly GET_SITE_AUDIT_CUSTOMER = '/siteauditreport/getsiteauditCustomer';
  public static readonly GET_SITE_AUDIT_PORTFOLIO = '/siteauditreport/getsiteauditportfolio';
  public static readonly GET_SITE_AUDIT_SITE = '/siteauditreport/getsiteauditsites';
  public static readonly GET_SITE_AUDIT_REPORT_CUSTOMER = '/siteauditreport/getsiteauditCustomer';
  public static readonly GET_SITE_AUDIT_REPORT_PORTFOLIO = '/siteauditreport/getsiteauditportfolio';
  public static readonly GET_SITE_AUDIT_REPORT_SITE = '/siteauditreport/getsiteauditsites';
  public static readonly GET_ALL_AUDIT_DATA_BY_ID = '/siteauditreport?id=';
  public static readonly SITE_AUDIT_REPORT_GENERATE = '/siteauditreport/generatefinalreport?reportId=';
  public static readonly SITE_AUDIT_PREVIEW_REPORT = '/siteauditreport/getpreviewreport';
  public static readonly UPLOAD_SITE_IMAGES = '/siteauditreport/uploadsiteauditlayout';
  public static readonly GET_SITE_LAYOUT_BY_ID = '/siteauditreport/getsitelayoutbyreportId';
  public static readonly DELETE_REPORT_SITEAUDIT = '/site/deletereportsiteimage';
  public static readonly GET_REPORT_SITEAUDIT = '/site/getallsiteimage';
  public static readonly GET_EXPECTED_PERFORMANCE_DATA = '/AutomationReport/SiteProduction';
  public static readonly CREATE_NEW_SITE_AUDIT_REPORT = '/siteauditreport/addSiteAuditReport';
  public static readonly GET_ALL_JHA_SITE_AUDIT_REPORT = '/jha/getSiteAuditJHAByReport?reportId=';
  public static readonly GET_ALL_JHA_SITE_AUDIT_REPORT_WITH_CPS = '/jha/getSiteAuditJHAByCPS';

  // Data Table
  public static readonly GET_DATA_TABLE_INFORMATION = '/automation/datatables/getDatatables';
  public static readonly PUT_DATA_TABLE_INFORMATION = '/automation/datatables/insertupdateDatatables';
  public static readonly GET_EDIT_INFORMATION = '/automation/datatables/DataTableEditHistoryFilterViewModel';
  public static readonly GET_REFETCH_INFORMATION = '/automation/datatables/GetPerformanceDatableReFetchHistory';
  public static readonly REFETCH_DATA_TABLE_INFORMATION = '/automation/datatables/refetchDatatables';
  public static readonly CHECK_FETCHING_STATUS = '/automation/datatables/checkBinValueInFetchingState';
  public static readonly CHECK_FETCHING_COUNT = '/automation/datatables/fetchRefetchCount';
  public static readonly EXPORT_DATA = '/automation/datatables/ExportDataTableInformation';
  public static readonly LOCK_UNLOCK_DATA_TABLE = '/automation/datatables/lockUnlockDatatables';
  public static readonly REFETCH_DATA_TABLE_BY_DEVICE = '/automation/datatables/bindFetchedValueByHardware';
  public static readonly REFETCH_DATA_TABLE_BY_DATE = '/automation/datatables/bindFetchedValueByCalender';
  public static readonly IMPORT_DATA = '/automation/datatables/importDataTableBinValueFromExcel';

  // Availability
  public static readonly GET_AVAILABILITY_DATA_TABLE_INFORMATION = '/automation/availability/getDatatables';
  public static readonly PUT_AVAILABILITY_DATA_TABLE_INFORMATION = '/automation/availability/insertupdateCell';
  public static readonly LOCK_UNLOCK_AVAILABILITY_DATA_TABLE = '/automation/availability/lockUnlockAvailability';
  public static readonly REFETCH_AVAILABILITY_DATA_TABLE_INFORMATION = '/automation/availability/refetchDatatables';
  public static readonly REFETCH_AVAILABILITY_DATA_TABLE_BY_DEVICE = '/automation/availability/bindFetchedValueByHardware';
  public static readonly REFETCH_AVAILABILITY_DATA_TABLE_BY_DATE = '/automation/availability/bindFetchedValueByCalender';
  public static readonly GET_AVAILABILITY_EDIT_INFORMATION = '/automation/availability/getAvailabilityDataTableEditHistory';
  public static readonly GET_AVAILABILITY_REFETCH_INFORMATION = '/automation/availability/getAvailabilityDataTableRefetchHistory';
  public static readonly AVAILABILITY_EXPORT_DATA = '/automation/availability/exportDataTableInformation';
  public static readonly AVAILABILITY_IMPORT_DATA = '/automation/availability/importDataTableBinValueFromExcel';
  public static readonly AVAILABILITY_GET_EXCLUSIONS = '/automation/availability/getExclusions';
  public static readonly AVAILABILITY_GET_EXCLUSIONS_TYPES = '/customer/getExclusionTypes';
  public static readonly AVAILABILITY_MODIFY_EXCLUSIONS = '/automation/availability/modifyExclusions';
  public static readonly GET_AVAILABILITY_REPORT = '/automation/availability/availabilityReport';
  public static readonly GET_EXCLUSION_REPORT = '/automation/availability/exclusionReport';
  public static readonly EXPORT_AVAILABILITY_REPORT = '/automation/availability/availabilityReportExport';
  public static readonly EXPORT_EXCLUSION_REPORT = '/automation/availability/exclusionReportExport';
  public static readonly GET_AVAILABILITY_PORTFOLIO = '/portfolio/portfolioforAutomationCustomer';
  public static readonly GET_AVAILABILITY_SITE = '/site/siteforAutomationCustomer';
  public static readonly EXPORT_AVAILABILITY_EXCLUSIONS_DATA = '/automation/availability/exportExclusionsExcel';
  public static readonly IMPORT_AVAILABILITY_EXCLUSIONS_DATA = '/automation/availability/importExclusionFromExcelForMultipleDays';
  public static readonly CHECK_MISSING_AVAILABILITY_DATA = '/automation/availability/checkMissingAvailabilityData';

  //Setting
  public static readonly SAVE_GENERAL_INFO = '/GeneralInfo';

  //worktype
  public static readonly WORK_TYPE_LIST = '/workType';
  public static readonly WORKSTEP_BY_ID = '/workType/workstepbyworktypeid';
  public static readonly WORK_STEP_LIST = '/workStep';
  public static readonly ENABLE_WORKTYPE = '/workType/enableworktype';
  public static readonly WORK_STEP_ARRAY = '/workType/getworkstepdetailsbyid';
  public static readonly EXPORT_WORK_TYPE_LIST = '/workType';
  //Hazard
  public static readonly GET_HAZARD_NAME = '/hazards';

  //WorkStep
  public static readonly WORKSTEP = '/workstep';
  public static readonly ENABLE_WORKSTEP = '/workstep/enableworkstep';

  // LOTO
  public static readonly LOTO = '/loto';
  public static readonly CREATE_UPDATE_LOTO_BARRIER = '/LotoBarrier/CreateOrUpdateLOTOBarrier';
  public static readonly GET_MASTER_LOTO_BARRIER = '/LotoBarrier/getLotoBarriers';
  public static readonly GET_LOTO_BARRIER = '/LotoBarrier';

  //JHA Report
  public static readonly JHA_BY_FILTER = '/jha/JHA3Data';
  public static readonly DELETE_JHA = '/SiteVistReport/deletereportsbyreportid?reportId=';
  public static readonly JHA_GENERATE_PDF = '/jha/exportjha3?reportId=';
  public static readonly JHA_DOWNLOAD_PDF = '/jha/downloadPdfReport?reportId=';
  public static readonly DETAIL_JHA = '/jha/jhalinkingscreen?id=';
  public static readonly GET_CREW_LEAD = '/jha/getcrawlead';
  public static readonly GET_CREW_MEMBER = '/jha/getcrawmember';
  public static readonly GET_WORK_ORDER_TICKET = '/jha/getwoticketlist/';
  public static readonly ADD_WORK_ORDER_TICKET = '/WorkOrderTicket';
  public static readonly DELETE_WORK_ORDER_TICKET = '/WorkOrderTicket/deletemapping';
  public static readonly GET_JHA_DETAILS_BY_ID = '/jha/getjha3reportdetail?id=';
  //performance Dashboard
  public static readonly GET_SYSTEM_PERFORMANCE = '/AutomationReport/systemPerformanceChart';
  public static readonly GET_TABLE_DATA = '/AutomationReport/tabularFormat';
  public static readonly GET_INSOLATION = '/AutomationReport/insolationYTD';
  public static readonly GET_ESTIMATED_LOSS = '/AutomationReport/estimatedLoss';
  public static readonly GET_PRODUCTION_DATA = '/AutomationReport/siteProductionChart';
  public static readonly GET_PERFORMANCE_INDEX = '/AutomationReport/sitePerformanceIndex';
  public static readonly GET_INVERTER_PRODUCTION_DATA = '/AutomationReport/inverterPerformance';
  public static readonly GET_INVERTER_HEAT_MAP_DATA = '/AutomationReport/inverterHeatMap';
  public static readonly GET_PDF_REPORT = '/AutomationReport/exportReportPdf';
  public static readonly GET_TICKETS = '/AutomationReport/getTickets';
  public static readonly GET_CHARTS = '/AutomationReport/commonChartsCall';
  public static readonly GET_WORK_ORDER_BY_YEAR_SITE_ID = '/WorkOrder/getwobaseonyearsiteid';
  public static readonly SITECHECKIN_BY_FILTER = '/SiteCheckInCheckOut/getall';
  public static readonly SITECHECKIN_BY_FIELDTECH = '/SiteCheckInCheckOut';
  public static readonly GET_AUDIT = '/SiteCheckInCheckOut/getAudit?id=';
  public static readonly UPDATE_AUDIT = '/SiteCheckInCheckOut';
  public static readonly DELETE_RECORD = '/SiteCheckInCheckOut?id=';
  public static readonly GET_POWER_CHARTS = '/AutomationReport/powerCharts';
  public static readonly GET_SUNRISE_SUNSET_BY_SITE_IDS = '/AutomationReport/getSunriseAndSunsetbySiteIds';
  public static readonly ATTACH_PERFORMANCE_REPORT = '/AutomationReport/attachperfomancereport';
  public static readonly DOWNLOAD_PERFORMANCE_REPORT = '/AutomationReport/downloadperfomancereport';
  public static readonly PORTFOLIO_WORK_ORDER = '/AutomationReport/portfolioWorkOrder';
  public static readonly MAP_WORK_ORDER = '/AutomationReport/mapWorkOrder';
  public static readonly BULK_UPDATE_WORK_ORDER_STATUS = '/AutomationReport/bulkupdatewostatus';
  public static readonly OUTAGE_GRID_REPORT = '/OutageReport/getoutagereport';
  public static readonly GET_OUTAGE_PORTFOLIO_REPORT = '/OutageReport/getOutagePortFolioReport';
  public static readonly GET_OUTAGE_SITE_REPORT = '/OutageReport/getOutageSiteReport';
  public static readonly GENERATE_PROD_DATA_DASHBOARD = '/AutomationReport/generateProdDataDashboard';
  public static readonly ADD_TICKETS_OUTAGE = '/OutageReport/addTicketsOutage';
  public static readonly GET_OUTAGE_ALERT_EXCEL = '/OutageReport/exportAlertReport';
  public static readonly EXPORT_CHARTS_DATA = '/AutomationReport/exportPerformanceTable';
  public static readonly GET_POWER_CARDS = '/AutomationReport/powerCards';
  public static readonly CHECK_INCLUDE_PHOTO_IN_SITE = '/SiteCheckInCheckOut/includePhotoEnable?siteId=';
  public static readonly SITE_CHECKOUT_IMAGE_UPLOAD = '/SiteCheckInCheckOut/uploadImages';
  public static readonly GENERATE_POWER_CHART_TOKEN = '/AutomationReport/generatePowerChartsToken';
  public static readonly VALIDATE_POWER_CHART_TOKEN = '/AutomationReport/validatePowerChartsToken';

  //report scheduler
  public static readonly REPORT_SCHEDULER = '/ScheduledReport';
  public static readonly CREATE_SCHEDULE = '/ScheduledReport/Create';
  public static readonly UPDATE_SCHEDULE = '/ScheduledReport/Update';
  public static readonly GET_A_SCHEDULE = '/ScheduledReport/getZeroGenReport';
  public static readonly RUN_SCHEDULE = '/ScheduledReport/RunTaskForcefully/';
  public static readonly ACTIVE_INACTIVE_SCHEDULE = '/ScheduledReport/ActiveInactiveTask';
  public static readonly DELETE_SCHEDULE = '/ScheduledReport/DeleteTask';

  //Encrypt UserId
  public static readonly Encrypt_UserId = '/user/encryptUserId/';
  public static readonly CLEAR_CUSTOMER_CACHE = '/user/ClearCustomerCache';
  //report scheduler
  public static readonly REFETCH_SCHEDULE_LIST = '/RefetchScheduledReport';
  public static readonly CREATE_REFETCH_SCHEDULE = '/RefetchScheduledReport/Create';
  public static readonly UPDATE_REFETCH_SCHEDULE = '/RefetchScheduledReport/Update';
  public static readonly GET_A_REFETCH_SCHEDULE = '/RefetchScheduledReport/getRefetchScheduledReport';
  public static readonly DELETE_REFETCH_SCHEDULE = '/RefetchScheduledReport/DeleteTask';
  public static readonly CLONE_REFETCH_SCHEDULE = '/RefetchScheduledReport/Clone';

  //Email Logs
  public static readonly GET_EMAIL_LOGS = '/AuditLogs/getEmailAuditLogs';
  public static readonly GET_EMAIL_TYPE_AND_STATUS_DROPDOWN_VALUES = '/AuditLogs/emailAuditPageLoad';

  // work order rescheduler
  public static readonly GET_ALL_WO_RESCHEDULE = '/WorkOrder/GetAllWorkOrderReschedules';
  public static readonly ADD_UPDATE_WO_RESCHEDULE = '/WorkOrder/InsertUpdateWorkOrderReschedule';
  public static readonly DELETE_WO_RESCHEDULE = '/WorkOrder/DeleteWorkOrderReschedule';

  //operations
  public static readonly GET_PM_COMPLETION_CHART = '/OperationReport/pmCompletionReport';
  public static readonly EXPORT_COMPLETION_PDF = '/OperationReport/exportCompetionReport';
  public static readonly GET_PIVOT_CHART = '/OperationReport/reschedulePivotReport';
  public static readonly EXPORT_PIVOT_PDF = '/OperationReport/exportReschedulePivotReport';
  public static readonly GET_PM_WORKORDERS = '/OperationReport/pmCompletionTable';
  public static readonly BULK_RESCHEDULE = '/OperationReport/BulkUpdateWorkOrderReschedule';

  //operation
  public static readonly SERVICES_ROOT = '/OperationServices';
  public static readonly SERVICES_LISTING = '/OperationServices/getall';
  public static readonly GET_A_SERVICES = '/OperationServices/getbyid';
  public static readonly GET_SERVICES_RATE_DROP_DOWN_LIST = '/OperationServices/getRateTypeDDL';
  public static readonly GET_SERVICES_LIST = '/OperationServices/getOperationServiceDDL';
  public static readonly GET_SERVICES_HISTORY_LIST = '/OperationServices/getOperationServiceAuditLog';

  //Region & Subregion
  public static readonly CREATE_REGION = '/Region/Create';
  public static readonly CREATE_SUB_REGION = '/SubRegion/Create';
  public static readonly GET_REGIONS_LIST = '/Region/getall';
  public static readonly GET_SUB_REGIONS_LIST = '/SubRegion/getall';
  public static readonly GET_COUNTIES_LIST = '/County/getall';
  public static readonly GET_REGION_DETAILS_BY_ID = '/Region/getbyid';
  public static readonly GET_SUB_REGION_DETAILS_BY_ID = '/SubRegion/getbyid';
  public static readonly GET_COUNTY_DETAILS_BY_ID = '/County/getbyid';
  public static readonly DELETE_REGION = '/Region/';
  public static readonly DELETE_SUB_REGION = '/SubRegion/';
  public static readonly GET_REGION_DROPDOWN_LIST = '/Region/getRegionDDL';
  public static readonly GET_SUB_REGION_DROPDOWN_LIST = '/SubRegion/getSubRegionDDL';
  public static readonly UPDATE_REGION = '/Region/Update';
  public static readonly UPDATE_SUB_REGION = '/SubRegion/Update';
  public static readonly UPDATE_COUNTY = '/County/Update';

  // contracts
  public static readonly CREATE_UPDATE_CONTRACT = '/Contract';
  public static readonly GET_CONTRACT_BY_CUSTOMER_ID = '/Contract/getByCustomerId';
  public static readonly GET_CONTRACT_BY_CONTRACT_ID = '/Contract/getContractbyid';
  public static readonly REMOVE_CONTRACT = '/Contract';
  public static readonly GET_SITES_BY_CUSTOMER = '/Contract/getContractSiteList';
  public static readonly GET_CONTRACT_LIST_BY_CUSTOMER = '/Contract/getContractDDL';
  public static readonly UPDATE_SITE_CONTRACT = '/Contract/updateSiteContract';
  public static readonly EXPORT_CONTRACT = '/Contract/ContractServiceExport';
  public static readonly IMPORT_CONTRACT = '/Contract/importContractDetail';
  public static readonly GENERATE_ALL_VALUES = '/Contract/generateAll';
  public static readonly CONTRACT_HISTORY = '/Contract/getContractsServiceAuditLog';
  public static readonly SITE_CONTRACT_FILTER_DROPS = '/Contract/getSiteFilterDropDown';
  public static readonly MARK_CONTRACT_ACTIVE_INACTIVE = '/Contract/activeInActiveContract';

  // Custom Dashboard
  public static readonly CREATE_CUSTOM_DASHBOARD = '/customdashboard';
  public static readonly DELETE_CUSTOM_DASHBOARD = '/customdashboard/delete';
  public static readonly GET_ALL_CUSTOM_DASHBOARDS = '/customdashboard/getall';
  public static readonly GET_CUSTOM_DASHBOARD_BY_ID = '/customdashboard/getbydashboardid';

  // Customer API Gateway Dashboard Configuration
  public static readonly SUBSCRIPTION_DETAILS = '/customerapi/SubscriptionDetails';
  public static readonly CUSTOMER_MODULES = '/customerapi/Modules';
  //Dropbox image gallery
  public static readonly GET_GALLERY_IMAGE_FILES = '/QESTFiles/getGalleryFiles';
  public static readonly UPLOAD_FILES_TO_GALLERY = '/QESTFiles/create';
  public static readonly UPLOAD_GALLERY_VIDEOS = '/QESTFiles/createVideo';
  public static readonly DELETE_GALLERY_FILES = '/QESTFiles/delete';
  public static readonly DELETE_MULTIPLE_IMAGES_FILES = '/QESTFiles/deleteFiles';
  public static readonly GET_DEVICES_TAGS_LIST = '/QESTFiles/getDeviceTagDDL';
  public static readonly GET_CONDITIONAL_TAGS_LIST = '/QESTFiles/getConditionTagDDL';
  public static readonly APPLY_IMAGE_TAGS = '/QESTFiles/updateImageTag';
  public static readonly DOWNLOAD_PREVIEW_IMAGE = '/QESTFiles/downloadPreviewImage';
  public static readonly DOWNLOAD_AS_FOLDER = '/QESTFiles/downloadFolder';
  public static readonly DOWNLOAD_ALL_GALLERY_IMAGES = '/QESTFiles/downloadAllFiles';
  public static readonly GET_WO_GALLERY = '/QESTFiles/getWoGallery';
  public static readonly GET_WO_VIDEO_GALLERY = '/QESTFiles/getWoVideoGallery';
  public static readonly GET_FILES_TAGS_LIST = '/QESTFiles/getFilesTagDDL';
  public static readonly UPDATE_FILES_ATTACHMENTS = '/QESTFiles/updateFileData';
  public static readonly UPDATE_BULK_FILES_TAGS = '/QESTFiles/bulkUpdateFileTags';

  //QEST FORMS
  public static readonly CREATE_QEST_TEMPLATE = '/qesttemplate/addQESTTemplate';
  public static readonly UPLOAD_QEST_TEMPLATE_FILE = '/qesttemplate/uploadQESTTemplateFile';
  public static readonly GET_TEMPLATE_LISTING = '/qesttemplate/getAllTemplate';
  public static readonly DELETE_TEMPLATE = '/qesttemplate/deleteQESTTemplate';
  public static readonly GET_TEMPLATE_DETAILS_BY_ID = '/qesttemplate/getById';
  public static readonly GET_TEMPLATE_DROPDOWN = '/qesttemplate/getTemplateDropDown';
  public static readonly GET_FORMS_LISTING = '/QESTForm/GetAllQESTForm';
  public static readonly GET_FORM_DETAILS_BY_ID = '/QESTForm/GetById';
  public static readonly DELETE_FORM = '/QESTForm/DeleteQESTForm';
  public static readonly IS_FORM_LOCKED = '/QESTForm/LockQESTForm';
  public static readonly FORM_UNLOCKED = '/QESTForm/UnlockQESTForm';
  public static readonly CREATE_QEST_FORM = '/QESTForm/CreateOrUpdateQESTForms';
  public static readonly GET_EQUIPMENT_DROPDOWN_LIST = '/equipment/getEquipmentByDeviceType';
  public static readonly GET_TEMPLATE_TYPE_DROPDOWN_LIST = '/qesttemplate/getTemplateTypeDropDown';
  public static readonly CLONE_QEST_FORM = '/QESTForm/CloneQESTForm';
  public static readonly CLONE_QEST_TEMPLATE = '/qesttemplate/cloneQESTTemplate';
  public static readonly CHANGE_STATUS_OF_QEST_FORM = '/QESTForm/ActiveInActiveQESTForm';
  public static readonly GET_WO_AVAILABLE_FORMS_LIST_FOR_QEST_FORMS = '/QESTFormWOMap/getQESTFormForWorkOrder';
  public static readonly GET_WO_VIEW_FORMS_LIST_FOR_QEST_FORMS = '/QESTFormWOMap/getAllSavedQESTFormForWO';
  public static readonly GET_COMPLETED_FORM_COUNT = '/QESTFormWOMap/getQESTFormCount';
  public static readonly GET_COMPLETED_FORM_EDIT_NOTE_HISTORY = '/QESTFormWOMap/getCompletedQFEditNoteAuditLog';
  public static readonly SAVE_SELECTED_INVERTERS = '/QESTFormWOMap/mapQESTFormWithDevice';
  public static readonly DELETE_VIEW_FORM = '/QESTFormWOMap/deleteQESTFormForDevice';
  public static readonly GET_SAVED_QEST_FORM_FOR_WO = '/QESTFormWOMap/getAllSavedQESTFormForWO';
  public static readonly GET_SELECTED_INVERTER_LIST = '/QESTFormWOMap/getSiteDeviceDetail';
  public static readonly GET_MAPPED_WO_QEST_FORM = '/QESTFormWOMap/getMappedWOQESTForm';
  public static readonly SAVE_QEST_FORM_FOR_WO = '/QESTFormWOMap/saveQESTFormForWO';
  public static readonly UPLOAD_QEST_FORM_FILES = '/QESTFormWOMap/uploadQESTFormFiles';
  public static readonly DOWNLOAD_QEST_FORM_FILES = '/QESTFormWOMap/downloadQESTFormPDF';
  public static readonly UPLOAD_FILLED_FORM_AS_PDF = '/QESTFormWOMap/uploadQESTFormPDF';
  public static readonly UPLOAD_BLANK_FORM_AS_PDF = '/QESTForm/uploadQESTFormPDF';
  public static readonly DOWNLOAD_BLANK_FORM_AS_PDF = '/QESTForm/downloadQESTFormPDF';
  public static readonly DOWNLOAD_ALL_VIEW_MODAL_FORMS = '/QESTFormWOMap/downloadAllQESTForm';
  public static readonly GET_JSON_FORM_FOR_CLONING = '/QESTFormWOMap/getListOfMappedWOQESTForm';
  public static readonly GET_COVER_PAGE_DETAILS = '/QESTFormWOMap/getCoverPage';
  public static readonly UPLOAD_COVER_PAGE = '/QESTFormWOMap/mergeCoverPagePDF';
  public static readonly GET_SM_QEST_FORM = '/QESTFormWOMap/getMappedWOSummaryReport';
  public static readonly GET_TPM_QEST_FORM = '/QESTFormWOMap/getMappedWOTPMForm';
  public static readonly GET_WO_AVAILABLE_FORMS_LIST_FOR_MODULE_TORQUE_QEST_FORMS = '/QESTFormWOMap/getAllTRQSavedQESTFormForWO';
  public static readonly GET_SITE_ZONE_DETAILS = '/QESTFormWOMap/getSiteZoneDetail';
  public static readonly GET_MTRQ_FORMS_FOR_WORKORDER = '/QESTFormWOMap/getTRQQESTFormForWorkOrder';
  public static readonly MAP_MTRQ_FORM_WITH_ZONE = '/QESTFormWOMap/mapQESTFormWithZone';

  //form analytics for QESTForm
  public static readonly GET_CONTROL_TYPE_LIST = '/qestDataAnalytics/fetchControlType';
  public static readonly GET_CONTROL_TYPE_DATA_LIST = '/qestDataAnalytics/fetchControlDataType';
  public static readonly GET_FORM_ANALYTICS_ADD_EDIT = '/qestDataAnalytics/addUpdateDataAnalyticsTag';
  public static readonly GET_FORM_ANALYTICS_GET_BY_ID = '/qestDataAnalytics/getById';
  public static readonly GET_FORM_ANALYTICS_LIST = '/qestDataAnalytics/getAllDataAnalyticsTag';
  public static readonly GET_FORM_ANALYTICS_DELETE = '/qestDataAnalytics/deleteDataAnalyticsTag';
  public static readonly GET_FORM_ANALYTICS_STATUS_CHANGE = '/qestDataAnalytics/activeInActiveDataAnalyticsTag';
  public static readonly ADD_UPDATE_WO_FORM_TAG_DATA = '/qestDataAnalytics/addUpdateWOFormTagData';

  // File upload in chunk
  public static readonly FILE_UPLOAD_CHUNK = '/QESTFiles/uploadFileInChunk';

  // qe-analytics
  public static readonly CHECK_UPDATE_QE_ANALYTICS_PASSWORD = '/qeAnalytics/checkAndUpdateQEAnalyticsPassword';
  public static readonly GET_QE_MENU_MODULE_LIST = '/qeAnalytics/getQEMenuModuleList';
  public static readonly QE_ANALYTICS_BY_FILTER = '/qeAnalytics/qeAnalyticsByFilter';
  public static readonly CAPTURE_QE_MENU_ANALYTICS = '/qeAnalytics/captureQEMenuAnalytics';
  public static readonly AUTHENTICATE_QE_ANALYTICS_CODE = '/user/authenticateQEAnalyticsCode';
  public static readonly UPDATE_QE_ANALYTICS_CODE = '/user/updateQEAnalyticsCode';
  public static readonly QE_ANALYTICS_END_USER_SESSION = '/qeAnalytics/endUserSession';
  public static readonly QE_ANALYTICS_USER_DRILLDOWN_BY_FILTER = '/qeAnalytics/qeAnalyticsUserDrilldownByFilter';

  //notification
  public static readonly GET_NOTIFICATION_BY_FILTER = '/CheckInOutNotification/getallbyfilter';
  public static readonly NOTIFICATION = '/CheckInOutNotification';
  public static readonly GET_NOTIFICATION_TYPES = '/CheckInOutNotification/notificationtypes';
  public static readonly DROPDOWN_OPTIONS = '/CheckInOutNotification/cpsdropdowns';
}
