import { DatePipe } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject, Subscription, debounceTime, forkJoin } from 'rxjs';
import { CustomerService } from '../../../entities/customer-management/customer.service';
import { CustomFormService } from '../../../entities/operations/custom-forms/custom-form.service';
import { QESTFormTemplateTypes } from '../../../entities/operations/custom-forms/custom-forms.model';
import { RegionMappingService } from '../../../entities/operations/region-mapping/region-mapping.service';
import { PortfolioService } from '../../../entities/portfolio-management/portfolio.service';
import { QEAnalyticsModuleAndEnumList, QEMenuModuleType } from '../../../entities/qe-analytics/models/qe-analytics.model';
import { QEAnalyticsService } from '../../../entities/qe-analytics/services/qe-analytics.service';
import { ReportService } from '../../../entities/report/report.service';
import { JhaService } from '../../../entities/safety/jha/jha.service';
import { AppliedFilter } from '../../../entities/site-device/site-device.model';
import { SiteDeviceService } from '../../../entities/site-device/site-device.service';
import { SiteService } from '../../../entities/site-management/site.service';
import {
  TicketBillingStatusesResponse,
  TicketPriorityMapping,
  TicketStatusMapping,
  statusDropDownList
} from '../../../entities/ticket-management/ticket.model';
import { TicketService } from '../../../entities/ticket-management/ticket.service';
import { UserService } from '../../../entities/user-management/user.service';
import { WorkorderService } from '../../../entities/workorder-management/workorder.service';
import { AppConstants } from '../../constants';
import { AUTHORITY_ROLE_STRING, ROLE_TYPE } from '../../enums';
import { Dropdown, GroupByDropdown, TemplateTypeDropdown } from '../../models/dropdown.model';
import { AllReportDropdown, ReportType, SITE_AUDIT_REPORT_STATUS_LIST } from '../../models/report.model';
import { ArrayTypesList, QECategoryType, QEServiceCategoryTypeList } from '../../models/site.model';
import { USER_STATUS_ENUM, USER_STATUS_LIST } from '../../models/user.model';
import { CommonService } from '../../services/common.service';
import { StateService } from '../../services/states.service';
import { StorageService } from '../../services/storage.service';
import { checkAuthorisations } from '../../utils';
import { CommonFilter, SflAutoSearchWithKeyboardResponse } from './common-filter.model';
import { FilterHelperService } from './filter-helper.service';
import {
  FILTER_PAGE_NAME,
  FILTER_SECTION_ENUM,
  FilterDetails,
  NOT_ALLOWED_SECTIONS_FOR_AUTO_FILTER,
  QE_DATE_DURATION_KEY_ENUM,
  QE_DATE_DURATION_LIST,
  SHOULD_ALLOW_SELECT_ALL
} from './filter.model';

@Component({
  selector: 'sfl-filter',
  templateUrl: './filter.component.html',
  styleUrls: ['./filter.component.scss']
})
export class FilterComponent implements OnInit, OnDestroy, OnChanges {
  @Input() filterDetails: FilterDetails;
  @Input() addReportTypeJHA = false;
  @Input() isApiErrorFilter = false;
  @Input() siteSearch = false;
  @Input() workOrderSearch = false;
  @Input() linkedTicketSearch = false;
  @Output() refreshList: EventEmitter<CommonFilter> = new EventEmitter<CommonFilter>();
  @Output() clearParentList: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() refreshTableHeight: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() searchValue;
  @Input() searchBy;
  @Input() billingPageViewType: 'billingView' | 'dispatchView' = 'billingView';
  @Input() isBillingPage: boolean = true;
  @Input() filterHeaderShow: boolean = true;
  @Input() filterAppendPosition: string = 'body';
  appliedFilter: Array<AppliedFilter> = new Array<AppliedFilter>();
  customerList: Array<Dropdown> = new Array<Dropdown>();
  ticketBillingStatuses: Array<TicketBillingStatusesResponse> = new Array<TicketBillingStatusesResponse>();
  crewMemberList: Array<Dropdown> = new Array<Dropdown>();
  workOrderList: Array<Dropdown> = new Array<Dropdown>();
  portfolioList: Array<Dropdown> = new Array<Dropdown>();
  siteAuditReportCustomerList: Array<Dropdown> = new Array<Dropdown>();
  siteAuditReportPortfolioList: Array<Dropdown> = new Array<Dropdown>();
  siteAuditReportSiteList: Array<Dropdown> = new Array<Dropdown>();
  siteAuditCustomerList: Array<Dropdown> = new Array<Dropdown>();
  siteAuditPortfolioList: Array<Dropdown> = new Array<Dropdown>();
  siteAuditSiteList: Array<Dropdown> = new Array<Dropdown>();
  SiteAuditCrewMemberList: Array<Dropdown> = new Array<Dropdown>();
  siteList: Array<Dropdown> = new Array<Dropdown>();
  stateList: Array<Dropdown> = new Array<Dropdown>();
  fieldTechList: Array<Dropdown> = new Array<Dropdown>();
  qeUserList: Array<Dropdown> = new Array<Dropdown>();
  equipmentList: any[] = [];
  templateTypeList: Array<TemplateTypeDropdown> = new Array<TemplateTypeDropdown>();
  regionList: Array<Dropdown> = new Array<Dropdown>();
  subRegionList: Array<Dropdown> = new Array<Dropdown>();
  filterModel: CommonFilter = new CommonFilter();
  filterModelCopy: CommonFilter = new CommonFilter();
  allReportDropdown: AllReportDropdown = new AllReportDropdown();
  reportType: ReportType = new ReportType();
  subscription: Subscription = new Subscription();
  affectedKWModelChanged = new Subject<number>();
  searchModelChanged = new Subject<string>();
  deviceNameModelChanged = new Subject<string>();
  isFilterOpen: boolean;
  loading: boolean = true;
  currentPage: number = 1;
  filterTitleList = AppConstants.FILTER_TITLES;
  pageSize = AppConstants.rowsPerPage;
  ticketPriority = TicketPriorityMapping;
  ticketEstimateStatus = statusDropDownList;
  ticketStatus = TicketStatusMapping;
  years = [];
  reportTypeData: ReportType[];
  reportstatus = [
    { id: 'Draft', name: 'Draft' },
    { id: 'Complete', name: 'Complete' }
  ];
  truckRollTypeList = [
    { id: 0, name: 'None', disabled: false },
    { id: 1, name: 'TR', disabled: false },
    { id: 2, name: 'TR-PM', disabled: false }
  ];
  woDropstatusList: Dropdown[] = [];
  arrayTypeList = ArrayTypesList;
  deviceTypeList: Dropdown[];
  modelList: Dropdown[];
  mfgList: Dropdown[];
  frequencytype: GroupByDropdown[] = [
    { id: 1, name: 'A', type: 'Annual' },
    { id: 1, name: 'H1', type: 'Semi-Annual' },
    { id: 2, name: 'H2', type: 'Semi-Annual' },
    { id: 1, name: 'Q1', type: 'Quarterly' },
    { id: 2, name: 'Q2', type: 'Quarterly' },
    { id: 3, name: 'Q3', type: 'Quarterly' },
    { id: 4, name: 'Q4', type: 'Quarterly' },
    { id: 1, name: 'JAN', type: 'Monthly' },
    { id: 2, name: 'FEB', type: 'Monthly' },
    { id: 3, name: 'MAR', type: 'Monthly' },
    { id: 4, name: 'APR', type: 'Monthly' },
    { id: 5, name: 'MAY', type: 'Monthly' },
    { id: 6, name: 'JUN', type: 'Monthly' },
    { id: 7, name: 'JUL', type: 'Monthly' },
    { id: 8, name: 'AUG', type: 'Monthly' },
    { id: 9, name: 'SEP', type: 'Monthly' },
    { id: 10, name: 'OCT', type: 'Monthly' },
    { id: 11, name: 'NOV', type: 'Monthly' },
    { id: 12, name: 'DEC', type: 'Monthly' }
  ];
  truckRollTypeList2 = [
    { id: 0, name: 'None', disabled: false },
    { id: 1, name: 'TR', disabled: false },
    { id: 2, name: 'TR-PM', disabled: false }
  ];
  dataSourceList: Array<Dropdown> = new Array<Dropdown>();
  automationSitesList: Array<Dropdown> = new Array<Dropdown>();
  devicesModelList = [];
  controlTypeList: Array<Dropdown> = new Array<Dropdown>();
  tagTypeList: Array<Dropdown> = new Array<Dropdown>();
  ticketTypeList: Array<Dropdown> = new Array<Dropdown>();
  costTypeList: Array<Dropdown> = new Array<Dropdown>();
  saReportStatusList = SITE_AUDIT_REPORT_STATUS_LIST;
  nercSiteTypeDropDown: Array<Dropdown> = new Array<Dropdown>();
  qestFormTemplateTypes = QESTFormTemplateTypes;
  qeServiceTypeDropDownOption: QECategoryType[] = [];
  qeCategoryTypeList: QEServiceCategoryTypeList[] = [];
  filterSectionEnum = FILTER_SECTION_ENUM;
  filterPageNameEnum = FILTER_PAGE_NAME;
  userTypeList: Array<Dropdown> = new Array<Dropdown>();
  qeMenuModuleTypeList: (Pick<Dropdown, 'name' | 'id'> & QEMenuModuleType)[] = [];
  dateDurationTypeList: Array<Dropdown> = new Array<Dropdown>();
  dateDurationConfig = {
    durationMinDate: new Date(),
    durationMaxDate: new Date(),
    durationMaxEndDate: new Date()
  };
  qeDateDurationKeyEnum = QE_DATE_DURATION_KEY_ENUM;
  userNamesList: Array<Dropdown> = new Array<Dropdown>();
  userEmailsList: Array<Dropdown> = new Array<Dropdown>();
  companyNamesList: Array<Dropdown> = new Array<Dropdown>();
  userStatusList: Array<Dropdown> = new Array<Dropdown>();
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    private readonly storageService: StorageService,
    private readonly portfolioService: PortfolioService,
    private readonly siteService: SiteService,
    private readonly customerService: CustomerService,
    private readonly jhaService: JhaService,
    private readonly stateService: StateService,
    private readonly userService: UserService,
    public datepipe: DatePipe,
    private readonly commonService: CommonService,
    private readonly reportservice: ReportService,
    private readonly regionMappingService: RegionMappingService,
    private readonly workorderservice: WorkorderService,
    public readonly siteDeviceService: SiteDeviceService,
    private readonly route: ActivatedRoute,
    private readonly customFormService: CustomFormService,
    private readonly ticketService: TicketService,
    private readonly qeAnalyticsService: QEAnalyticsService,
    private readonly filterHelperService: FilterHelperService
  ) {
    this.affectedKWModelChanged.pipe(debounceTime(1000)).subscribe(() => {
      this.refreshListInParent();
      if (this.filterModel.affectedkw === null || !this.filterModel.affectedkw) {
        const index = this.appliedFilter.findIndex(item => item.text === this.filterTitleList.MINIMUM_AFFECTED_KW);
        if (index > -1) {
          this.appliedFilter.splice(index, 1);
        }
      }
    });
    this.searchModelChanged.pipe(debounceTime(1000)).subscribe(() => {
      this.refreshListInParent();
      if (this.filterModel.search === '' || !this.filterModel.search) {
        this.appliedFilter.splice(
          this.appliedFilter.findIndex(item => item.text === this.filterTitleList.SEARCH_BOX),
          1
        );
      }
    });

    this.deviceNameModelChanged.pipe(debounceTime(1000)).subscribe(() => {
      this.refreshListInParent();
      if (this.filterModel.DeviceName === '' || !this.filterModel.DeviceName) {
        const index = this.appliedFilter.findIndex(item => item.text === this.filterTitleList.DEVICE);
        if (index > -1) {
          this.appliedFilter.splice(index, 1);
        }
      }
    });
  }

  ngOnInit(): void {
    this.filterModel.itemsCount = this.pageSize;
    this.filterModel.sortBy = this.filterDetails.default_sort ? this.filterDetails.default_sort : this.filterModel.sortBy;
    this.filterModel.direction = this.filterDetails.default_direction ? this.filterDetails.default_direction : this.filterModel.direction;
    let hasUrlFilter = false;
    if (this.filterDetails.page_name === 'dashboardPage') {
      this.frequencytype.push({ id: 1, name: 'BEE', type: 'Biennial' }, { id: 2, name: 'BEO', type: 'Biennial' });
    }
    this.route.queryParams.subscribe(params => {
      if (Object.keys(params).length) {
        this.filterModel.customerIds = params['customerId'] ? [Number(params['customerId'])] : [];
        this.filterModel.portfolioIds = params['portfolioId'] ? [Number(params['portfolioId'])] : [];
        this.filterModel.siteIds = params['siteId'] ? [Number(params['siteId'])] : [];
        this.filterModel.isArchive = params['isArchive'] === 'true';
        this.filterModel.isActive = params['isArchive'] === 'false' ? 'Active' : '';
        this.filterModel.automationPartnerIds = params['automationPartnerId'] ? [Number(params['automationPartnerId'])] : [];
        this.filterModel.automationSiteIds = params['automationSiteId'] ? [Number(params['automationSiteId'])] : [];
        if (this.filterDetails.page_name === 'sitePage') {
          const siteFilters = this.storageService.get('sitePage');
          this.filterModel.states = siteFilters.states;
          this.filterModel.regionIds = siteFilters.regionIds;
          this.filterModel.subregionIds = siteFilters.subregionIds;
        }
        if (
          this.filterModel.portfolioIds.length ||
          this.filterModel.customerIds.length ||
          this.filterModel.siteIds.length ||
          this.filterModel.automationPartnerIds.length ||
          this.filterModel.automationSiteIds.length
        ) {
          hasUrlFilter = true;
        }
      }
    });
    const filter = hasUrlFilter ? this.filterModel : this.storageService.get(this.filterDetails.page_name);
    if (this.filterDetails.filter_item.TRUCK_ROLL?.show) {
      const selectedTruckRoll = filter.truckRoll?.length ? this.getFilteredTruckRolls(filter.truckRoll) : [];
      if (selectedTruckRoll.length) {
        this.truckRollChange(selectedTruckRoll);
      }
    }
    const filterSection = this.storageService.get(this.filterDetails.filter_section_name);
    this.isFilterOpen = this.filterHeaderShow ? true : filterSection;
    const tempArray = [];
    const tempArrayObj = [];
    const filterArrayObj = [];
    if (this.filterDetails.filter_item.CUSTOMER?.show) {
      tempArray.push(this.customerService.getAllCustomer());
      tempArrayObj.push('customerList');
    }
    if (this.filterDetails.filter_item.BILLING_STATUS?.show) {
      tempArray.push(this.ticketService.getTicketBillingStatuses());
      tempArrayObj.push('ticketBillingStatuses');
    }
    if (this.filterDetails.filter_item.CREW_MEMBER?.show) {
      tempArray.push(this.jhaService.getCrewMember());
      tempArrayObj.push('crewMemberList');
    }
    if (this.filterDetails.filter_item.SITE_AUDIT_REPORT_CUSTOMER?.show) {
      tempArray.push(this.customerService.getSiteAuditReportCustomersAll());
      tempArrayObj.push('siteAuditReportCustomerList');
    }
    if (this.filterDetails.filter_item.SITE_AUDIT_REPORT_PORTFOLIO?.show) {
      tempArray.push(this.portfolioService.getAllSiteAuditReportPortfoliosbycustomerId());
      tempArrayObj.push('siteAuditReportPortfolioList');
    }
    if (this.filterDetails.filter_item.SITE_AUDIT_REPORT_SITE?.show) {
      tempArray.push(this.siteService.getAllSiteAuditReportSitesByPortfolioId());
      tempArrayObj.push('siteAuditReportSiteList');
    }
    if (this.filterDetails.filter_item.SITE_AUDIT_CUSTOMER?.show) {
      tempArray.push(this.customerService.getSiteAuditJHACustomersAll());
      tempArrayObj.push('siteAuditCustomerList');
    }
    if (this.filterDetails.filter_item.SITE_AUDIT_PORTFOLIO?.show) {
      tempArray.push(this.portfolioService.getAllSiteAuditJHAPortfoliosbycustomerId());
      tempArrayObj.push('siteAuditPortfolioList');
    }
    if (this.filterDetails.filter_item.SITE_AUDIT_SITE?.show) {
      tempArray.push(this.siteService.getAllSiteAuditJHASitesByPortfolioId());
      tempArrayObj.push('siteAuditSiteList');
    }
    if (this.filterDetails.filter_item.SITE_AUDIT_CREW_MEMBER?.show) {
      tempArray.push(this.jhaService.getSiteSuditJHACrewMember());
      tempArrayObj.push('SiteAuditCrewMemberList');
    }
    if (this.filterDetails.filter_item.STATE?.show) {
      tempArray.push(this.stateService.getStates(false));
      tempArrayObj.push('stateList');
    }
    if (this.filterDetails.filter_item.QE_TECH?.show) {
      const userRoles = Object.entries(AUTHORITY_ROLE_STRING)
        .filter(
          ([roleType, authorityRoleString]) =>
            authorityRoleString !== AUTHORITY_ROLE_STRING[ROLE_TYPE.CUSTOMER] &&
            authorityRoleString !== AUTHORITY_ROLE_STRING[ROLE_TYPE.CONTRACTOR]
        )
        .map(([roleType, authorityRoleString]) => Number(roleType));
      tempArray.push(this.userService.getUserByRoles(userRoles));
      tempArrayObj.push('fieldTechList');
    }
    if (this.filterDetails.filter_item.FIELD_TECH_IDS?.show) {
      tempArray.push(this.userService.getAllQEUsers());
      tempArrayObj.push('fieldTechList');
    }
    if (this.filterDetails.filter_item.CONTROL_TYPE_IDS?.show) {
      tempArray.push(this.customFormService.getControlTypeList());
      tempArrayObj.push('controlTypeList');
    }
    if (this.filterDetails.filter_item.CONTROL_DATA_TYPE_IDS?.show) {
      tempArray.push(this.customFormService.getTagTypeList(null));
      tempArrayObj.push('tagTypeList');
    }
    if (this.filterDetails.filter_item.TEMPLATE_TYPE_IDS?.show) {
      this.getTemplateTypeDropdown('TEMPLATE_TYPE_IDS');
    }
    if (this.filterDetails.filter_item.TEMPLATE_TYPE_FORM_LIST_IDS?.show) {
      this.getTemplateTypeDropdown('TEMPLATE_TYPE_FORM_LIST_IDS');
    }
    if (this.filterDetails.filter_item.EQUIPMENT_LIST?.show) {
      this.getEquipmentList();
    }
    if (this.filterDetails.filter_item.REGION?.show) {
      tempArray.push(this.regionMappingService.getRegionDropdownList());
      tempArrayObj.push('regionList');
    }
    if (this.filterDetails.filter_item.SUB_REGION?.show) {
      tempArray.push(this.regionMappingService.getSubRegionDropdownList());
      tempArrayObj.push('subRegionList');
    }
    if (this.filterDetails.filter_item.USER?.show) {
      if (
        this.filterDetails.page_name === this.filterPageNameEnum.ADMIN_QE_ANALYTICS &&
        this.filterDetails.filterSectionEnum === this.filterSectionEnum.ADMIN_QE_ANALYTICS
      ) {
        const userRoles = Object.entries(AUTHORITY_ROLE_STRING).map(([roleType, authorityRoleString]) => Number(roleType));
        tempArray.push(this.userService.getUserByRoles(userRoles));
      } else {
        tempArray.push(this.userService.getAllQEUsers());
      }
      tempArrayObj.push('fieldTechList');
    }
    if (this.filterDetails.filter_item.WORKORDER_STATUS?.show) {
      tempArray.push(this.workorderservice.getWorkOrderStatus(false));
      tempArrayObj.push('woDropstatusList');
    }
    if (this.filterDetails.filter_item.AUTOMATION_DATA_SOURCE?.show) {
      tempArray.push(this.siteService.getAllDataSourceList(this.isApiErrorFilter));
      tempArrayObj.push('dataSourceList');
    }
    if (this.filterDetails.filter_item.SHOW_NERC_SITE_TYPE?.show) {
      this.getNercSiteTypeValues();
    }
    if (this.filterDetails.filter_item.TICKET_TYPE?.show) {
      this.getTicketTypeList();
    }
    if (this.filterDetails.filter_item.COSTS_TYPE?.show) {
      this.getCostTypeList();
    }
    if (this.filterDetails.filter_item.QE_SERVICE_TYPE?.show) {
      tempArray.push(this.siteService.getQEServiceTypeDropDown());
      tempArrayObj.push('qeCategoryTypeList');
    }
    if (this.filterDetails.filter_item.USER_TYPE?.show) {
      this.getUserTypeList();
    }
    if (this.filterDetails.filter_item.QE_MODULES?.show) {
      this.getQEMenuModuleList();
    }
    if (this.filterDetails.filter_item.QE_DATE_DURATION?.show) {
      if (
        this.filterDetails.page_name === this.filterPageNameEnum.ADMIN_QE_ANALYTICS &&
        this.filterDetails.filterSectionEnum === this.filterSectionEnum.ADMIN_QE_ANALYTICS
      ) {
        const allowedDurationTypes = [
          QE_DATE_DURATION_KEY_ENUM.LAST_7_DAYS,
          QE_DATE_DURATION_KEY_ENUM.LAST_30_DAYS,
          QE_DATE_DURATION_KEY_ENUM.DATE_RANGE
        ];
        this.dateDurationTypeList = QE_DATE_DURATION_LIST.filter(durationType => allowedDurationTypes.includes(durationType.id));
      } else {
        this.dateDurationTypeList = QE_DATE_DURATION_LIST;
      }
    }
    if (
      this.filterDetails.filter_item.USER_NAME?.show ||
      this.filterDetails.filter_item.USER_EMAIL?.show ||
      this.filterDetails.filter_item.USER_COMPANY?.show
    ) {
      tempArray.push(this.userService.getUserFilters());
      tempArrayObj.push('userFilters');
    }
    if (this.filterDetails.filter_item.USER_STATUS?.show) {
      this.userStatusList = USER_STATUS_LIST;
    }
    if (filter) {
      this.filterModel = filter;
      if (this.isFilterAllowedWithoutButton() && !this.isSiteAuditPages()) {
        this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel, this.filterDetails.page_name);
      }

      if (this.filterDetails.filter_item.QE_TECH?.show) {
        if (this.filterModel.userIds && this.filterModel.userIds.length) {
          filterArrayObj.push({
            text: this.filterTitleList.QE_TECH,
            value: this.filterModel?.userIds
          });
        }
      }
      if (this.filterDetails.filter_item.CREW_MEMBER?.show) {
        if (this.filterModel.crewMemberNames.length) {
          filterArrayObj.push({
            text: this.filterTitleList.CREW_MEMBER,
            value: this.filterModel.crewMemberNames
          });
        }
      }
      if (this.filterDetails.filter_item.USER?.show) {
        if (this.filterModel.userIds && this.filterModel.userIds.length) {
          filterArrayObj.push({
            text: this.filterTitleList.USER,
            value: this.filterModel?.userIds
          });
        }
      }
      if (this.filterDetails.filter_item.DEVICE_TYPE?.show) {
        tempArray.push(this.siteDeviceService.getAllDeviceType());
        tempArrayObj.push('deviceTypeList');
        if (this.filterModel.deviceTypeIds) {
          filterArrayObj.push({ text: this.filterTitleList.DEVICE_TYPE, value: this.filterModel.deviceTypeIds });
        }
      }
      if (this.filterDetails.filter_item.MODEL?.show) {
        this.siteDeviceService.getDeviceModel().subscribe(res => {
          this.devicesModelList = res.map((element: any) => ({
            ...element,
            name: element.name + ' [' + element.mfgName + ']'
          }));
          this.updateModelListValues(true);
        });
        tempArray.push(this.siteDeviceService.getDeviceModel());
        tempArrayObj.push('modelList');
        if (this.filterModel.deviceModels) {
          filterArrayObj.push({ text: this.filterTitleList.MODEL, value: this.filterModel.deviceModels });
        }
      }
      if (this.filterDetails.filter_item.MFG?.show) {
        tempArray.push(this.siteDeviceService.getDeviceMfg());
        tempArrayObj.push('mfgList');
        if (this.filterModel.mfgs) {
          filterArrayObj.push({ text: this.filterTitleList.MFG, value: this.filterModel.mfgs });
        }
      }
      if (this.filterDetails.filter_item.DEVICE?.show) {
        if (this.filterModel.DeviceName) {
          filterArrayObj.push({ text: this.filterTitleList.DEVICE, value: this.filterModel.DeviceName });
        }
      }

      if (this.filterDetails.filter_item.SHOW_DELETED?.show) {
        if (this.filterModel.isDelete) {
          filterArrayObj.push({ text: this.filterTitleList.SHOW_DELETED, value: this.filterModel.isDelete });
        }
      }
      if (this.filterDetails.filter_item.IS_LINK?.show) {
        if (this.filterModel.isLink) {
          filterArrayObj.push({ text: this.filterTitleList.IS_LINK, value: this.filterModel.isLink });
        }
      }
      if (this.filterDetails.filter_item.CHECK_IN_ONLY?.show) {
        if (this.filterModel.checkInOnly) {
          filterArrayObj.push({ text: this.filterTitleList.CHECK_IN_ONLY, value: this.filterModel.checkInOnly });
          this.addFilter(
            { text: this.filterTitleList.CHECK_IN_ONLY, name: this.filterModel.checkInOnly },
            this.filterTitleList.CHECK_IN_ONLY
          );
        }
      }
      if (this.filterDetails.filter_item.SHOW_ARCHIVED?.show) {
        if (this.filterModel.isArchive) {
          filterArrayObj.push({ text: this.filterTitleList.SHOW_ARCHIVED, value: this.filterModel.isArchive });
        }
      }
      if (this.filterDetails.filter_item.START_YEAR?.show) {
        this.years = this.commonService.getYear(false, true);
        if (this.filterModel.years && this.filterModel.years.length) {
          filterArrayObj.push({ text: this.filterTitleList.START_YEAR, value: this.filterModel.years });
          this.addFilter({ text: this.filterTitleList.START_YEAR, name: this.filterModel.years }, this.filterTitleList.START_YEAR);
        } else if (this.filterModel.year) {
          filterArrayObj.push({ text: this.filterTitleList.START_YEAR, value: this.filterModel.year });
          this.addFilter({ text: this.filterTitleList.START_YEAR, name: this.filterModel.year }, this.filterTitleList.START_YEAR);
        } else {
          if (this.filterDetails.filter_item.START_YEAR?.multi) {
            this.filterModel.years = [this.commonService.getCurrentYear()];
            this.addFilter({ text: this.filterTitleList.START_YEAR, name: this.filterModel.years }, this.filterTitleList.START_YEAR);
          } else {
            this.filterModel.year = this.commonService.getCurrentYear();
            this.addFilter({ text: this.filterTitleList.START_YEAR, name: this.filterModel.year }, this.filterTitleList.START_YEAR);
          }
        }
      }
      if (this.filterDetails.filter_item.ASSESSMENT_TYPE?.show) {
        this.getReportType();
        if (this.filterModel.reportTypeIds && this.filterModel.reportTypeIds.length) {
          filterArrayObj.push({
            text: this.filterTitleList.ASSESSMENT_TYPE,
            value: this.filterModel?.reportTypeIds
          });
        }
      }
      if (this.filterDetails.filter_item.REPORT_STATUS?.show) {
        if (this.filterModel.reportStatus) {
          filterArrayObj.push({ text: this.filterTitleList.REPORT_STATUS, value: this.filterModel.reportStatus });
        }
      }
      if (this.filterDetails.filter_item.WORKORDER_STATUS?.show) {
        if (this.filterModel.woStatus) {
          filterArrayObj.push({ text: this.filterTitleList.WORKORDER_STATUS, value: this.filterModel.woStatus });
        }
      }
      if (this.filterDetails.filter_item.SITE_AUDIT_REPORT_STATUS?.show) {
        if (this.filterModel.reportStatusId) {
          filterArrayObj.push({ text: this.filterTitleList.SITE_AUDIT_REPORT_STATUS, value: this.filterModel.reportStatusId });
        }
      }
      if (this.filterDetails.filter_item.SHOW_NERC_SITE_TYPE?.show) {
        if (this.filterModel.nercSiteTypeId) {
          filterArrayObj.push({ text: this.filterTitleList.SHOW_NERC_SITE_TYPE, value: this.filterModel.nercSiteTypeId });
        }
      }
      if (this.filterDetails.filter_item.QE_SERVICE_TYPE?.show) {
        if (this.filterModel.qeServiceTypes) {
          filterArrayObj.push({ text: this.filterTitleList.QE_SERVICE_TYPE, value: this.filterModel.qeServiceTypes });
        }
      }
      if (
        this.filterDetails.filter_item.USER_TYPE?.show &&
        this.filterDetails.filter_item.USER_TYPE?.multi &&
        this.filterModel.userTypeIds &&
        this.filterModel.userTypeIds.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.USER_TYPE,
          value: this.filterModel.userTypeIds
        });
      }
      if (
        this.filterDetails.filter_item.USER_TYPE?.show &&
        !this.filterDetails.filter_item.USER_TYPE?.multi &&
        this.filterModel.userTypeId
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.USER_TYPE,
          value: this.filterModel.userTypeId
        });
      }
      if (this.filterDetails.filter_item.QE_DATE_DURATION?.show) {
        if (this.filterModel.dateDuration) {
          this.onDateDurationSelect();
          this.addFilter(this.filterModel.dateDuration, this.filterTitleList.QE_DATE_DURATION);
        }
      }
      if (this.filterDetails.filter_item.QE_MODULES?.show) {
        if (this.filterModel.qeMenuModuleIds) {
          this.addFilter(this.qeMenuModuleTypeList, this.filterTitleList.QE_MODULES, 'qeMenuModuleIds');
        }
      }
      if (
        this.filterDetails.filter_item.USER_NAME?.show &&
        this.filterDetails.filter_item.USER_NAME?.multi &&
        this.filterModel.userNames &&
        this.filterModel.userNames.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.USER_NAME,
          value: this.filterModel.userNames
        });
      }
      if (this.filterDetails.filter_item.USER_NAME?.show && !this.filterDetails.filter_item.USER_NAME?.multi && this.filterModel.userName) {
        filterArrayObj.push({
          text: this.filterTitleList.USER_NAME,
          value: this.filterModel.userName
        });
      }
      if (
        this.filterDetails.filter_item.USER_EMAIL?.show &&
        this.filterDetails.filter_item.USER_EMAIL?.multi &&
        this.filterModel.emails &&
        this.filterModel.emails.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.USER_EMAIL,
          value: this.filterModel.emails
        });
      }
      if (this.filterDetails.filter_item.USER_EMAIL?.show && !this.filterDetails.filter_item.USER_EMAIL?.multi && this.filterModel.email) {
        filterArrayObj.push({
          text: this.filterTitleList.USER_EMAIL,
          value: this.filterModel.email
        });
      }
      if (
        this.filterDetails.filter_item.USER_COMPANY?.show &&
        this.filterDetails.filter_item.USER_COMPANY?.multi &&
        this.filterModel.companyNames &&
        this.filterModel.companyNames.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.USER_COMPANY,
          value: this.filterModel.companyNames
        });
      }
      if (
        this.filterDetails.filter_item.USER_COMPANY?.show &&
        !this.filterDetails.filter_item.USER_COMPANY?.multi &&
        this.filterModel.companyName
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.USER_COMPANY,
          value: this.filterModel.companyName
        });
      }
      if (this.filterDetails.filter_item.USER_STATUS?.show) {
        this.addFilter({ name: this.filterModel.isActive }, this.filterTitleList.USER_STATUS);
      }
      if (this.filterModel.frequencies && this.filterModel.frequencies.length) {
        filterArrayObj.push({ text: this.filterTitleList.FREQUENCY_TYPE, value: this.filterModel.frequencies });
      }
      if (this.filterModel.arrayTypeIds && this.filterModel.arrayTypeIds.length) {
        filterArrayObj.push({
          text: this.filterTitleList.ARRAY_TYPE,
          value: this.filterModel.arrayTypeIds
        });
      }
      if (this.filterDetails.filter_item.CUSTOMER?.show && this.filterModel.customerIds && this.filterModel.customerIds.length) {
        const allReportDropdown = new AllReportDropdown();
        allReportDropdown.ids = this.filterModel.customerIds;
        allReportDropdown.customerIds = this.filterModel.customerIds;
        if (this.filterDetails.filter_item.PORTFOLIO?.show) {
          tempArray.push(this.portfolioService.getAllReportPortfoliosByCustomerId(allReportDropdown));
          tempArrayObj.push('portfolioList');
        }
        if (this.filterDetails.filter_item.SITE?.show) {
          tempArray.push(this.siteService.getAllReportSitesByPortfolioId(allReportDropdown));
          tempArrayObj.push('siteList');
        }
        filterArrayObj.push({
          text: this.filterTitleList.CUSTOMER,
          value: this.filterModel.customerIds
        });
      } else {
        if (this.filterDetails.filter_item.PORTFOLIO?.show) {
          const allReportDropdown = new AllReportDropdown();
          allReportDropdown.ids = [];
          tempArray.push(this.portfolioService.getAllReportPortfoliosByCustomerId(allReportDropdown));
          tempArrayObj.push('portfolioList');
        }
      }
      if (this.filterDetails.filter_item.PORTFOLIO?.show && this.filterModel.portfolioIds && this.filterModel.portfolioIds.length) {
        if (this.filterDetails.filter_item.SITE?.show) {
          const allReportDropdown = new AllReportDropdown();
          allReportDropdown.ids = this.filterModel.portfolioIds;
          tempArray.push(this.siteService.getAllReportSitesByPortfolioId(allReportDropdown));
          tempArrayObj.push('siteList');
        }
        filterArrayObj.push({
          text: this.filterTitleList.PORTFOLIO,
          value: this.filterModel.portfolioIds
        });
      } else {
        if (this.filterDetails.filter_item.SITE?.show) {
          const allReportDropdown = new AllReportDropdown();
          allReportDropdown.ids = [];
          tempArray.push(this.siteService.getAllReportSitesByPortfolioId(allReportDropdown));
          tempArrayObj.push('siteList');
        }
      }
      if (this.filterDetails.filter_item.SITE?.show && this.filterModel.siteIds && this.filterModel.siteIds.length) {
        filterArrayObj.push({
          text: this.filterTitleList.SITE,
          value: this.filterModel.siteIds
        });
      }
      if (this.filterDetails.filter_item.STATE?.show && this.filterModel.states && this.filterModel.states.length) {
        filterArrayObj.push({
          text: this.filterTitleList.STATE,
          value: this.filterModel.states
        });
      }
      if (
        this.filterDetails.filter_item.SITE_AUDIT_REPORT_CUSTOMER?.show &&
        this.filterDetails.filter_item.SITE_AUDIT_REPORT_CUSTOMER?.multi &&
        this.filterModel.customerNames &&
        this.filterModel.customerNames.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.SITE_AUDIT_REPORT_CUSTOMER,
          value: this.filterModel.customerNames
        });
      }
      if (
        this.filterDetails.filter_item.SITE_AUDIT_REPORT_PORTFOLIO?.show &&
        this.filterDetails.filter_item.SITE_AUDIT_REPORT_PORTFOLIO?.multi &&
        this.filterModel.portfolioNames &&
        this.filterModel.portfolioNames.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.SITE_AUDIT_REPORT_PORTFOLIO,
          value: this.filterModel.portfolioNames
        });
      }
      if (
        this.filterDetails.filter_item.SITE_AUDIT_CUSTOMER?.show &&
        this.filterDetails.filter_item.SITE_AUDIT_CUSTOMER?.multi &&
        this.filterModel.customerNames &&
        this.filterModel.customerNames.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.SITE_AUDIT_CUSTOMER,
          value: this.filterModel.customerNames
        });
      }
      if (
        this.filterDetails.filter_item.SITE_AUDIT_PORTFOLIO?.show &&
        this.filterDetails.filter_item.SITE_AUDIT_PORTFOLIO?.multi &&
        this.filterModel.portfolioNames &&
        this.filterModel.portfolioNames.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.SITE_AUDIT_PORTFOLIO,
          value: this.filterModel.portfolioNames
        });
      }
      if (
        this.filterDetails.filter_item.SITE_AUDIT_CREW_MEMBER?.show &&
        this.filterDetails.filter_item.SITE_AUDIT_CREW_MEMBER?.multi &&
        this.filterModel.crewMemberNames &&
        this.filterModel.crewMemberNames.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.SITE_AUDIT_CREW_MEMBER,
          value: this.filterModel.crewMemberNames
        });
      }
      if (
        this.filterDetails.filter_item.SITE_AUDIT_REPORT_SITE?.show &&
        this.filterDetails.filter_item.SITE_AUDIT_REPORT_SITE?.multi &&
        this.filterModel.siteNames &&
        this.filterModel.siteNames.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.SITE_AUDIT_REPORT_SITE,
          value: this.filterModel.siteNames
        });
      }
      if (
        this.filterDetails.filter_item.SITE_AUDIT_SITE?.show &&
        this.filterDetails.filter_item.SITE_AUDIT_SITE?.multi &&
        this.filterModel.siteNames &&
        this.filterModel.siteNames.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.SITE_AUDIT_SITE,
          value: this.filterModel.siteNames
        });
      }
      if (
        this.filterDetails.filter_item.STATUS?.show &&
        this.filterDetails.filter_item.STATUS?.multi &&
        this.filterModel.statusIds &&
        this.filterModel.statusIds.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.STATUS,
          value: this.filterModel.statusIds
        });
      }
      if (
        this.filterDetails.filter_item.TICKET_TYPE?.show &&
        this.filterDetails.filter_item.TICKET_TYPE?.multi &&
        this.filterModel.ticketTypeIds &&
        this.filterModel.ticketTypeIds.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.TICKET_TYPE,
          value: this.filterModel.ticketTypeIds
        });
      }
      if (
        this.filterDetails.filter_item.COSTS_TYPE?.show &&
        this.filterDetails.filter_item.COSTS_TYPE?.multi &&
        this.filterModel.costTypeIds &&
        this.filterModel.costTypeIds.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.COSTS_TYPE,
          value: this.filterModel.costTypeIds
        });
      }
      if (
        this.filterDetails.filter_item.PRIORITY?.show &&
        this.filterDetails.filter_item.PRIORITY?.multi &&
        this.filterModel.priorityIds &&
        this.filterModel.priorityIds.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.PRIORITY,
          value: this.filterModel.priorityIds
        });
      }
      if (
        this.filterDetails.filter_item.TICKET_ESTIMATION_STATUS?.show &&
        this.filterDetails.filter_item.TICKET_ESTIMATION_STATUS?.multi &&
        this.filterModel.ticketEstimateStatusIds &&
        this.filterModel.ticketEstimateStatusIds.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.TICKET_ESTIMATION_STATUS,
          value: this.filterModel.ticketEstimateStatusIds
        });
      }
      if (
        this.filterDetails.filter_item.BILLING_STATUS?.show &&
        this.filterModel.TicketBillingStatusIds &&
        this.filterModel.TicketBillingStatusIds.length
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.BILLING_STATUS,
          value: this.filterModel.TicketBillingStatusIds
        });
      }
      if (this.filterDetails.filter_item.OPEN_DATE?.show && this.filterModel.openDate) {
        this.filterModel.openDate.start = new Date(this.filterModel.openDate.start);
        this.filterModel.openDate.end = new Date(this.filterModel.openDate.end);
        filterArrayObj.push({
          text: this.filterTitleList.OPEN_DATE,
          value: this.filterModel.openDate
        });
      }
      if (this.filterDetails.filter_item.START_DATE?.show && this.filterModel.startDate) {
        this.filterModel.startDate = new Date(this.filterModel.startDate);
        filterArrayObj.push({
          text: this.filterTitleList.START_DATE,
          value: this.filterModel.startDate
        });
      }
      if (this.filterDetails.filter_item.END_DATE?.show && this.filterModel.endDate) {
        this.filterModel.endDate = new Date(this.filterModel.endDate);
        filterArrayObj.push({
          text: this.filterTitleList.END_DATE,
          value: this.filterModel.endDate
        });
      }
      if (this.filterDetails.filter_item.ACTIVITY_RANGE?.show && this.filterModel.activityRange) {
        this.filterModel.activityRange.start = new Date(this.filterModel.activityRange.start);
        this.filterModel.activityRange.end = new Date(this.filterModel.activityRange.end);
        filterArrayObj.push({
          text: this.filterTitleList.ACTIVITY_RANGE,
          value: this.filterModel.activityRange
        });
      }
      if (this.filterDetails.filter_item.DATE?.show && this.filterModel.date) {
        this.filterModel.date.start = new Date(this.filterModel.date.start);
        this.filterModel.date.end = new Date(this.filterModel.date.end);
        filterArrayObj.push({
          text: this.filterTitleList.DATE,
          value: this.filterModel.date
        });
      }
      if (this.filterDetails.filter_item.CLOSE_DATE?.show && this.filterModel.closeDate) {
        this.filterModel.closeDate.start = new Date(this.filterModel.closeDate.start);
        this.filterModel.closeDate.end = new Date(this.filterModel.closeDate.end);
        filterArrayObj.push({
          text: this.filterTitleList.CLOSE_DATE,
          value: this.filterModel.closeDate
        });
      }
      if (this.filterDetails.filter_item.EXCLUSION_TO?.show && this.filterModel.exclusionTo) {
        this.filterModel.exclusionTo.start = new Date(this.filterModel.exclusionTo.start);
        this.filterModel.exclusionTo.end = new Date(this.filterModel.exclusionTo.end);
        filterArrayObj.push({
          text: this.filterTitleList.EXCLUSION_TO,
          value: this.filterModel.exclusionTo
        });
      }
      if (this.filterDetails.filter_item.EXCLUSION_FROM?.show && this.filterModel.exclusionFrom) {
        this.filterModel.exclusionFrom.start = new Date(this.filterModel.exclusionFrom.start);
        this.filterModel.exclusionFrom.end = new Date(this.filterModel.exclusionFrom.end);
        filterArrayObj.push({
          text: this.filterTitleList.EXCLUSION_FROM,
          value: this.filterModel.exclusionFrom
        });
      }

      if (this.filterDetails.filter_item.STATUS?.show && !this.filterDetails.filter_item.STATUS?.multi && this.filterModel.ticketStatus) {
        filterArrayObj.push({
          text: this.filterTitleList.STATUS,
          value: this.filterModel.ticketStatus
        });
      }

      if (
        this.filterDetails.filter_item.TICKET_TYPE?.show &&
        !this.filterDetails.filter_item.TICKET_TYPE?.multi &&
        this.filterModel.ticketTypeId
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.TICKET_TYPE,
          value: this.filterModel.ticketTypeId
        });
      }
      if (
        this.filterDetails.filter_item.COSTS_TYPE?.show &&
        !this.filterDetails.filter_item.COSTS_TYPE?.multi &&
        this.filterModel.costTypeId
      ) {
        filterArrayObj.push({
          text: this.filterTitleList.COSTS_TYPE,
          value: this.filterModel.costTypeId
        });
      }

      if (this.filterDetails.filter_item.MINIMUM_AFFECTED_KW?.show && this.filterModel.affectedkw) {
        filterArrayObj.push({ text: this.filterTitleList.MINIMUM_AFFECTED_KW, value: this.filterModel.affectedkw });
      }
      if (this.filterDetails.filter_item.PRIORITY?.show && !this.filterDetails.filter_item.PRIORITY?.multi && this.filterModel.priorityId) {
        filterArrayObj.push({ text: this.filterTitleList.PRIORITY, value: this.filterModel.priorityId });
      }
      if (this.filterDetails.filter_item.SEARCH_BOX?.show && this.filterModel.search) {
        filterArrayObj.push({ text: this.filterTitleList.SEARCH_BOX, value: this.filterModel.search });
      }
      if (this.filterDetails.filter_item.ACTIVITY_START?.show && this.filterModel.activityRangeStart) {
        this.filterModel.activityRangeStart = new Date(this.filterModel.activityRangeStart);
        filterArrayObj.push({
          text: this.filterTitleList.ACTIVITY_START,
          value: this.filterModel.activityRangeStart
        });
      }

      if (this.filterDetails.filter_item.ACTIVITY_END?.show && this.filterModel.activityRangeEnd) {
        this.filterModel.activityRangeEnd = new Date(this.filterModel.activityRangeEnd);
        filterArrayObj.push({
          text: this.filterTitleList.ACTIVITY_END,
          value: this.filterModel.activityRangeEnd
        });
      }
      if (this.filterDetails.filter_item.TRUCK_ROLL?.show && this.filterModel.truckRoll && this.filterModel.truckRoll.length) {
        filterArrayObj.push({
          text: this.filterTitleList.TRUCK_ROLL,
          value: this.filterModel.truckRoll
        });
      }
      if (this.filterDetails.filter_item.RMA_TRACKING?.show && this.filterModel.rmaTracking) {
        filterArrayObj.push({
          text: this.filterTitleList.RMA_TRACKING,
          value: this.filterModel.rmaTracking
        });
      }
      if (this.filterDetails.filter_item.RMA_RETURN_REQUIRED?.show && this.filterModel.rmaReturnRequired) {
        filterArrayObj.push({
          text: this.filterTitleList.RMA_RETURN_REQUIRED,
          value: this.filterModel.rmaReturnRequired
        });
      }
      if (this.filterDetails.filter_item.RMA_COMPLETE?.show && this.filterModel.rmaComplete) {
        filterArrayObj.push({
          text: this.filterTitleList.RMA_COMPLETE,
          value: this.filterModel.rmaComplete
        });
      }
      if (this.filterDetails.filter_item.RESOLVED?.show && this.filterModel.isResolve) {
        filterArrayObj.push({
          text: this.filterTitleList.RESOLVED,
          value: this.filterModel.isResolve
        });
      }
      if (this.filterDetails.filter_item.SHOW_STATUS?.show && this.filterModel.isActive) {
        filterArrayObj.push({
          text: this.filterTitleList.SHOW_STATUS,
          value: this.filterModel.isActive
        });
      }
      if (this.filterDetails.filter_item.SHOW_NERC?.show && this.filterModel.isNERC) {
        filterArrayObj.push({
          text: this.filterTitleList.SHOW_NERC,
          value: this.filterModel.isNERC
        });
      }
      if (this.filterDetails.filter_item.AUTOMATION_DATA_SOURCE?.show && this.filterModel.automationPartnerIds.length) {
        filterArrayObj.push({
          text: this.filterTitleList.AUTOMATION_DATA_SOURCE,
          value: this.filterModel.automationPartnerIds
        });
        tempArray.push(this.siteService.getAllAutomationSitesByDataSource(this.filterModel.automationPartnerIds));
        tempArrayObj.push('automationSitesList');
      } else {
        if (this.filterDetails.filter_item.AUTOMATION_SITE?.show) {
          tempArray.push(this.siteService.getAllAutomationSitesByDataSource(this.filterModel.automationPartnerIds));
          tempArrayObj.push('automationSitesList');
        }
      }
      if (this.filterDetails.filter_item.AUTOMATION_SITE?.show && this.filterModel.automationSiteIds.length) {
        filterArrayObj.push({
          text: this.filterTitleList.AUTOMATION_SITE,
          value: this.filterModel.automationSiteIds
        });
      }
      if (this.filterDetails.filter_item.IS_RESCHEDULED?.show) {
        if (this.filterModel.IsRescheduled) {
          filterArrayObj.push({ text: this.filterTitleList.IS_RESCHEDULED, value: this.filterModel.IsRescheduled });
        }
      }
      if (this.filterDetails.filter_item.IS_UNSCHEDULED?.show) {
        if (this.filterModel.isUnScheduled) {
          filterArrayObj.push({ text: this.filterTitleList.IS_UNSCHEDULED, value: this.filterModel.isUnScheduled });
        }
      }
      if (this.filterDetails.filter_item.IS_TENTATIVE_MONTH?.show) {
        if (this.filterModel.IsTentavieMonth) {
          filterArrayObj.push({ text: this.filterTitleList.IS_TENTATIVE_MONTH, value: this.filterModel.IsTentavieMonth });
        }
      }
      if (this.filterDetails.filter_item.IS_ACTIVE_NOTIFICATION?.show) {
        this.filterModel.isActive = true;
        if (this.filterModel.isActive) {
          filterArrayObj.push({ text: this.filterTitleList.IS_ACTIVE_NOTIFICATION, value: this.filterModel.isActive });
        }
      }
      if (this.filterDetails.filter_item.IS_INACTIVE_MISSING_CONFIG_NOTIFICATION?.show) {
        if (this.filterModel.missingConfigOrInactive) {
          filterArrayObj.push({
            text: this.filterTitleList.IS_INACTIVE_MISSING_CONFIG_NOTIFICATION,
            value: this.filterModel.missingConfigOrInactive
          });
        }
      }
      if (this.filterDetails.filter_item.FIELD_TECH_IDS?.show) {
        if (this.filterModel.FieldTechIds && this.filterModel.FieldTechIds.length) {
          filterArrayObj.push({
            text: this.filterTitleList.FIELD_TECH_IDS,
            value: this.filterModel?.FieldTechIds
          });
        }
      }
      if (this.filterDetails.filter_item.CONTROL_TYPE_IDS?.show) {
        if (this.filterModel.controlTypeIds && this.filterModel.controlTypeIds.length) {
          filterArrayObj.push({
            text: this.filterTitleList.CONTROL_TYPE_IDS,
            value: this.filterModel?.controlTypeIds
          });
        }
      }
      if (this.filterDetails.filter_item.CONTROL_DATA_TYPE_IDS?.show) {
        if (this.filterModel.controlDataTypeIds && this.filterModel.controlDataTypeIds.length) {
          filterArrayObj.push({
            text: this.filterTitleList.CONTROL_DATA_TYPE_IDS,
            value: this.filterModel?.controlDataTypeIds
          });
        }
      }
      if (this.filterDetails.filter_item.STATE?.show) {
        if (this.filterModel.states && this.filterModel.states.length) {
          filterArrayObj.push({
            text: this.filterTitleList.STATE,
            value: this.filterModel?.states
          });
        }
      }
      if (this.filterDetails.filter_item.REGION?.show) {
        if (this.filterModel.regionIds && this.filterModel.regionIds.length) {
          filterArrayObj.push({
            text: this.filterTitleList.REGION,
            value: this.filterModel?.regionIds
          });
        }
      }
      if (this.filterDetails.filter_item.SUB_REGION?.show) {
        if (this.filterModel.subregionIds && this.filterModel.subregionIds.length) {
          filterArrayObj.push({
            text: this.filterTitleList.SUB_REGION,
            value: this.filterModel?.subregionIds
          });
        }
      }
      this.getAllLists(tempArray, tempArrayObj, filterArrayObj);
    } else {
      if (this.isFilterAllowedWithoutButton() && !this.isSiteAuditPages()) {
        this.filterModel = this.storageService.mergeSharedFiltersIntoModel(this.filterModel, this.filterDetails.page_name);
      }
      if (this.filterDetails.filter_item.PORTFOLIO?.show) {
        const allReportDropdown = new AllReportDropdown();
        allReportDropdown.ids = [];
        tempArray.push(this.portfolioService.getAllReportPortfoliosByCustomerId(allReportDropdown));
        tempArrayObj.push('portfolioList');
      }
      if (this.filterDetails.filter_item.SITE?.show) {
        const allReportDropdown = new AllReportDropdown();
        allReportDropdown.ids = [];
        allReportDropdown.customerIds = [];
        tempArray.push(this.siteService.getAllReportSitesByPortfolioId(allReportDropdown));
        tempArrayObj.push('siteList');
      }
      if (this.filterDetails.filter_item.START_YEAR?.show) {
        this.years = this.commonService.getYear(false, true);
        if (this.filterDetails.filter_item.START_YEAR?.multi) {
          this.filterModel.years = [this.commonService.getCurrentYear()];
          this.addFilter({ text: this.filterTitleList.START_YEAR, name: this.filterModel.years }, this.filterTitleList.START_YEAR);
        } else {
          this.filterModel.year = this.commonService.getCurrentYear();
          this.addFilter({ text: this.filterTitleList.START_YEAR, name: this.filterModel.year }, this.filterTitleList.START_YEAR);
        }
      }
      if (this.filterDetails.filter_item.ASSESSMENT_TYPE?.show) {
        this.getReportType();
      }
      if (this.filterDetails.filter_item.DEVICE_TYPE?.show) {
        tempArray.push(this.siteDeviceService.getAllDeviceType());
        tempArrayObj.push('deviceTypeList');
      }
      if (this.filterDetails.filter_item.AUTOMATION_SITE?.show) {
        tempArray.push(this.siteService.getAllAutomationSitesByDataSource(this.filterModel.automationPartnerIds));
        tempArrayObj.push('automationSitesList');
      }
      if (this.filterDetails.filter_item.MODEL?.show) {
        this.devicesModelList.push(this.siteDeviceService.getDeviceModel());
        this.siteDeviceService.getDeviceModel().subscribe(res => {
          this.devicesModelList = res.map((element: any) => ({
            ...element,
            name: element.name + ' [' + element.mfgName + ']'
          }));
          this.updateModelListValues(true);
        });
        tempArray.push(this.devicesModelList);
        tempArrayObj.push('modelList');
      }
      if (this.filterDetails.filter_item.MFG?.show) {
        tempArray.push(this.siteDeviceService.getDeviceMfg());
        tempArrayObj.push('mfgList');
      }

      if (this.filterDetails.filter_item.QE_DATE_DURATION?.show) {
        this.filterModel.dateDuration = this.dateDurationTypeList[0].id;
        this.onDateDurationSelect();
        this.addFilter(this.filterModel.dateDuration, this.filterTitleList.QE_DATE_DURATION);
        this.refreshListInParent();
      }

      if (this.filterDetails.filter_item.USER_STATUS?.show) {
        this.filterModel.isActive = this.userStatusList.find(item => item.id === USER_STATUS_ENUM.ACTIVE_AND_INACTIVE).id;
        this.addFilter({ name: this.filterModel.isActive }, this.filterTitleList.USER_STATUS);
      }

      if (this.filterDetails.filter_item.BILLING_STATUS?.show) {
        filterArrayObj.push({
          text: this.filterTitleList.BILLING_STATUS,
          value: this.filterModel.TicketBillingStatusIds
        });
      }

      this.getAllLists(tempArray, tempArrayObj);
    }
  }

  updateModelListValues(isLoadedFirstTime = false) {
    if (this.filterModel?.mfgs?.length) {
      this.modelList = this.devicesModelList.filter(item => this.filterModel.mfgs.includes(item.mfgName));
    } else {
      this.modelList = this.devicesModelList;
    }
    if (!isLoadedFirstTime) {
      this.filterModel.deviceModels = [];
      this.addFilter(this.modelList, this.filterTitleList.MODEL, 'deviceModels', 'name');
    }
  }

  getAllLists(apiArray: any, mapResultList: string[], filterArrayObj = []) {
    if (apiArray.length) {
      this.loading = true;
      forkJoin(apiArray).subscribe({
        next: (res: any) => {
          for (const [index, value] of mapResultList.entries()) {
            if (this.filterDetails.page_name === 'siteDashboardPage' || this.filterDetails.page_name === 'dashboardPage') {
              if (value === 'customerList' || value === 'portfolioList' || value === 'siteList') {
                this[value] = res[index].filter(item => item.isActive);
              } else {
                this[value] = res[index];
              }
            } else {
              if (value === 'modelList') {
                if (Array.isArray(res[index])) {
                  this[value] = res[index].map((element: any) => ({
                    ...element,
                    name: element.name + ' [' + element.mfgName + ']'
                  }));
                }
              } else {
                this[value] = res[index];
              }
            }
            if (value === 'fieldTechList' && this.filterDetails.filter_item.USER?.show) {
              this.setQEUserList();
            }
            if (value === 'qeCategoryTypeList' && this.filterDetails.filter_item.QE_SERVICE_TYPE?.show) {
              this.setQEServiceTypeDropDown();
            }
            if (
              (value === 'userFilters' && this.filterDetails.filter_item.USER_NAME?.show) ||
              this.filterDetails.filter_item.USER_EMAIL?.show ||
              this.filterDetails.filter_item.USER_COMPANY?.show
            ) {
              this.userNamesList = res[index].userNames ?? [];
              this.userEmailsList = res[index].userEmails ?? [];
              this.companyNamesList = res[index].companyNames ?? [];
            }
          }
          if (filterArrayObj.length) {
            this.setFilterHeaders(filterArrayObj);
          }
          setTimeout(() => {
            this.loading = false;
          }, 1000);
        },
        error: e => {
          this.loading = false;
        }
      });
    } else if (filterArrayObj.length) {
      this.setFilterHeaders(filterArrayObj);
    }
  }

  setFilterHeaders(filteredObj) {
    for (const i of filteredObj) {
      if (i.text === this.filterTitleList.CUSTOMER) {
        let name = this.customerList.find(item => item.id === this.filterModel.customerIds[0]).name;
        name += this.filterModel.customerIds.length > 1 ? ` +${this.filterModel.customerIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.CREW_MEMBER) {
        let name = this.crewMemberList.find(item => item.name === this.filterModel.crewMemberNames[0]).name;
        name += this.filterModel.crewMemberNames.length > 1 ? ` +${this.filterModel.crewMemberNames.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.PORTFOLIO) {
        let name = this.portfolioList.find(item => item.id === this.filterModel.portfolioIds[0]).name;
        name += this.filterModel.portfolioIds.length > 1 ? ` +${this.filterModel.portfolioIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.SITE) {
        let name = this.siteList.find(item => item.id === this.filterModel.siteIds[0]).name;
        name += this.filterModel.siteIds.length > 1 ? ` +${this.filterModel.siteIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.STATE) {
        let name = this.stateList.find(item => item.name === this.filterModel.states[0]).name;
        name += this.filterModel.states.length > 1 ? ` +${this.filterModel.states.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.STATUS && this.filterDetails.filter_item.STATUS.multi) {
        let name = this.ticketStatus.find(item => item.id === this.filterModel.statusIds[0]).name;
        name += this.filterModel.statusIds.length > 1 ? ` +${this.filterModel.statusIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.STATUS && !this.filterDetails.filter_item.STATUS.multi) {
        const val = this.ticketStatus.find(data => data.id === i.value);
        this.addFilter({ name: val?.name }, i.text);
      } else if (i.text === this.filterTitleList.TICKET_TYPE && this.filterDetails.filter_item.TICKET_TYPE.multi) {
        let name = this.ticketTypeList.find(item => item.id === this.filterModel.ticketTypeIds[0]).name;
        name += this.filterModel.ticketTypeIds.length > 1 ? ` +${this.filterModel.ticketTypeIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.TICKET_TYPE && !this.filterDetails.filter_item.TICKET_TYPE.multi) {
        const val = this.ticketTypeList.find(data => data.id === i.value);
      } else if (i.text === this.filterTitleList.COSTS_TYPE && this.filterDetails.filter_item.COSTS_TYPE.multi) {
        let name = this.costTypeList.find(item => item.id === this.filterModel.costTypeIds[0]).name;
        name += this.filterModel.costTypeIds.length > 1 ? ` +${this.filterModel.costTypeIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.COSTS_TYPE && !this.filterDetails.filter_item.COSTS_TYPE.multi) {
        const val = this.costTypeList.find(data => data.id === i.value);
        this.addFilter({ name: val?.name }, i.text);
      } else if (i.text === this.filterTitleList.PRIORITY && this.filterDetails.filter_item.PRIORITY?.multi) {
        let name = this.ticketPriority.find(item => item.id === this.filterModel.priorityIds[0]).name;
        name += this.filterModel.priorityIds.length > 1 ? ` +${this.filterModel.priorityIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (
        i.text === this.filterTitleList.TICKET_ESTIMATION_STATUS &&
        this.filterDetails.filter_item.TICKET_ESTIMATION_STATUS?.multi
      ) {
        let name = this.ticketEstimateStatus.find(item => item.id === this.filterModel.ticketEstimateStatusIds[0]).name;
        name += this.filterModel.ticketEstimateStatusIds.length > 1 ? ` +${this.filterModel.ticketEstimateStatusIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.BILLING_STATUS) {
        let name = this.ticketBillingStatuses.find(
          item => item.ticketBillingStatusID === this.filterModel.TicketBillingStatusIds[0]
        ).description;
        name += this.filterModel.TicketBillingStatusIds.length > 1 ? ` +${this.filterModel.TicketBillingStatusIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text, 'TicketBillingStatusIds');
      } else if (i.text === this.filterTitleList.OPEN_DATE) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.START_DATE) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.END_DATE) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.ACTIVITY_RANGE) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.DATE) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.CLOSE_DATE) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.EXCLUSION_TO) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.EXCLUSION_FROM) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.MINIMUM_AFFECTED_KW) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.PRIORITY && !this.filterDetails.filter_item.PRIORITY?.multi) {
        const val = this.ticketPriority.find(data => data.id === i.value);
        this.addFilter({ name: val.name }, i.text);
      } else if (i.text === this.filterTitleList.QE_TECH) {
        let name = this.fieldTechList.find(item => item.id === this.filterModel.userIds[0]).name;
        name += this.filterModel.userIds.length > 1 ? ` +${this.filterModel.userIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.REGION) {
        let name = this.regionList.find(item => item.id === this.filterModel.regionIds[0]).name;
        name += this.filterModel.regionIds.length > 1 ? ` +${this.filterModel.regionIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.SUB_REGION) {
        let name = this.subRegionList.find(item => item.id === this.filterModel.subregionIds[0]).name;
        name += this.filterModel.subregionIds.length > 1 ? ` +${this.filterModel.subregionIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.USER) {
        let name = this.qeUserList.find(item => item.id === this.filterModel.userIds[0]).name;
        name += this.filterModel.userIds.length > 1 ? ` +${this.filterModel.userIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.START_YEAR && this.filterDetails.filter_item.START_YEAR.multi) {
        let name = this.years.find(item => item.id === this.filterModel.years[0]).name;
        name += this.filterModel.years.length > 1 ? ` +${this.filterModel.years.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.START_YEAR && !this.filterDetails.filter_item.START_YEAR.multi) {
        const val = this.years.find(data => data.id === i.value);
        this.addFilter({ name: val?.name }, i.text);
      } else if (i.text === this.filterTitleList.ASSESSMENT_TYPE && this.filterModel.reportTypeIds.length) {
        let name = this.reportTypeData.find(item => item.id === this.filterModel.reportTypeIds[0]).name;
        name += this.filterModel.reportTypeIds.length > 1 ? ` +${this.filterModel.reportTypeIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.REPORT_STATUS) {
        const val = this.reportstatus.find(data => data.id === i.value);
        this.addFilter({ name: val.name }, i.text);
      } else if (i.text === this.filterTitleList.WORKORDER_STATUS) {
        const val = this.woDropstatusList.find(data => data.id === i.value);
        this.addFilter({ name: val.name }, i.text);
      } else if (i.text === this.filterTitleList.SITE_AUDIT_REPORT_STATUS) {
        const val = this.saReportStatusList.find(data => data.id === i.value);
        this.addFilter({ name: val.name }, i.text);
      } else if (i.text === this.filterTitleList.SHOW_NERC_SITE_TYPE) {
        const val = this.nercSiteTypeDropDown.find(data => data.id === i.value);
        this.addFilter({ name: val.name }, i.text);
      } else if (i.text === this.filterTitleList.QE_SERVICE_TYPE && this.filterModel.qeServiceTypes.length) {
        this.addFilter(this.qeServiceTypeDropDownOption, this.filterTitleList.QE_SERVICE_TYPE, 'qeServiceTypes');
      } else if (
        i.text === this.filterTitleList.USER_TYPE &&
        this.filterModel.userTypeIds.length &&
        this.filterDetails.filter_item.USER_TYPE.multi
      ) {
        let name = this.userTypeList.find(item => item.id === this.filterModel.userTypeIds[0]).name;
        name += this.filterModel.userTypeIds.length > 1 ? ` +${this.filterModel.userTypeIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (
        i.text === this.filterTitleList.USER_TYPE &&
        this.filterModel.userTypeId &&
        !this.filterDetails.filter_item.USER_TYPE.multi
      ) {
        let name = this.userTypeList.find(item => item.id === this.filterModel.userTypeId).name;
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.QE_MODULES && this.filterModel.qeMenuModuleIds.length) {
        let name = this.qeMenuModuleTypeList.find(item => item.id === this.filterModel.qeMenuModuleIds[0]).name;
        name += this.filterModel.qeMenuModuleIds.length > 1 ? ` +${this.filterModel.qeMenuModuleIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.QE_DATE_DURATION && this.filterModel.dateDuration) {
        let name = this.filterModel.durationStartDate as unknown as String;
        name += this.filterModel.durationEndDate ? ` - ${this.filterModel.durationEndDate}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (
        i.text === this.filterTitleList.USER_NAME &&
        this.filterModel.userNames.length &&
        this.filterDetails.filter_item.USER_NAME.multi
      ) {
        let name = this.userNamesList.find(item => item.name === this.filterModel.userNames[0]).name;
        name += this.filterModel.userNames.length > 1 ? ` +${this.filterModel.userNames.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (
        i.text === this.filterTitleList.USER_NAME &&
        this.filterModel.userName &&
        !this.filterDetails.filter_item.USER_NAME.multi
      ) {
        let name = this.userNamesList.find(item => item.name === this.filterModel.userName).name;
        this.addFilter([{ name: name }], i.text);
      } else if (
        i.text === this.filterTitleList.USER_EMAIL &&
        this.filterModel.emails.length &&
        this.filterDetails.filter_item.USER_EMAIL.multi
      ) {
        let name = this.userEmailsList.find(item => item.name === this.filterModel.emails[0]).name;
        name += this.filterModel.emails.length > 1 ? ` +${this.filterModel.emails.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.USER_EMAIL && this.filterModel.email && !this.filterDetails.filter_item.USER_EMAIL.multi) {
        let name = this.userEmailsList.find(item => item.name === this.filterModel.email).name;
        this.addFilter([{ name: name }], i.text);
      } else if (
        i.text === this.filterTitleList.USER_COMPANY &&
        this.filterModel.companyNames.length &&
        this.filterDetails.filter_item.USER_COMPANY.multi
      ) {
        let name = this.companyNamesList.find(item => item.name === this.filterModel.companyNames[0]).name;
        name += this.filterModel.companyNames.length > 1 ? ` +${this.filterModel.companyNames.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (
        i.text === this.filterTitleList.USER_COMPANY &&
        this.filterModel.companyName &&
        !this.filterDetails.filter_item.USER_COMPANY.multi
      ) {
        let name = this.companyNamesList.find(item => item.name === this.filterModel.companyName).name;
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.USER_STATUS && 'isActive' in this.filterModel) {
        let name = this.userStatusList.find(item => item.id === this.filterModel.isActive).name;
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.FREQUENCY_TYPE && this.filterModel.frequencies.length) {
        let name = this.frequencytype.find(item => item.name === this.filterModel.frequencies[0]).name;
        name += this.filterModel.frequencies.length > 1 ? ` +${this.filterModel.frequencies.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.ARRAY_TYPE && this.filterModel.arrayTypeIds.length) {
        let name = this.arrayTypeList.find(item => item.id === this.filterModel.arrayTypeIds[0]).name;
        name += this.filterModel.arrayTypeIds.length > 1 ? ` +${this.filterModel.arrayTypeIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.SEARCH_BOX) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.DEVICE_TYPE && this.filterModel.deviceTypeIds.length) {
        let name = this.deviceTypeList.find(item => item.id === this.filterModel.deviceTypeIds[0]).name;
        name += this.filterModel.deviceTypeIds.length > 1 ? ` +${this.filterModel.deviceTypeIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.MODEL && this.filterModel.deviceModels.length) {
        let name = this.modelList.find(item => item.name === this.filterModel.deviceModels[0])?.name;
        name += this.filterModel.deviceModels.length > 1 ? ` +${this.filterModel.deviceModels.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.MFG && this.filterModel.mfgs.length) {
        let name = this.mfgList.find(item => item.name === this.filterModel.mfgs[0])?.name;
        name += this.filterModel.mfgs.length > 1 ? ` +${this.filterModel.mfgs.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
        this.updateModelListValues(true);
      } else if (i.text === this.filterTitleList.DEVICE) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.SHOW_DELETED) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.SHOW_ARCHIVED) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.ACTIVITY_START) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.ACTIVITY_END) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.TRUCK_ROLL) {
        let name = this.truckRollTypeList.find(item => item.id === this.filterModel.truckRoll[0]).name;
        name += this.filterModel.truckRoll.length > 1 ? ` +${this.filterModel.truckRoll.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.RESOLVED) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.RMA_COMPLETE) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.RMA_TRACKING) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.RMA_RETURN_REQUIRED) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.SHOW_STATUS) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.SITE_AUDIT_REPORT_CUSTOMER) {
        let name = this.siteAuditReportCustomerList.find(item => item.name === this.filterModel.customerNames[0]).name;
        name += this.filterModel.customerNames.length > 1 ? ` +${this.filterModel.customerNames.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.SITE_AUDIT_REPORT_PORTFOLIO) {
        let name = this.siteAuditReportPortfolioList.find(item => item.name === this.filterModel.portfolioNames[0]).name;
        name += this.filterModel.portfolioNames.length > 1 ? ` +${this.filterModel.portfolioNames.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.SITE_AUDIT_CUSTOMER) {
      } else if (i.text === this.filterTitleList.SHOW_NERC) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.SITE_AUDIT_CUSTOMER) {
        let name = this.siteAuditCustomerList.find(item => item.name === this.filterModel.customerNames[0]).name;
        name += this.filterModel.customerNames.length > 1 ? ` +${this.filterModel.customerNames.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.SITE_AUDIT_PORTFOLIO) {
        let name = this.siteAuditPortfolioList.find(item => item.name === this.filterModel.portfolioNames[0]).name;
        name += this.filterModel.portfolioNames.length > 1 ? ` +${this.filterModel.portfolioNames.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.SITE_AUDIT_SITE) {
        let name = this.siteAuditSiteList.find(item => item.name === this.filterModel.siteNames[0]).name;
        name += this.filterModel.siteNames.length > 1 ? ` +${this.filterModel.siteNames.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.SITE_AUDIT_REPORT_SITE) {
        let name = this.siteAuditReportSiteList.find(item => item.name === this.filterModel.siteNames[0]).name;
        name += this.filterModel.siteNames.length > 1 ? ` +${this.filterModel.siteNames.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.SITE_AUDIT_CREW_MEMBER) {
        let name = this.SiteAuditCrewMemberList.find(item => item.name === this.filterModel.crewMemberNames[0]).name;
        name += this.filterModel.crewMemberNames.length > 1 ? ` +${this.filterModel.crewMemberNames.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.AUTOMATION_DATA_SOURCE) {
        let name = this.dataSourceList.find(item => item.id === this.filterModel.automationPartnerIds[0]).name;
        name += this.filterModel.automationPartnerIds.length > 1 ? ` +${this.filterModel.automationPartnerIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.AUTOMATION_SITE) {
        let name = this.automationSitesList.find(item => item.siteNumber === this.filterModel.automationSiteIds[0]).name;
        name += this.filterModel.automationSiteIds.length > 1 ? ` +${this.filterModel.automationSiteIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.IS_RESCHEDULED) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.IS_UNSCHEDULED) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.IS_TENTATIVE_MONTH) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.IS_ACTIVE_NOTIFICATION) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.IS_INACTIVE_MISSING_CONFIG_NOTIFICATION) {
        this.addFilter({ name: i.value }, i.text);
      } else if (i.text === this.filterTitleList.FIELD_TECH_IDS) {
        let name = this.fieldTechList.find(item => item.id === this.filterModel.FieldTechIds[0]).name;
        name += this.filterModel.FieldTechIds.length > 1 ? ` +${this.filterModel.FieldTechIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.TEMPLATE_TYPE_IDS && this.filterModel.templateTypeIds.length) {
        let name = this.templateTypeList.find(item => item.templateTypeId === this.filterModel.templateTypeIds[0])?.templateTypeName;
        name += this.filterModel.templateTypeIds.length > 1 ? ` +${this.filterModel.templateTypeIds.length - 1}` : '';
        this.addFilter([{ templateTypeName: name }], i.text);
      } else if (i.text === this.filterTitleList.TEMPLATE_TYPE_FORM_LIST_IDS && this.filterModel.templateTypeId) {
        const name = this.templateTypeList.find(item => item.templateTypeId === i.value).templateTypeName ?? '';
        this.addFilter({ templateTypeName: name }, i.text);
      } else if (i.text === this.filterTitleList.EQUIPMENT_LIST && this.filterModel.equipmentIds.length) {
        let name = this.equipmentList.find(item => item.id === this.filterModel.equipmentIds[0])?.equipmentModel;
        name += this.filterModel.equipmentIds.length > 1 ? ` +${this.filterModel.equipmentIds.length - 1}` : '';
        this.addFilter([{ equipmentModel: name }], i.text);
      } else if (i.text === this.filterTitleList.CONTROL_TYPE_IDS) {
        let name = this.controlTypeList.find(item => item.id === this.filterModel.controlTypeIds[0]).name;
        name += this.filterModel.controlTypeIds.length > 1 ? ` +${this.filterModel.controlTypeIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      } else if (i.text === this.filterTitleList.CONTROL_DATA_TYPE_IDS) {
        let name = this.tagTypeList.find(item => item.id === this.filterModel.controlDataTypeIds[0]).name;
        name += this.filterModel.controlDataTypeIds.length > 1 ? ` +${this.filterModel.controlDataTypeIds.length - 1}` : '';
        this.addFilter([{ name: name }], i.text);
      }
    }
  }

  getAndSetFilteredArray(key: string) {
    if (this.filterModelCopy[key].length) {
      this.filterModel[key] = this.filterModel[key].filter(x => !this.filterModelCopy[key].includes(x));
    } else {
      this.filterModel[key] = [];
    }

    return [...this.filterModel[key]];
  }

  clearSingleFilter(clearFor: string, removeFilter = false) {
    let list = [];
    let arrayOfIds = [];

    if (clearFor === this.filterTitleList.CUSTOMER && this.filterDetails.filter_item.CUSTOMER?.multi) {
      arrayOfIds = this.getAndSetFilteredArray('customerIds');
      this.onCustomerSelectDeSelect(removeFilter);
      list = [this.filterTitleList.CUSTOMER, this.filterTitleList.PORTFOLIO, this.filterTitleList.SITE];
    } else if (clearFor === this.filterTitleList.CUSTOMER && !this.filterDetails.filter_item.CUSTOMER?.multi) {
      this.filterModel.customerId = null;
      this.filterModel.customerIds = [];
      this.onCustomerSelectDeSelect(removeFilter);
      list = [this.filterTitleList.CUSTOMER];
    } else if (clearFor === this.filterTitleList.CREW_MEMBER) {
      arrayOfIds = this.getAndSetFilteredArray('crewMemberNames');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.CREW_MEMBER];
    } else if (clearFor === this.filterTitleList.PORTFOLIO) {
      arrayOfIds = this.getAndSetFilteredArray('portfolioIds');
      if (removeFilter) {
        this.onPortfolioSelectDeSelect(removeFilter);
      }
      list = [this.filterTitleList.PORTFOLIO, this.filterTitleList.SITE];
    } else if (clearFor === this.filterTitleList.SITE) {
      arrayOfIds = this.getAndSetFilteredArray('siteIds');
      if (removeFilter) {
        this.onFilterChange(removeFilter);
      }
      list = [this.filterTitleList.SITE];
    } else if (clearFor === this.filterTitleList.STATE) {
      arrayOfIds = this.getAndSetFilteredArray('states');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.STATE];
    } else if (clearFor === this.filterTitleList.REGION) {
      arrayOfIds = this.getAndSetFilteredArray('regionIds');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.REGION];
    } else if (clearFor === this.filterTitleList.SUB_REGION) {
      arrayOfIds = this.getAndSetFilteredArray('subregionIds');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SUB_REGION];
    } else if (clearFor === this.filterTitleList.STATUS) {
      if (this.filterDetails.filter_item.STATUS?.multi) {
        arrayOfIds = this.getAndSetFilteredArray('statusIds');
      } else {
        this.filterModel.ticketStatus = null;
      }
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.STATUS];
    } else if (clearFor === this.filterTitleList.TICKET_TYPE) {
      if (this.filterDetails.filter_item.TICKET_TYPE?.multi) {
        arrayOfIds = this.getAndSetFilteredArray('ticketTypeIds');
      } else {
        this.filterModel.ticketTypeId = null;
      }
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.TICKET_TYPE];
    } else if (clearFor === this.filterTitleList.COSTS_TYPE) {
      if (this.filterDetails.filter_item.COSTS_TYPE?.multi) {
        arrayOfIds = this.getAndSetFilteredArray('costTypeIds');
      } else {
        this.filterModel.costTypeId = null;
      }
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.COSTS_TYPE];
    } else if (clearFor === this.filterTitleList.PRIORITY && this.filterDetails.filter_item.PRIORITY?.multi) {
      arrayOfIds = this.getAndSetFilteredArray('priorityIds');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.PRIORITY];
    } else if (
      clearFor === this.filterTitleList.TICKET_ESTIMATION_STATUS &&
      this.filterDetails.filter_item.TICKET_ESTIMATION_STATUS?.multi
    ) {
      arrayOfIds = this.getAndSetFilteredArray('ticketEstimateStatusIds');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.TICKET_ESTIMATION_STATUS];
    } else if (clearFor === this.filterTitleList.OPEN_DATE) {
      this.filterModel.openDate = null;
      this.fromToDateChanged(this.filterModel.openDate);
      list = [this.filterTitleList.OPEN_DATE];
    } else if (clearFor === this.filterTitleList.START_DATE) {
      this.filterModel.startDate = null;
      this.onSingleDateChanged(this.filterModel.startDate);
      list = [this.filterTitleList.START_DATE];
    } else if (clearFor === this.filterTitleList.END_DATE) {
      this.filterModel.endDate = null;
      this.onSingleDateChanged(this.filterModel.endDate);
      list = [this.filterTitleList.END_DATE];
    } else if (clearFor === this.filterTitleList.ACTIVITY_RANGE) {
      this.addRemoveIsExclusiveFilter();
      this.filterModel.activityRange = null;
      this.fromToDateChanged(this.filterModel.activityRange);
      list = [this.filterTitleList.ACTIVITY_RANGE];
    } else if (clearFor === this.filterTitleList.DATE) {
      this.filterModel.date = null;
      this.fromToDateChanged(this.filterModel.date);
      list = [this.filterTitleList.DATE];
    } else if (clearFor === this.filterTitleList.CLOSE_DATE) {
      this.filterModel.closeDate = null;
      this.fromToDateChanged(this.filterModel.closeDate);
      list = [this.filterTitleList.CLOSE_DATE];
    } else if (clearFor === this.filterTitleList.EXCLUSION_TO) {
      this.filterModel.exclusionTo = null;
      this.fromToDateChanged(this.filterModel.exclusionTo);
      list = [this.filterTitleList.EXCLUSION_TO];
    } else if (clearFor === this.filterTitleList.EXCLUSION_FROM) {
      this.filterModel.exclusionFrom = null;
      this.fromToDateChanged(this.filterModel.exclusionFrom);
      list = [this.filterTitleList.EXCLUSION_FROM];
    } else if (clearFor === this.filterTitleList.MINIMUM_AFFECTED_KW) {
      this.filterModel.affectedkw = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.MINIMUM_AFFECTED_KW];
    } else if (clearFor === this.filterTitleList.PRIORITY && !this.filterDetails.filter_item.PRIORITY?.multi) {
      this.filterModel.priorityId = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.PRIORITY];
    } else if (clearFor === this.filterTitleList.QE_TECH) {
      arrayOfIds = this.getAndSetFilteredArray('userIds');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.QE_TECH];
    } else if (clearFor === this.filterTitleList.USER) {
      arrayOfIds = this.getAndSetFilteredArray('userIds');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.USER];
    } else if (clearFor === this.filterTitleList.ASSESSMENT_TYPE) {
      arrayOfIds = this.getAndSetFilteredArray('reportTypeIds');
      this.addRemoveIsInclusiveFilter();
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.ASSESSMENT_TYPE];
    } else if (clearFor === this.filterTitleList.REPORT_STATUS) {
      this.filterModel.reportStatus = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.REPORT_STATUS];
    } else if (clearFor === this.filterTitleList.WORKORDER_STATUS) {
      this.filterModel.woStatus = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.WORKORDER_STATUS];
    } else if (clearFor === this.filterTitleList.SITE_AUDIT_REPORT_STATUS) {
      this.filterModel.reportStatusId = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SITE_AUDIT_REPORT_STATUS];
    } else if (clearFor === this.filterTitleList.SHOW_NERC_SITE_TYPE) {
      this.filterModel.nercSiteTypeId = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SHOW_NERC_SITE_TYPE];
    } else if (clearFor === this.filterTitleList.QE_SERVICE_TYPE) {
      this.filterModel.qeServiceTypes = [];
      this.filterModel.qeServiceScaleTypes = [];
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.QE_SERVICE_TYPE];
      this.handleServiceTypeStringId([]);
    } else if (clearFor === this.filterTitleList.USER_TYPE) {
      if (this.filterDetails.filter_item.USER_TYPE?.multi) {
        arrayOfIds = this.getAndSetFilteredArray('userTypeIds');
      } else {
        this.filterModel.userTypeId = null;
      }
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.USER_TYPE];
    } else if (clearFor === this.filterTitleList.QE_MODULES) {
      arrayOfIds = this.getAndSetFilteredArray('qeMenuModuleIds');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.QE_MODULES];
    } else if (clearFor === this.filterTitleList.QE_DATE_DURATION) {
      this.filterModel.dateDuration = this.dateDurationTypeList[0].id;
      this.onDateDurationSelect();
      this.onFilterChange(removeFilter);
      this.addFilter(this.filterModel.dateDuration, this.filterTitleList.QE_DATE_DURATION);
    } else if (clearFor === this.filterTitleList.USER_NAME) {
      if (this.filterDetails.filter_item.USER_NAME?.multi) {
        arrayOfIds = this.getAndSetFilteredArray('userNames');
      } else {
        this.filterModel.userName = null;
      }
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.USER_NAME];
    } else if (clearFor === this.filterTitleList.USER_EMAIL) {
      if (this.filterDetails.filter_item.USER_EMAIL?.multi) {
        arrayOfIds = this.getAndSetFilteredArray('emails');
      } else {
        this.filterModel.email = null;
      }
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.USER_EMAIL];
    } else if (clearFor === this.filterTitleList.USER_COMPANY) {
      if (this.filterDetails.filter_item.USER_COMPANY?.multi) {
        arrayOfIds = this.getAndSetFilteredArray('companyNames');
      } else {
        this.filterModel.companyName = null;
      }
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.USER_COMPANY];
    } else if (clearFor === this.filterTitleList.USER_STATUS) {
      this.filterModel.isActive = this.userStatusList.find(item => item.id === USER_STATUS_ENUM.ACTIVE_AND_INACTIVE).id;
      this.onFilterChange(removeFilter);
      this.addFilter({ name: this.filterModel.isActive }, this.filterTitleList.USER_STATUS);
    } else if (clearFor === this.filterTitleList.FREQUENCY_TYPE) {
      this.filterModel.frequencies = [];
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.FREQUENCY_TYPE];
    } else if (clearFor === this.filterTitleList.ARRAY_TYPE) {
      arrayOfIds = this.getAndSetFilteredArray('arrayTypeIds');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.ARRAY_TYPE];
    } else if (clearFor === this.filterTitleList.SEARCH_BOX) {
      this.filterModel.search = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SEARCH_BOX];
    } else if (clearFor === this.filterTitleList.DEVICE_TYPE) {
      arrayOfIds = this.getAndSetFilteredArray('deviceTypeIds');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.DEVICE_TYPE];
    } else if (clearFor === this.filterTitleList.DEVICE) {
      this.filterModel.DeviceName = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.DEVICE];
    } else if (clearFor === this.filterTitleList.MODEL) {
      arrayOfIds = this.getAndSetFilteredArray('deviceModels');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.MODEL];
    } else if (clearFor === this.filterTitleList.MFG) {
      arrayOfIds = this.getAndSetFilteredArray('mfgs');
      this.filterModel.deviceModels = null;
      this.updateModelListValues();
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.MFG];
    } else if (clearFor === this.filterTitleList.START_YEAR) {
      if (this.filterDetails.filter_item.START_YEAR?.multi) {
        if (this.filterModelCopy.years.length) {
          this.filterModel.years = this.filterModel.years.filter(x => !this.filterModelCopy.years.includes(x));
        } else {
          this.filterModel.years = [this.commonService.getCurrentYear()];
        }

        arrayOfIds = [...this.filterModel.years];
      } else {
        this.filterModel.year = this.commonService.getCurrentYear();
        this.addFilter({ text: this.filterTitleList.START_YEAR, name: this.filterModel.year }, this.filterTitleList.START_YEAR);
      }
      this.onFilterChange(removeFilter);
    } else if (clearFor === this.filterTitleList.SHOW_DELETED) {
      this.filterModel.isDelete = false;
      this.refreshListInParent();
      list = [this.filterTitleList.SHOW_DELETED];
    } else if (clearFor === this.filterTitleList.IS_LINK) {
      this.filterModel.isLink = false;
      this.refreshListInParent();
      list = [this.filterTitleList.IS_LINK];
    } else if (clearFor === this.filterTitleList.IS_INCLUSIVE_SEARCH) {
      this.filterModel.isInclusiveSearch = false;
      if (this.filterModel?.reportTypeIds?.length > 0) {
        this.refreshListInParent();
      }
      list = [this.filterTitleList.IS_INCLUSIVE_SEARCH];
    } else if (clearFor === this.filterTitleList.IS_EXCLUSIVE_SEARCH) {
      this.filterModel.isExclusiveSearch = false;
      if (this.filterModel?.reportTypeIds?.length > 0) {
        this.refreshListInParent();
      }
      list = [this.filterTitleList.IS_EXCLUSIVE_SEARCH];
    } else if (clearFor === this.filterTitleList.CHECK_IN_ONLY) {
      this.filterModel.checkInOnly = false;
      this.refreshListInParent();
      list = [this.filterTitleList.CHECK_IN_ONLY];
    } else if (clearFor === this.filterTitleList.SHOW_ARCHIVED) {
      this.filterModel.isArchive = false;
      this.refreshListInParent();
      list = [this.filterTitleList.SHOW_ARCHIVED];
    } else if (clearFor === this.filterTitleList.TRUCK_ROLL) {
      this.filterModel.truckRoll = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.TRUCK_ROLL];
    } else if (clearFor === this.filterTitleList.RESOLVED) {
      this.filterModel.isResolve = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.RESOLVED];
    } else if (clearFor === this.filterTitleList.RMA_COMPLETE) {
      this.filterModel.rmaComplete = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.RMA_COMPLETE];
    } else if (clearFor === this.filterTitleList.RMA_TRACKING) {
      this.filterModel.rmaTracking = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.RMA_TRACKING];
    } else if (clearFor === this.filterTitleList.RMA_RETURN_REQUIRED) {
      this.filterModel.rmaReturnRequired = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.RMA_RETURN_REQUIRED];
    } else if (clearFor === this.filterTitleList.SHOW_STATUS) {
      this.filterModel.isActive = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SHOW_STATUS];
    } else if (clearFor === this.filterTitleList.SHOW_NERC) {
      this.filterModel.isNERC = null;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SHOW_NERC];
    } else if (clearFor === this.filterTitleList.ACTIVITY_END) {
      this.filterModel.activityRangeEnd = null;
      this.fromToDateChanged(this.filterModel.activityRangeEnd);
      list = [this.filterTitleList.ACTIVITY_END];
    } else if (clearFor === this.filterTitleList.ACTIVITY_START) {
      this.filterModel.activityRangeStart = null;
      this.fromToDateChanged(this.filterModel.activityRangeStart);
      list = [this.filterTitleList.ACTIVITY_START];
    } else if (clearFor === this.filterTitleList.SITE_AUDIT_REPORT_CUSTOMER) {
      arrayOfIds = this.getAndSetFilteredArray('customerNames');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SITE_AUDIT_REPORT_CUSTOMER];
    } else if (clearFor === this.filterTitleList.SITE_AUDIT_REPORT_PORTFOLIO) {
      arrayOfIds = this.getAndSetFilteredArray('portfolioNames');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SITE_AUDIT_REPORT_PORTFOLIO];
    } else if (clearFor === this.filterTitleList.SITE_AUDIT_CUSTOMER) {
      arrayOfIds = this.getAndSetFilteredArray('customerNames');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SITE_AUDIT_CUSTOMER];
    } else if (clearFor === this.filterTitleList.SITE_AUDIT_PORTFOLIO) {
      arrayOfIds = this.getAndSetFilteredArray('portfolioNames');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SITE_AUDIT_PORTFOLIO];
    } else if (clearFor === this.filterTitleList.SITE_AUDIT_SITE) {
      arrayOfIds = this.getAndSetFilteredArray('siteNames');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SITE_AUDIT_SITE];
    } else if (clearFor === this.filterTitleList.SITE_AUDIT_REPORT_SITE) {
      arrayOfIds = this.getAndSetFilteredArray('siteNames');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SITE_AUDIT_REPORT_SITE];
    } else if (clearFor === this.filterTitleList.SITE_AUDIT_CREW_MEMBER) {
      arrayOfIds = this.getAndSetFilteredArray('crewMemberNames');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.SITE_AUDIT_CREW_MEMBER];
    } else if (clearFor === this.filterTitleList.AUTOMATION_DATA_SOURCE) {
      arrayOfIds = this.getAndSetFilteredArray('automationPartnerIds');
      // this.onDataSourceChanged(removeFilter);
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.AUTOMATION_DATA_SOURCE];
    } else if (clearFor === this.filterTitleList.AUTOMATION_SITE) {
      arrayOfIds = this.getAndSetFilteredArray('automationSiteIds');
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.AUTOMATION_SITE];
    } else if (clearFor === this.filterTitleList.IS_RESCHEDULED) {
      this.filterModel.IsRescheduled = false;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.IS_RESCHEDULED];
    } else if (clearFor === this.filterTitleList.IS_UNSCHEDULED) {
      this.filterModel.isUnScheduled = false;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.IS_UNSCHEDULED];
    } else if (clearFor === this.filterTitleList.IS_TENTATIVE_MONTH) {
      this.filterModel.IsTentavieMonth = false;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.IS_TENTATIVE_MONTH];
    } else if (clearFor === this.filterTitleList.IS_ACTIVE_NOTIFICATION) {
      this.filterModel.isActive = false;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.IS_ACTIVE_NOTIFICATION];
    } else if (clearFor === this.filterTitleList.IS_INACTIVE_MISSING_CONFIG_NOTIFICATION) {
      this.filterModel.missingConfigOrInactive = false;
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.IS_INACTIVE_MISSING_CONFIG_NOTIFICATION];
    } else if (clearFor === this.filterTitleList.FIELD_TECH_IDS) {
      arrayOfIds = this.getAndSetFilteredArray('FieldTechIds');
      this.filterModel.FieldTechIds = [];
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.FIELD_TECH_IDS];
    } else if (clearFor === this.filterTitleList.TEMPLATE_TYPE_IDS) {
      arrayOfIds = this.getAndSetFilteredArray('templateTypeIds');
      this.filterModel.templateTypeIds = [];
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.TEMPLATE_TYPE_IDS];
    } else if (clearFor === this.filterTitleList.TEMPLATE_TYPE_FORM_LIST_IDS) {
      this.filterModel.templateTypeId = null;
      this.setFilterHeaders([
        {
          text: this.filterTitleList.TEMPLATE_TYPE_FORM_LIST_IDS,
          value: this.filterModel?.templateTypeId
        }
      ]);
      this.onFilterChange(removeFilter);
    } else if (clearFor === this.filterTitleList.EQUIPMENT_LIST) {
      arrayOfIds = this.getAndSetFilteredArray('equipmentIds');
      this.filterModel.equipmentIds = [];
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.EQUIPMENT_LIST];
    } else if (clearFor === this.filterTitleList.BILLING_STATUS) {
      arrayOfIds = this.getAndSetFilteredArray('TicketBillingStatusIds');
      this.filterModel.TicketBillingStatusIds = [];
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.BILLING_STATUS];
    } else if (clearFor === this.filterTitleList.CONTROL_TYPE_IDS) {
      arrayOfIds = this.getAndSetFilteredArray('controlTypeIds');
      this.filterModel.controlTypeIds = [];
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.CONTROL_TYPE_IDS];
    } else if (clearFor === this.filterTitleList.CONTROL_DATA_TYPE_IDS) {
      arrayOfIds = this.getAndSetFilteredArray('controlDataTypeIds');
      this.filterModel.controlDataTypeIds = [];
      this.onFilterChange(removeFilter);
      list = [this.filterTitleList.CONTROL_DATA_TYPE_IDS];
    }
    for (const val of list) {
      const index = this.appliedFilter.findIndex(item => item.text === val);
      if (index > -1 && !arrayOfIds.length) {
        this.appliedFilter.splice(index, 1);
      }
    }
  }

  clearFilter(clearList = true) {
    this.resetPage();
    this.filterModel = new CommonFilter();
    this.filterModel.itemsCount = this.pageSize;
    this.filterModel.sortBy = this.filterDetails.default_sort ? this.filterDetails.default_sort : 'Open';
    this.filterModel.direction = this.filterDetails.default_direction ? this.filterDetails.default_direction : 'asc';
    this.getAllPortfolioByCustomer();
    this.getAllSiteByPortfolio();
    this.appliedFilter = [];
    if (this.filterDetails.filter_item.START_YEAR?.show) {
      if (this.filterDetails.filter_item.START_YEAR?.multi) {
        this.filterModel.years = [this.commonService.getCurrentYear()];
        this.addFilter({ text: this.filterTitleList.START_YEAR, name: this.filterModel.years }, this.filterTitleList.START_YEAR);
      } else {
        this.filterModel.year = this.commonService.getCurrentYear();
        this.addFilter({ text: this.filterTitleList.START_YEAR, name: this.filterModel.year }, this.filterTitleList.START_YEAR);
      }
    }
    if (this.filterDetails.filter_item.TEMPLATE_TYPE_FORM_LIST_IDS?.show) {
      this.filterModel.templateTypeId = null;
      this.setFilterHeaders([
        {
          text: this.filterTitleList.TEMPLATE_TYPE_FORM_LIST_IDS,
          value: this.filterModel?.templateTypeId
        }
      ]);
    }
    if (this.filterDetails.filter_item.QE_SERVICE_TYPE?.show) {
      this.setQEServiceTypeDropDown();
    }
    if (clearList) {
      if (!this.isFilterAllowedWithoutButton()) {
        this.emitFilterModel();
      } else {
        this.clearParentList.emit(true);
      }
    }
  }

  toggleFilter() {
    this.isFilterOpen = !this.isFilterOpen;
    this.refreshTableHeightInParent();
    this.storageService.set(this.filterDetails?.filter_section_name, this.isFilterOpen);
  }

  getTicketTypeList(): void {
    this.subscription.add(
      this.ticketService.getTicketTypeList().subscribe({
        next: res => {
          this.ticketTypeList = res.map(item => ({ id: item.ticketTypeId, name: item.ticketTypeName }));
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getNercSiteTypeValues() {
    this.subscription.add(
      this.siteService.getSiteTypeNERCDropDown().subscribe({
        next: res => {
          const obj = {
            name: 'All',
            id: 0
          };
          res.unshift(obj);
          this.nercSiteTypeDropDown = res;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getCostTypeList() {
    this.subscription.add(
      this.ticketService.getCostTypeList().subscribe({
        next: res => {
          this.costTypeList = res.map(item => ({ id: item.costTypeID, name: item.costTypeName }));
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  handleServiceTypeStringId(qeServiceTypes: number[]): void {
    const clonedQEServiceTypes = JSON.parse(JSON.stringify(qeServiceTypes));
    const filteredIds = clonedQEServiceTypes.filter(item => typeof item !== 'string');
    const qeServiceScaleTypes = this.filterModel.qeServiceScaleTypes;
    this.setServiceAndCategoryTypeGroup(filteredIds, qeServiceScaleTypes);
  }

  setServiceAndCategoryTypeGroup(qeServiceTypes: number[] = [], qeServiceScaleTypes: number[] = []): void {
    const parentName = qeServiceTypes.length
      ? this.qeCategoryTypeList.find(item =>
          qeServiceTypes.every(qeServiceTypeItem => item.categoryList.map(item => item.id).includes(qeServiceTypeItem))
        )?.name
      : qeServiceScaleTypes.length
      ? this.qeCategoryTypeList.find(item =>
          qeServiceScaleTypes.every(qeServiceTypeItem => item.categoryList.map(item => item.parentId).includes(qeServiceTypeItem))
        )?.name
      : '';

    const parentId = this.qeServiceTypeDropDownOption.find(item => item.name === parentName)?.id;

    if (parentId) {
      qeServiceTypes.splice(0, 0, parentId as number);
    }

    this.filterModel.qeServiceTypes = qeServiceTypes;
    this.filterModel.qeServiceScaleTypes = qeServiceScaleTypes;

    this.handleCategoryTypeListSelection(qeServiceTypes);
  }

  handleCategoryTypeListSelectionRemove($event): void {
    const ids = this.filterModel.qeServiceTypes.filter(item => typeof item === 'string');
    const numberIds = this.filterModel.qeServiceTypes.filter(item => typeof item === 'number');
    const typeOfId = typeof $event.value.id;

    if (typeOfId === 'string' || !ids.length) {
      this.filterModel.qeServiceTypes = [];
      this.filterModel.qeServiceScaleTypes = [];
    }

    // to be used
    // if (typeOfId === 'number' && !numberIds.length) {
    //   this.filterModel.qeServiceTypes = [];
    //   this.filterModel.qeServiceScaleTypes = [];
    // }

    this.handleServiceTypeStringId(this.filterModel.qeServiceTypes);
  }

  handleCategoryTypeListSelection(qeServiceTypes: number[]): void {
    const ids = qeServiceTypes.filter(item => typeof item === 'string');

    if (!ids.length) {
      qeServiceTypes = [];
      this.filterModel.qeServiceTypes = [];
      this.filterModel.qeServiceScaleTypes = [];
    }

    if (ids.length) {
      const gname = this.qeServiceTypeDropDownOption.find(item => item.id === ids[0])?.name;
      const list = this.qeCategoryTypeList.find(item => item.name === gname)?.categoryList ?? [];
      this.qeServiceTypeDropDownOption = this.qeServiceTypeDropDownOption.filter(item => typeof item.id === 'string');
      this.qeServiceTypeDropDownOption.push(...list);
    }

    this.handleServiceCategoryTypeSelection(qeServiceTypes);
  }

  handleServiceCategoryTypeSelection(qeServiceTypes: number[]): void {
    const singleSelectableGroupNames = this.qeServiceTypeDropDownOption.filter(item => item.isSingleSelectable).map(item => item.groupName);
    const singleSelectableGroupIds = this.qeServiceTypeDropDownOption.filter(item => item.isSingleSelectable).map(item => item.groupId);

    const stringIds = new Set<number>(qeServiceTypes.filter(x => typeof x === 'string'));
    const qeServiceScaleTypes = this.qeServiceTypeDropDownOption
      .filter(item => stringIds.has(item.id as number))
      .map(item => item.parentId) as number[];

    this.filterModel.qeServiceScaleTypes = qeServiceScaleTypes;

    if (qeServiceScaleTypes.length && qeServiceScaleTypes.length > 0) {
      this.filterModel.qeServiceTypes = qeServiceTypes;
    } else {
      this.filterModel.qeServiceTypes = [];
    }

    const shouldDisable = (item: QECategoryType) =>
      ((item?.parentGroupName && item.groupName !== item.parentGroupName) ||
        (item?.parentGroupId && item.groupId !== item.parentGroupId)) &&
      !qeServiceTypes.filter(item1 => typeof item1 === 'string').length;

    const resetAllOptions = () =>
      this.qeServiceTypeDropDownOption.map(item => ({
        ...item,
        disabled: shouldDisable(item)
      }));

    const groupSelectedByGroupId = (ids: number[]) => {
      const grouped = new Map<number, number[]>();
      ids.forEach(serviceId => {
        const selectedItem = this.qeServiceTypeDropDownOption.find(x => x.id === serviceId);
        if (selectedItem) {
          if (!grouped.has(selectedItem.groupId)) {
            grouped.set(selectedItem.groupId, []);
          }
          grouped.get(selectedItem.groupId).push(serviceId);
        }
      });
      return grouped;
    };

    const isSingleSelectGroup = (groupId: number, groupName: string) =>
      singleSelectableGroupNames.includes(groupName) || singleSelectableGroupIds.includes(groupId);

    const disableUnselectedInGroup = (groupId: number, selectedIds: number[]) =>
      this.qeServiceTypeDropDownOption.map(item =>
        item.groupId === groupId ? { ...item, disabled: !selectedIds.includes(item.id as number) } : item
      );

    this.qeServiceTypeDropDownOption = resetAllOptions();

    if (qeServiceTypes.length > 0) {
      const groupedSelections = groupSelectedByGroupId(qeServiceTypes);

      groupedSelections.forEach((selectedIds, groupId) => {
        const firstItem = this.qeServiceTypeDropDownOption.find(x => x.groupId === groupId);
        if (firstItem && isSingleSelectGroup(groupId, firstItem.groupName)) {
          this.qeServiceTypeDropDownOption = disableUnselectedInGroup(groupId, selectedIds);
        }
      });
    }

    this.addFilter(this.qeServiceTypeDropDownOption, this.filterTitleList.QE_SERVICE_TYPE, 'qeServiceTypes');
  }

  setQEServiceTypeDropDown(): void {
    this.qeServiceTypeDropDownOption = JSON.parse(JSON.stringify(this.qeCategoryTypeList)).map(
      (item: QEServiceCategoryTypeList) => (delete item.categoryList, item)
    );
    this.qeServiceTypeDropDownOption.push(...this.qeCategoryTypeList[0].categoryList);
    this.handleServiceTypeStringId(this.filterModel?.qeServiceTypes ?? []);
  }

  setQEUserList(): void {
    if (
      this.filterDetails.filter_item.USER?.show &&
      this.filterDetails.page_name === this.filterPageNameEnum.ADMIN_QE_ANALYTICS &&
      this.filterDetails.filterSectionEnum === this.filterSectionEnum.ADMIN_QE_ANALYTICS
    ) {
      if (
        this.filterDetails.filter_item.USER_TYPE?.show &&
        this.filterDetails.filter_item.USER_TYPE?.multi &&
        this.filterModel.userTypeIds.length
      ) {
        const qeUserList = this.fieldTechList.filter(item => this.filterModel.userTypeIds.includes(item.userRoleId));
        this.qeUserList = JSON.parse(JSON.stringify(qeUserList));
      } else if (
        this.filterDetails.filter_item.USER_TYPE?.show &&
        !this.filterDetails.filter_item.USER_TYPE?.multi &&
        this.filterModel.userTypeId
      ) {
        const qeUserList = this.fieldTechList.filter(item => this.filterModel.userTypeId === item.userRoleId);
        this.qeUserList = JSON.parse(JSON.stringify(qeUserList));
      } else {
        this.qeUserList = JSON.parse(JSON.stringify(this.fieldTechList));
      }
    } else {
      this.qeUserList = JSON.parse(JSON.stringify(this.fieldTechList));
    }
  }

  onDateDurationSelect(): void {
    let today = new Date();
    if (
      this.filterModel.dateDuration === this.qeDateDurationKeyEnum.CURRENT_DAY ||
      this.filterModel.dateDuration === this.qeDateDurationKeyEnum.SPECIFIC_DATE
    ) {
      this.filterModel.durationStartDate = today;
      this.filterModel.durationEndDate = today;
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.PREVIOUS_DAY) {
      this.filterModel.durationStartDate = new Date(today.setDate(today.getDate() - 1));
      this.filterModel.durationEndDate = new Date(today);
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.LAST_3_DAYS) {
      this.filterModel.durationStartDate = new Date(today.setDate(today.getDate() - 3));
      this.filterModel.durationEndDate = new Date(today.setDate(today.getDate() + 2));
    } else if (
      this.filterModel.dateDuration === this.qeDateDurationKeyEnum.LAST_7_DAYS ||
      this.filterModel.dateDuration === this.qeDateDurationKeyEnum.WEEK
    ) {
      this.filterModel.durationStartDate = new Date(today.setDate(today.getDate() - 7));
      this.filterModel.durationEndDate = new Date(today.setDate(today.getDate() + 6));
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.CURRENT_WEEK) {
      const current = new Date();
      const day = current.getDay();
      const diffToMonday = day === 0 ? -6 : 1 - day;
      const monday = new Date(current);
      monday.setDate(current.getDate() + diffToMonday);
      monday.setHours(0, 0, 0, 0);
      const sunday = new Date(monday);
      sunday.setDate(monday.getDate() + 6);
      sunday.setHours(23, 59, 59, 999);
      this.filterModel.durationStartDate = monday;
      this.filterModel.durationEndDate = sunday;
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.PREVIOUS_WEEK) {
      const current = new Date();
      const day = current.getDay();
      const diffToMonday = day === 0 ? -6 : 1 - day;
      const monday = new Date(current);
      monday.setDate(current.getDate() + diffToMonday - 7);
      monday.setHours(0, 0, 0, 0);
      const sunday = new Date(monday);
      sunday.setDate(monday.getDate() + 6);
      sunday.setHours(23, 59, 59, 999);
      this.filterModel.durationStartDate = monday;
      this.filterModel.durationEndDate = sunday;
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.LAST_15_DAYS) {
      this.filterModel.durationStartDate = new Date(today.setDate(today.getDate() - 15));
      this.filterModel.durationEndDate = new Date(today.setDate(today.getDate() + 14));
    } else if (
      this.filterModel.dateDuration === this.qeDateDurationKeyEnum.LAST_30_DAYS ||
      this.filterModel.dateDuration === this.qeDateDurationKeyEnum.MONTH
    ) {
      this.filterModel.durationStartDate = new Date(today.setDate(today.getDate() - 30));
      this.filterModel.durationEndDate = new Date(today.setDate(today.getDate() + 29));
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.CURRENT_MONTH) {
      const current = new Date();
      const firstDay = new Date(current.getFullYear(), current.getMonth(), 1);
      const lastDay = new Date(current.getFullYear(), current.getMonth() + 1, 0);
      firstDay.setHours(0, 0, 0, 0);
      lastDay.setHours(23, 59, 59, 999);
      this.filterModel.durationStartDate = firstDay;
      this.filterModel.durationEndDate = lastDay;
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.PREVIOUS_MONTH) {
      const current = new Date();
      const firstDayPrev = new Date(current.getFullYear(), current.getMonth() - 1, 1);
      const lastDayPrev = new Date(current.getFullYear(), current.getMonth(), 0);
      firstDayPrev.setHours(0, 0, 0, 0);
      lastDayPrev.setHours(23, 59, 59, 999);
      this.filterModel.durationStartDate = firstDayPrev;
      this.filterModel.durationEndDate = lastDayPrev;
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.CURRENT_QUARTER) {
      const current = new Date();
      const quarter = Math.floor(current.getMonth() / 3);
      const firstDay = new Date(current.getFullYear(), quarter * 3, 1);
      const lastDay = new Date(current.getFullYear(), quarter * 3 + 3, 0);
      firstDay.setHours(0, 0, 0, 0);
      lastDay.setHours(23, 59, 59, 999);
      this.filterModel.durationStartDate = firstDay;
      this.filterModel.durationEndDate = lastDay;
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.PREVIOUS_QUARTER) {
      const current = new Date();
      let quarter = Math.floor(current.getMonth() / 3) - 1;
      let year = current.getFullYear();
      if (quarter < 0) {
        quarter = 3;
        year -= 1;
      }
      const firstDay = new Date(year, quarter * 3, 1);
      const lastDay = new Date(year, quarter * 3 + 3, 0);
      firstDay.setHours(0, 0, 0, 0);
      lastDay.setHours(23, 59, 59, 999);
      this.filterModel.durationStartDate = firstDay;
      this.filterModel.durationEndDate = lastDay;
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.CURRENT_HALF_YEAR) {
      const current = new Date();
      const month = current.getMonth();
      const year = current.getFullYear();
      let firstDay, lastDay;
      if (month < 6) {
        firstDay = new Date(year, 0, 1);
        lastDay = new Date(year, 6, 0);
      } else {
        firstDay = new Date(year, 6, 1);
        lastDay = new Date(year, 12, 0);
      }
      firstDay.setHours(0, 0, 0, 0);
      lastDay.setHours(23, 59, 59, 999);
      this.filterModel.durationStartDate = firstDay;
      this.filterModel.durationEndDate = lastDay;
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.PREVIOUS_HALF_YEAR) {
      const current = new Date();
      const month = current.getMonth();
      let year = current.getFullYear();
      let firstDay, lastDay;
      if (month < 6) {
        year -= 1;
        firstDay = new Date(year, 6, 1);
        lastDay = new Date(year, 12, 0);
      } else {
        firstDay = new Date(year, 0, 1);
        lastDay = new Date(year, 6, 0);
      }
      firstDay.setHours(0, 0, 0, 0);
      lastDay.setHours(23, 59, 59, 999);
      this.filterModel.durationStartDate = firstDay;
      this.filterModel.durationEndDate = lastDay;
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.CURRENT_YEAR) {
      const current = new Date();
      const firstDay = new Date(current.getFullYear(), 0, 1);
      const lastDay = new Date(current.getFullYear(), 12, 0);
      firstDay.setHours(0, 0, 0, 0);
      lastDay.setHours(23, 59, 59, 999);
      this.filterModel.durationStartDate = firstDay;
      this.filterModel.durationEndDate = lastDay;
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.PREVIOUS_YEAR) {
      const current = new Date();
      const prevYear = current.getFullYear() - 1;
      const firstDay = new Date(prevYear, 0, 1);
      const lastDay = new Date(prevYear, 12, 0);
      firstDay.setHours(0, 0, 0, 0);
      lastDay.setHours(23, 59, 59, 999);
      this.filterModel.durationStartDate = firstDay;
      this.filterModel.durationEndDate = lastDay;
    } else if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.DATE_RANGE) {
      this.filterModel.durationStartDate = new Date(today.setDate(today.getDate() - 2));
      this.filterModel.durationEndDate = new Date(today.setDate(today.getDate() + 2));
      this.onDateDurationDateChanged(this.filterModel.durationStartDate);
    }
  }

  onDateDurationDateChanged(date: Date): void {
    const newDate = new Date(date);
    newDate.setHours(0, 0, 0, 0);
    if (this.filterModel.dateDuration === this.qeDateDurationKeyEnum.SPECIFIC_DATE) {
      this.filterModel.durationEndDate = new Date(date);
    }
    const durationEndDate = new Date(this.filterModel.durationEndDate);
    durationEndDate.setHours(0, 0, 0, 0);
    const tempDate = new Date(newDate);
    const durationMaxDate = new Date();
    // to be used - if 14 days
    // const durationMaxDate = new Date(tempDate.setDate(tempDate.getDate() + 14));
    if (newDate > durationEndDate) {
      this.filterModel.durationEndDate = newDate;
    }
    if (durationMaxDate > this.dateDurationConfig.durationMaxDate) {
      this.dateDurationConfig.durationMaxEndDate = this.dateDurationConfig.durationMaxDate;
    } else {
      this.dateDurationConfig.durationMaxEndDate = durationMaxDate;
      this.filterModel.durationEndDate = durationMaxDate;
    }
    if (date) {
      this.dateDurationConfig.durationMinDate = new Date(date);
    }
  }

  getUserTypeList(): void {
    this.subscription.add(
      this.userService.getRoles().subscribe({
        next: (res: any[]) => {
          this.userTypeList = res.map(item => ({ id: item.id, name: item.displayName, displayName: item.name }));
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getQEMenuModuleList(): void {
    const qeMenuModuleTypeList = this.storageService.get(AppConstants.qeMenuSubmenuItemListKey);
    if (qeMenuModuleTypeList && qeMenuModuleTypeList.length) {
      this.qeMenuModuleTypeList = qeMenuModuleTypeList.map(item => ({ ...item, id: item.menuId, name: item.menuName }));
      this.loading = false;
    } else {
      this.subscription.add(
        this.qeAnalyticsService.getQEMenuModuleList().subscribe({
          next: (res: QEAnalyticsModuleAndEnumList) => {
            this.qeMenuModuleTypeList = res.qeMenuModuleList.map(item => ({ ...item, id: item.menuId, name: item.menuName }));
            this.loading = false;
          },
          error: e => {
            this.loading = false;
          }
        })
      );
    }
  }

  getReportType() {
    this.subscription.add(
      this.reportservice.getReportTypeAll().subscribe({
        next: res => {
          const data = [];
          res.forEach((element: ReportType) => {
            if ((this.addReportTypeJHA || element.abbreviation.toLowerCase() !== 'jha') && element.abbreviation.toLowerCase() !== 'mnt') {
              data.push({ id: element.id, name: element.name });
            }
          });
          if (this.filterDetails.page_name === 'dashboardPage') {
            this.reportTypeData = data.filter(item => item.id !== 9);
          } else {
            this.reportTypeData = data;
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getTemplateTypeDropdown(type: string) {
    this.loading = true;
    this.customFormService.getTemplateTypeDropdown().subscribe({
      next: (data: any) => {
        this.templateTypeList = data;
        if (type === 'TEMPLATE_TYPE_IDS' && this.filterModel.templateTypeIds && this.filterModel.templateTypeIds.length) {
          this.setFilterHeaders([
            {
              text: this.filterTitleList.TEMPLATE_TYPE_IDS,
              value: this.filterModel?.templateTypeIds
            }
          ]);
        }
        if (type === 'TEMPLATE_TYPE_FORM_LIST_IDS' && this.filterModel.templateTypeId) {
          this.setFilterHeaders([
            {
              text: this.filterTitleList.TEMPLATE_TYPE_FORM_LIST_IDS,
              value: this.filterModel?.templateTypeId
            }
          ]);
        }
      },
      error: () => (this.loading = false)
    });
  }

  getEquipmentList() {
    this.customFormService.getEquipmentDropdownList([1]).subscribe({
      next: (data: any) => {
        this.equipmentList = data;
        if (this.filterModel.equipmentIds && this.filterModel.equipmentIds.length) {
          this.setFilterHeaders([
            {
              text: this.filterTitleList.EQUIPMENT_LIST,
              value: this.filterModel?.equipmentIds
            }
          ]);
        }
      },
      error: () => (this.loading = false)
    });
  }

  deviceSearchChanged() {
    this.resetPage();
    this.deviceNameModelChanged.next(null);
  }

  onCustomerSelectDeSelect(isRefresh = false) {
    this.resetPage();
    [this.filterTitleList.PORTFOLIO, this.filterTitleList.SITE].forEach(element => {
      const index = this.appliedFilter.findIndex(item => item.text === element);
      if (index > -1) {
        this.appliedFilter.splice(index, 1);
      }
    });
    this.portfolioList = [];
    this.siteList = [];
    this.filterModel.portfolioIds = [];
    this.filterModel.siteIds = [];
    this.getAllPortfolioByCustomer();
    this.getAllSiteByPortfolio();
    if (isRefresh) {
      this.refreshListInParent();
    }
  }

  onPortfolioSelectDeSelect(isRefresh = false) {
    this.resetPage();
    this.siteList = [];
    this.filterModel.siteIds = [];
    const index = this.appliedFilter.findIndex(item => item.text === this.filterTitleList.SITE);
    if (index > -1) {
      this.appliedFilter.splice(index, 1);
    }
    this.getAllSiteByPortfolio();
    if (isRefresh) {
      this.refreshListInParent();
    }
  }

  onFilterChange(isRefresh = false) {
    this.resetPage();
    if (isRefresh) {
      this.refreshListInParent();
    }
  }

  onFilterChangeAndRemove(event, filterFor): void {
    switch (this.filterDetails.filterSectionEnum) {
      case this.filterSectionEnum.OPERATIONS_CUSTOM_FORMS_FORM_LISTING:
        if (filterFor === this.filterTitleList.TEMPLATE_TYPE_FORM_LIST_IDS) {
          const { QEST_INVERTER_PM, QEST_SUMMARY_REPORT, QEST_COVER_PAGE } = this.qestFormTemplateTypes;
          if ([QEST_SUMMARY_REPORT, QEST_COVER_PAGE].includes(this.filterModel.templateTypeId)) {
            this.clearSingleFilter(this.filterTitleList.CUSTOMER, false);
          }
          if (![QEST_INVERTER_PM].includes(this.filterModel.templateTypeId)) {
            this.clearSingleFilter(this.filterTitleList.EQUIPMENT_LIST, false);
          }
        }
        break;
      default:
        break;
    }
  }

  getAllPortfolioByCustomer() {
    this.loading = true;
    this.allReportDropdown.ids = this.filterModel.customerIds;
    this.subscription.add(
      this.portfolioService.getAllReportPortfoliosByCustomerId(this.allReportDropdown).subscribe({
        next: (res: Dropdown[]) => {
          this.portfolioList = res;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  getAllSiteByPortfolio() {
    this.loading = true;
    this.allReportDropdown.customerIds = this.filterModel.customerIds;
    this.allReportDropdown.ids = this.filterModel.portfolioIds;
    this.subscription.add(
      this.siteService.getAllReportSitesByPortfolioId(this.allReportDropdown).subscribe({
        next: (res: Dropdown[]) => {
          this.siteList = res;
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  addFilter(event, filterFor: string, forValue = '', key = 'id') {
    if (forValue) {
      event = event.filter(val => this.filterModel[forValue]?.includes(val[key]));
    }

    if (event) {
      let name = '';
      if (
        filterFor === this.filterTitleList.OPEN_DATE ||
        filterFor === this.filterTitleList.ACTIVITY_RANGE ||
        filterFor === this.filterTitleList.DATE ||
        filterFor === this.filterTitleList.CLOSE_DATE ||
        filterFor === this.filterTitleList.EXCLUSION_TO ||
        filterFor === this.filterTitleList.EXCLUSION_FROM ||
        filterFor === this.filterTitleList.QE_DATE_DURATION
      ) {
        if (event?.name?.start && event?.name?.end) {
          name = `${this.datepipe.transform(event.name.start, AppConstants.fullDateFormat)} - 
          ${this.datepipe.transform(event.name.end, AppConstants.fullDateFormat)}`;
        } else if (filterFor === this.filterTitleList.QE_DATE_DURATION) {
          name = `${this.datepipe.transform(this.filterModel.durationStartDate, AppConstants.fullDateFormat)}`;
          name += this.filterModel.durationEndDate
            ? ` - ${this.datepipe.transform(this.filterModel.durationEndDate, AppConstants.fullDateFormat)}`
            : '';
        }
      } else if (filterFor === this.filterTitleList.ACTIVITY_START || filterFor === this.filterTitleList.ACTIVITY_END) {
        name = this.datepipe.transform(event.name, AppConstants.fullDateFormat);
      } else if (filterFor === this.filterTitleList.START_DATE || filterFor === this.filterTitleList.END_DATE) {
        name = this.datepipe.transform(event.name, AppConstants.fullDateFormat);
      } else if (filterFor === this.filterTitleList.SHOW_DELETED) {
        name = event.name ? 'Yes' : 'No';
      } else if (filterFor === this.filterTitleList.SHOW_ARCHIVED) {
        name = event.name ? 'Yes' : 'No';
      } else if (filterFor === this.filterTitleList.IS_INCLUSIVE_SEARCH) {
        name = event.name ? 'Yes' : 'No';
      } else if (filterFor === this.filterTitleList.IS_EXCLUSIVE_SEARCH) {
        name = event.name ? 'Yes' : 'No';
      } else if (filterFor === this.filterTitleList.IS_RESCHEDULED) {
        name = event.name ? 'Yes' : 'No';
      } else if (filterFor === this.filterTitleList.IS_UNSCHEDULED) {
        name = event.name ? 'Yes' : 'No';
      } else if (filterFor === this.filterTitleList.IS_TENTATIVE_MONTH) {
        name = event.name ? 'Yes' : 'No';
      } else if (filterFor === this.filterTitleList.IS_ACTIVE_NOTIFICATION) {
        name = event.name ? 'Yes' : 'No';
      } else if (filterFor === this.filterTitleList.IS_INACTIVE_MISSING_CONFIG_NOTIFICATION) {
        name = event.name ? 'Yes' : 'No';
      } else if (filterFor === this.filterTitleList.TEMPLATE_TYPE_IDS) {
        if (event.length > 0) {
          if (event.length > 1) {
            name = `${event[0].templateTypeName} +${event.length - 1}`;
          } else {
            name = event[0].templateTypeName;
          }
        } else {
          name = event.templateTypeName;
        }
      } else if (filterFor === this.filterTitleList.EQUIPMENT_LIST) {
        if (event.length > 0) {
          if (event.length > 1) {
            name = `${event[0].equipmentModel} +${event.length - 1}`;
          } else {
            name = event[0].equipmentModel;
          }
        } else {
          name = event.equipmentModel;
        }
      } else if (filterFor === this.filterTitleList.BILLING_STATUS) {
        if (event.length > 0) {
          if (event.length > 1) {
            name = `${event[0].description} +${event.length - 1}`;
          } else {
            name = event[0].description;
          }
        } else {
          name = event.description;
        }
      } else if (event && event.length > 0 && filterFor === this.filterTitleList.QE_SERVICE_TYPE) {
        if (event.length > 0) {
          const hasGroup = 'groupName' in event[0] && event[0].groupName;

          if (hasGroup) {
            const limit = 4;
            const sliced = event.slice(0, limit);
            const showCount = event.length > limit ? ` +${event.length - limit}` : '';

            const stringItems = (event.length > limit ? sliced : event).filter(item => typeof item.id === 'string');
            const numberItems = (event.length > limit ? sliced : event)
              .filter(item => typeof item.id === 'number')
              .sort((a, b) => a.groupId - b.groupId);

            name =
              `${stringItems.map(i => i.name).join(', ')}` +
              `${numberItems.length ? ' - ' : ''} ${numberItems.map(i => i.name).join(', ')}${showCount}`;
          }
        }
      } else if (event && event.length > 0 && filterFor === this.filterTitleList.USER_TYPE) {
        if (event.length > 0) {
          if (event.length > 1) {
            name = `${event[0].name} +${event.length - 1}`;
          } else {
            name = event[0].name;
          }
        } else {
          name = event.name;
        }
      } else if (event && event.length > 0 && filterFor === this.filterTitleList.QE_MODULES) {
        if (event.length > 0) {
          if ('parentName' in event[0]) {
            if (event.length > 1) {
              name = `${event[0].parentName} - ${event[0].name} +${event.length - 1}`;
            } else {
              name = `${event[0].parentName} - ${event[0].name}`;
            }
          }
        }
      } else if (event && event.length > 0 && filterFor === this.filterTitleList.USER_NAME) {
        if (event.length > 0) {
          if (event.length > 1) {
            name = `${event[0].name} +${event.length - 1}`;
          } else {
            name = event[0].name;
          }
        } else {
          name = event.name;
        }
      } else if (event && event.length > 0 && filterFor === this.filterTitleList.USER_EMAIL) {
        if (event.length > 0) {
          if (event.length > 1) {
            name = `${event[0].name} +${event.length - 1}`;
          } else {
            name = event[0].name;
          }
        } else {
          name = event.name;
        }
      } else if (event && event.length > 0 && filterFor === this.filterTitleList.USER_COMPANY) {
        if (event.length > 0) {
          if (event.length > 1) {
            name = `${event[0].name} +${event.length - 1}`;
          } else {
            name = event[0].name;
          }
        } else {
          name = event.name;
        }
      } else if (event && filterFor === this.filterTitleList.USER_STATUS) {
        const status = this.userStatusList.find(item => item.id === event.name);
        name = status?.name || '';
      } else if (
        event.length > 0 &&
        filterFor !== this.filterTitleList.TEMPLATE_TYPE_IDS &&
        filterFor !== this.filterTitleList.EQUIPMENT_LIST
      ) {
        if (event.length > 1) {
          name = `${event[0].name} +${event.length - 1}`;
        } else {
          name = event[0].name;
        }
      } else if (event && filterFor === this.filterTitleList.TEMPLATE_TYPE_FORM_LIST_IDS) {
        if ('templateTypeName' in event) {
          name = event.templateTypeName;
        }
      } else {
        name = event.name;
      }
      if (name) {
        if (this.appliedFilter.length) {
          const index = this.appliedFilter.findIndex(item => item.text === filterFor);
          if (index > -1) {
            this.appliedFilter[index].value = name;
          } else {
            this.appliedFilter.push({ text: filterFor, value: name });
          }
        } else {
          this.appliedFilter.push({ text: filterFor, value: name });
        }
      } else {
        const index = this.appliedFilter.findIndex(item => item.text === filterFor);
        if (index > -1) {
          this.appliedFilter.splice(index, 1);
        }
      }
    }
  }

  archiveUnarchiveReport(event) {
    if (event.target !== undefined) {
      this.filterModel.isArchive = event.target.checked;
    } else {
      this.filterModel.isArchive = event;
    }
    if (this.filterModel.isArchive) {
      this.refreshListInParent();
      this.addFilter({ text: this.filterTitleList.SHOW_ARCHIVED, name: this.filterModel.isArchive }, this.filterTitleList.SHOW_ARCHIVED);
    } else {
      this.clearSingleFilter(this.filterTitleList.SHOW_ARCHIVED);
    }
  }

  restoreDeletedReport(event) {
    if (event.target !== undefined) {
      this.filterModel.isDelete = event.target.checked;
    } else {
      this.filterModel.isDelete = event;
    }
    if (this.filterModel.isDelete) {
      this.refreshListInParent();
      this.addFilter({ text: this.filterTitleList.SHOW_DELETED, name: this.filterModel.isDelete }, this.filterTitleList.SHOW_DELETED);
    } else {
      this.clearSingleFilter(this.filterTitleList.SHOW_DELETED);
    }
  }

  checkInOnly(event) {
    if (event.target !== undefined) {
      this.filterModel.checkInOnly = event.target.checked;
    } else {
      this.filterModel.checkInOnly = event;
    }
    if (this.filterModel.checkInOnly) {
      this.refreshListInParent();
      this.addFilter({ text: this.filterTitleList.CHECK_IN_ONLY, name: this.filterModel.checkInOnly }, this.filterTitleList.CHECK_IN_ONLY);
    } else {
      this.clearSingleFilter(this.filterTitleList.CHECK_IN_ONLY);
    }
  }

  isUnscheduled(event) {
    if (event.target !== undefined) {
      this.filterModel.isUnScheduled = event.target.checked;
    } else {
      this.filterModel.isUnScheduled = event;
    }
    this.refreshListInParent();
    if (this.filterModel.isUnScheduled) {
      this.addFilter(
        { text: this.filterTitleList.IS_UNSCHEDULED, name: this.filterModel.isUnScheduled },
        this.filterTitleList.IS_UNSCHEDULED
      );
    } else {
      this.clearSingleFilter(this.filterTitleList.IS_UNSCHEDULED);
    }
  }

  isRescheduled(event) {
    if (event.target !== undefined) {
      this.filterModel.IsRescheduled = event.target.checked;
    } else {
      this.filterModel.IsRescheduled = event;
    }
    this.refreshListInParent();
    if (this.filterModel.IsRescheduled) {
      this.addFilter(
        { text: this.filterTitleList.IS_RESCHEDULED, name: this.filterModel.IsRescheduled },
        this.filterTitleList.IS_RESCHEDULED
      );
    } else {
      this.clearSingleFilter(this.filterTitleList.IS_RESCHEDULED);
    }
  }

  isTentativeMonth(event) {
    if (event.target !== undefined) {
      this.filterModel.IsTentavieMonth = event.target.checked;
    } else {
      this.filterModel.IsTentavieMonth = event;
    }
    this.refreshListInParent();
    if (this.filterModel.IsTentavieMonth) {
      this.addFilter(
        { text: this.filterTitleList.IS_TENTATIVE_MONTH, name: this.filterModel.IsTentavieMonth },
        this.filterTitleList.IS_TENTATIVE_MONTH
      );
    } else {
      this.clearSingleFilter(this.filterTitleList.IS_TENTATIVE_MONTH);
    }
  }

  isActiveNotification(event): void {
    if (event.target !== undefined) {
      this.filterModel.isActive = event.target.checked;
    } else {
      this.filterModel.isActive = event;
    }
    this.refreshListInParent();
    if (this.filterModel.isActive) {
      this.addFilter(
        { text: this.filterTitleList.IS_ACTIVE_NOTIFICATION, name: this.filterModel.isActive },
        this.filterTitleList.IS_ACTIVE_NOTIFICATION
      );
    } else {
      this.clearSingleFilter(this.filterTitleList.IS_ACTIVE_NOTIFICATION);
    }
  }

  isInactiveOrMissingConfigNotification(event): void {
    if (event.target !== undefined) {
      this.filterModel.missingConfigOrInactive = event.target.checked;
    } else {
      this.filterModel.missingConfigOrInactive = event;
      if (event) {
        this.isActiveNotification(false);
      }
    }
    this.refreshListInParent();
    if (this.filterModel.missingConfigOrInactive) {
      this.addFilter(
        { text: this.filterTitleList.IS_INACTIVE_MISSING_CONFIG_NOTIFICATION, name: this.filterModel.missingConfigOrInactive },
        this.filterTitleList.IS_INACTIVE_MISSING_CONFIG_NOTIFICATION
      );
    } else {
      this.clearSingleFilter(this.filterTitleList.IS_INACTIVE_MISSING_CONFIG_NOTIFICATION);
    }
  }

  linkedReport(event) {
    if (event.target !== undefined) {
      this.filterModel.isLink = event.target.checked;
    } else {
      this.filterModel.isLink = event.target.checked;
    }
    if (this.filterModel.isLink) {
      this.refreshListInParent();
      this.addFilter({ text: this.filterTitleList.IS_LINK, name: this.filterModel.isLink }, this.filterTitleList.IS_LINK);
    } else {
      this.clearSingleFilter(this.filterTitleList.IS_LINK);
    }
  }

  addRemoveIsExclusiveFilter() {
    if (this.filterModel?.activityRange) {
      this.addFilter(
        { text: this.filterTitleList.IS_EXCLUSIVE_SEARCH, name: this.filterModel.isExclusiveSearch },
        this.filterTitleList.IS_EXCLUSIVE_SEARCH
      );
    } else {
      this.clearSingleFilter(this.filterTitleList.IS_EXCLUSIVE_SEARCH);
    }
  }

  addRemoveIsInclusiveFilter() {
    if (this.filterDetails.page_name === 'dashboardPage') {
      if (this.filterModel?.reportTypeIds?.length > 0) {
        this.addFilter(
          { text: this.filterTitleList.IS_INCLUSIVE_SEARCH, name: this.filterModel.isInclusiveSearch },
          this.filterTitleList.IS_INCLUSIVE_SEARCH
        );
      } else {
        this.clearSingleFilter(this.filterTitleList.IS_INCLUSIVE_SEARCH);
      }
    }
  }

  isExclusiveSearch() {
    if (this.filterModel?.reportTypeIds?.length > 0) {
      this.refreshListInParent();
      this.addFilter(
        { text: this.filterTitleList.IS_INCLUSIVE_SEARCH, name: this.filterModel.isInclusiveSearch },
        this.filterTitleList.IS_INCLUSIVE_SEARCH
      );
    } else {
      this.clearSingleFilter(this.filterTitleList.IS_INCLUSIVE_SEARCH);
    }
  }

  isExclusiveSearchCheck() {
    if (this.filterModel.activityRange && this.filterTitleList.IS_EXCLUSIVE_SEARCH) {
      this.refreshListInParent();
      this.addRemoveIsExclusiveFilter();
    } else {
      this.clearSingleFilter(this.filterTitleList.IS_EXCLUSIVE_SEARCH);
    }
  }

  selectAndDeselectAll(array: Dropdown[], forValue: string, isSelect = false, addValue = 'id') {
    if (this.filterModel[forValue] === null) this.filterModel[forValue] = [];

    if (isSelect) {
      if (this.filterModelCopy[forValue].length) {
        this.filterModel[forValue] = [
          ...new Set([...this.filterModel[forValue], ...JSON.parse(JSON.stringify(this.filterModelCopy[forValue]))])
        ];
      } else {
        this.filterModel[forValue] = array.map(portfolio => portfolio[addValue]);
      }
    }

    if (forValue === 'customerIds') {
      this.onCustomerSelectDeSelect(true);
    } else if (forValue === 'portfolioIds') {
      this.onPortfolioSelectDeSelect(true);
    } else {
      this.onFilterChange(true);
    }
  }

  fromToDateChanged(event) {
    if ((event && event.start && event.end) || event === null) {
      this.resetPage();
      this.refreshListInParent();
    }
  }

  get canApplyFilterOnEnter(): boolean {
    return this.isFilterAllowedWithoutButton() && this.isFilterOpen;
  }

  onEnterPressed(event: SflAutoSearchWithKeyboardResponse, filterModelKey: string): void {
    this.filterModel[filterModelKey] = event.value;
    const optionalFields = [];
    const requiredFields = [];
    const isValidFilter = this.filterHelperService.checkRequiredFilteredFields(
      this.filterModel,
      requiredFields,
      optionalFields,
      event.isValidFilter
    );
    if (this.isFilterAllowedWithoutButton() && this.isFilterOpen && event.isValidFilter && isValidFilter) {
      this.applyFilterClick();
    }
  }

  onSingleDateChanged(event) {
    if (event || event === null) {
      this.resetPage();
      this.refreshListInParent();
    }
  }

  dateChanged(event) {
    this.resetPage();
    this.refreshListInParent();
  }

  modifyFilterModel(): void {
    if (this.filterDetails?.filter_item?.QE_SERVICE_TYPE?.show) {
      const qeServiceScaleTypes = JSON.parse(JSON.stringify(this.filterModel?.qeServiceScaleTypes ?? []));

      if (!(qeServiceScaleTypes.length && qeServiceScaleTypes.length > 0)) {
        this.filterModel.qeServiceTypes = [];
        this.filterModel.qeServiceScaleTypes = [];
      }
    }
  }

  refreshListInParent(): void {
    if (!this.isFilterAllowedWithoutButton() || !this.filterHeaderShow) {
      this.emitFilterModel();
    }
  }

  applyFilterClick(): void {
    if (this.isFilterAllowedWithoutButton()) {
      this.emitFilterModel();
    }
  }

  isFilterAllowedWithoutButton(): boolean {
    const sectionValue = this.filterDetails.filterSectionEnum ?? this.filterSectionEnum.NONE;
    return !NOT_ALLOWED_SECTIONS_FOR_AUTO_FILTER.includes(sectionValue);
  }

  emitFilterModel(): void {
    this.filterModel.page = 0;
    if (this.isFilterAllowedWithoutButton() && !this.isSiteAuditPages()) {
      this.storageService.updateSharedFilters(this.filterModel);
    }
    this.modifyFilterModel();
    this.refreshList.emit(this.filterModel);
  }

  refreshTableHeightInParent(): void {
    this.refreshTableHeight.emit(this.isFilterOpen);
  }

  resetPage() {
    this.filterModel.page = 0;
    this.currentPage = 0;
  }

  affectedkwChanged() {
    this.resetPage();
    this.affectedKWModelChanged.next(null);
  }

  onSearchChanged() {
    this.resetPage();
    this.searchModelChanged.next(null);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes && changes.billingPageViewType && changes.billingPageViewType.currentValue) {
      if (this.filterDetails.page_name === 'ticketsBillingListingPage') {
        if (changes.billingPageViewType.currentValue === 'billingView') {
          let list = [this.filterTitleList.START_DATE, this.filterTitleList.END_DATE, this.filterTitleList.TICKET_ESTIMATION_STATUS];
          for (const val of list) {
            const index = this.appliedFilter.findIndex(item => item.text === val);
            if (index > -1) {
              this.appliedFilter.splice(index, 1);
            }
          }
          if (this.filterModel.openDate) {
            this.addFilter({ name: this.filterModel.openDate }, 'Closed');
          }
          if (this.filterModel.closeDate) {
            this.addFilter({ name: this.filterModel.closeDate }, 'Opened');
          }
        } else {
          if (!this.filterModel.ticketEstimateStatusIds.length) {
            this.filterModel.ticketEstimateStatusIds = [5];
          }
          let list = [this.filterTitleList.OPEN_DATE, this.filterTitleList.CLOSE_DATE];
          for (const val of list) {
            const index = this.appliedFilter.findIndex(item => item.text === val);
            if (index > -1) {
              this.appliedFilter.splice(index, 1);
            }
          }
          if (this.filterModel.startDate) {
            this.addFilter({ name: this.filterModel.startDate }, 'Start Date');
          }
          if (this.filterModel.endDate) {
            this.addFilter({ name: this.filterModel.endDate }, 'End Date');
          }
          if (this.filterModel.ticketEstimateStatusIds.length) {
            this.addFilter(this.ticketEstimateStatus, this.filterTitleList.TICKET_ESTIMATION_STATUS, 'ticketEstimateStatusIds');
          }
        }
      }
    }
    this.filterModel.searchValue = this.searchValue;
    this.filterModel.searchBy = this.searchBy;
  }

  getAllDataSource() {
    this.loading = true;
    this.subscription.add(
      this.siteService.getAllDataSourceList(this.isApiErrorFilter).subscribe({
        next: (res: Dropdown[]) => {
          this.dataSourceList = res;
          this.loading = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  onDataSourceChanged(isRefresh = false) {
    this.filterModel.automationSiteIds = [];
    this.getAllAutomationSitesByDataSource();
    this.onFilterChange(isRefresh);
  }

  getAllAutomationSitesByDataSource() {
    this.automationSitesList = [];
    this.loading = true;
    this.subscription.add(
      this.siteService.getAllAutomationSitesByDataSource(this.filterModel.automationPartnerIds).subscribe({
        next: (res: Dropdown[]) => {
          this.automationSitesList = res;
          this.loading = false;
        },
        error: _e => {
          this.loading = false;
        }
      })
    );
  }

  onFilterSearch(event, filterListType: string, key = 'id') {
    if (event.term) {
      this.filterModelCopy[filterListType] = event.items?.map(element => element[key]);
    } else {
      this.filterModelCopy[filterListType] = [];
    }
  }

  getFilteredTruckRolls(ids: number[]) {
    const filteredTruckRolls = this.truckRollTypeList.filter(item => ids.includes(item.id));
    return filteredTruckRolls;
  }

  truckRollChange(selectedTruckRolls: { id: number; name: string; disabled: boolean }[]) {
    if (!selectedTruckRolls.length) {
      this.truckRollTypeList2.forEach(x => {
        x.disabled = false;
      });
      this.truckRollTypeList = [...this.truckRollTypeList2];
      return;
    }
    if (selectedTruckRolls[0].id === 1 || selectedTruckRolls[0].id === 2) {
      this.truckRollTypeList2.find(x => x.id === 0).disabled = true;
      this.truckRollTypeList = [...this.truckRollTypeList2];
    } else if (selectedTruckRolls[0].id === 0) {
      this.truckRollTypeList2.forEach(x => {
        if (x.id !== 0) {
          x.disabled = true;
        }
      });
      this.truckRollTypeList = [...this.truckRollTypeList2];
    }
  }

  shouldShowSelectAll(filterType: 'CUSTOMER' | 'PORTFOLIO' | 'SITE'): boolean {
    const sectionValue = this.filterDetails?.filterSectionEnum ?? this.filterSectionEnum.NONE;

    // Always show Select All for NOT_ALLOWED sections
    if (SHOULD_ALLOW_SELECT_ALL.includes(sectionValue)) {
      return true;
    }

    const customerSelected = (this.filterModel.customerIds?.length || 0) > 0;
    const portfolioSelected = (this.filterModel.portfolioIds?.length || 0) > 0;
    const siteSelected = (this.filterModel.siteIds?.length || 0) > 0;

    switch (filterType) {
      case 'CUSTOMER':
        return false;
      case 'PORTFOLIO':
        return customerSelected && !siteSelected;
      case 'SITE':
        return customerSelected || portfolioSelected;
      default:
        return false;
    }
  }

  isSiteAuditPages(): boolean {
    return (
      this.filterDetails.page_name === FILTER_PAGE_NAME.SAFETY_JHA_LISTING ||
      this.filterDetails.page_name === FILTER_PAGE_NAME.SAFETY_SITE_AUDIT_JHA ||
      this.filterDetails.page_name === FILTER_PAGE_NAME.CM_BULK_TICKET_CREATION
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
