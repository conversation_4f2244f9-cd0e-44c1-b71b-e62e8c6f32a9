import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { LotoRoutingModule } from './loto-routing.module';
import { LotoAddEditComponent } from './loto-add-edit/loto-add-edit.component';
import { SharedModule } from '../../../../@shared/shared.module';
import { LotoBarrierInfoComponent } from './loto-barrier-info/loto-barrier-info.component';

@NgModule({
  declarations: [LotoAddEditComponent, LotoBarrierInfoComponent],
  imports: [CommonModule, SharedModule, LotoRoutingModule]
})
export class LotoModule {}
