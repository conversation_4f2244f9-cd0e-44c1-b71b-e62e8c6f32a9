import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { AlertService } from '../../../@shared/services';
import { TicketService } from '../ticket.service';

@Component({
  selector: 'sfl-app-approval-response',
  templateUrl: './app-approval-response.component.html',
  styleUrls: ['./app-approval-response.component.scss']
})
export class AppApprovalResponseComponent implements OnInit, OnDestroy {
  approvalPayload: any = {};
  loading = false;
  subscription: Subscription = new Subscription();
  actionMessage: string;
  constructor(private route: ActivatedRoute, private ticketService: TicketService, private alertService: AlertService) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      if (!params['gid'] || !params['estapid'] || !params['ticketnumber'] || !params['action'] || !params['ext'] || !params['recipient']) {
        this.loading = false;
        this.actionMessage = 'Invalid approval link, Please use a valid approval link.';
        return;
      }

      this.approvalPayload = {
        newId: params['gid'],
        ticketEstApprovalId: params['estapid'],
        ticketNumber: params['ticketnumber'],
        strExpireDate: params['ext'],
        ticketEstimateApprovalStatus: Number(params['action']),
        recipient: params['recipient']
      };

      this.performAction();
    });
  }

  performAction(): void {
    this.subscription.add(
      this.ticketService.ApproveDeniedPerformAction(this.approvalPayload).subscribe({
        next: res => {
          this.loading = false;
          this.actionMessage = res.message;
          this.alertService.showSuccessToast(this.actionMessage);
        },
        error: err => {
          this.loading = false;
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
