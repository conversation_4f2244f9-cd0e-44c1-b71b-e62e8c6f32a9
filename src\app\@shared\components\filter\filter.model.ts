export class FilterDetails {
  filter_section_name: string;
  page_name: string;
  api: Array<string>;
  filter_item: any;
  default_sort: string;
  default_direction: string;
  placeholder = 'Search';
  filterSectionEnum = FILTER_SECTION_ENUM.NONE;
}

export enum FILTER_SECTION_ENUM {
  NONE,
  SITE_INFO_DASHBOARD,
  SITE_INFO_CUSTOMER_LISTING,
  SITE_INFO_PORTFOLIO_LISTING,
  SITE_INFO_SITES_LISTING,
  SITE_INFO_DEVICES_LISTING,
  SITE_INFO_EQUIPMENT_LISTING,
  PM_DASHBOARD,
  PM_SCOPE_LISTING,
  PM_WORK_ORDER_LISTING,
  PM_REPORTS_LISTING,
  PM_SITE_AUDIT_LISTING,
  CM_DASHBOARD,
  CM_ALL_TICKETS_LISTING,
  CM_TICKET_AUDIT_LISTING,
  CM_EXCLUSION_REPORT_LISTING,
  CM_BILLING_REPORT_LISTING,
  CM_TRUCK_ROLL_REPORT_LISTING,
  CM_MAP_REPORT_LISTING,
  CM_RMA_REPORT_LISTING,
  SAFETY_JHA_LISTING,
  SAFETY_SITE_CHECK_IN_LISTING,
  SAFETY_SITE_AUDIT_JHA,
  SAFETY_NOTIFICATIONS,
  OPERATION_REGION_MAPPING_LISTING,
  OPERATIONS_CUSTOM_FORMS_FORM_LISTING,
  OPERATIONS_CUSTOM_FORMS_TEMPLATE_LISTING,
  ADMIN_USERS_LISTING,
  ADMIN_API_ERROR_LOG_LISTING,
  ADMIN_CUSTOMER_API_GATEWAY_LISTING,
  ADMIN_QE_ANALYTICS,
  CM_BULK_TICKET_CREATION
}

export const NOT_ALLOWED_SECTIONS_FOR_AUTO_FILTER = [
  FILTER_SECTION_ENUM.SITE_INFO_DASHBOARD,
  FILTER_SECTION_ENUM.CM_DASHBOARD,
  FILTER_SECTION_ENUM.ADMIN_USERS_LISTING,
  FILTER_SECTION_ENUM.ADMIN_API_ERROR_LOG_LISTING,
  FILTER_SECTION_ENUM.ADMIN_CUSTOMER_API_GATEWAY_LISTING
];

export const SHOULD_ALLOW_SELECT_ALL = [
  FILTER_SECTION_ENUM.SITE_INFO_DASHBOARD,
  FILTER_SECTION_ENUM.CM_DASHBOARD,
  FILTER_SECTION_ENUM.ADMIN_USERS_LISTING,
  FILTER_SECTION_ENUM.ADMIN_API_ERROR_LOG_LISTING,
  FILTER_SECTION_ENUM.ADMIN_CUSTOMER_API_GATEWAY_LISTING,
  FILTER_SECTION_ENUM.ADMIN_QE_ANALYTICS,
  FILTER_SECTION_ENUM.CM_BILLING_REPORT_LISTING
];

export const IS_SITE_AUDIT_PAGES = [
  FILTER_SECTION_ENUM.PM_SITE_AUDIT_LISTING,
  FILTER_SECTION_ENUM.SAFETY_SITE_AUDIT_JHA,
  FILTER_SECTION_ENUM.CM_BULK_TICKET_CREATION
];

export const FILTER_PAGE_NAME = {
  SITE_INFO_DASHBOARD: 'siteDashboardPage',
  SITE_INFO_CUSTOMER_LISTING: 'customerListingPage',
  SITE_INFO_PORTFOLIO_LISTING: 'portfolioPage',
  SITE_INFO_SITES_LISTING: 'sitePage',
  SITE_INFO_DEVICES_LISTING: 'siteDeviceListingPage',
  SITE_INFO_EQUIPMENT_LISTING: 'equipmentListingPage',
  PM_DASHBOARD: 'dashboardPage',
  PM_SCOPE_LISTING: 'assessmentListPage',
  PM_WORK_ORDER_LISTING: 'workOrderPage',
  PM_REPORTS_LISTING: 'reportListPage',
  PM_SITE_AUDIT_LISTING: 'siteAuditReportListPage',
  CM_DASHBOARD: 'cmDashBoardListingPage',
  CM_ALL_TICKETS_LISTING: 'ticketsListingPage',
  CM_TICKET_AUDIT_LISTING: 'auditDispatchReportListingPage',
  CM_EXCLUSION_REPORT_LISTING: 'exclusionsListingPage',
  CM_BILLING_REPORT_LISTING: 'ticketsBillingListingPage',
  CM_TRUCK_ROLL_REPORT_LISTING: 'truckRollListingPage',
  CM_MAP_REPORT_LISTING: 'ticketsMapPage',
  CM_RMA_REPORT_LISTING: 'rmaReportPage',
  SAFETY_JHA_LISTING: 'jhaPage',
  SAFETY_SITE_CHECK_IN_LISTING: 'sitecheckinPage',
  SAFETY_NOTIFICATIONS: 'notificationsPage',
  SAFETY_SITE_AUDIT_JHA: 'siteAuditJhaPage',
  ADMIN_QE_ANALYTICS: 'qeAnalyticsPage',
  CM_BULK_TICKET_CREATION: 'bulkTicketCreationPage'
};

export enum QE_DATE_DURATION_KEY_ENUM {
  CURRENT_DAY = 1,
  PREVIOUS_DAY = 2,
  LAST_3_DAYS = 3,
  LAST_7_DAYS = 4,
  WEEK = 5,
  CURRENT_WEEK = 6,
  PREVIOUS_WEEK = 7,
  LAST_15_DAYS = 8,
  LAST_30_DAYS = 9,
  MONTH = 10,
  CURRENT_MONTH = 11,
  PREVIOUS_MONTH = 12,
  CURRENT_QUARTER = 13,
  PREVIOUS_QUARTER = 14,
  CURRENT_HALF_YEAR = 15,
  PREVIOUS_HALF_YEAR = 16,
  CURRENT_YEAR = 17,
  PREVIOUS_YEAR = 18,
  SPECIFIC_DATE = 19,
  DATE_RANGE = 20
}

export enum QE_DATE_DURATION_KEY_LABEL_ENUM {
  CURRENT_DAY = 'Current Day',
  PREVIOUS_DAY = 'Previous Day',
  LAST_3_DAYS = 'Last 3 Days',
  LAST_7_DAYS = 'Last 7 Days',
  WEEK = 'Week',
  CURRENT_WEEK = 'Current Week',
  PREVIOUS_WEEK = 'Previous Week',
  LAST_15_DAYS = 'Last 15 Days',
  LAST_30_DAYS = 'Last 30 Days',
  MONTH = 'Month',
  CURRENT_MONTH = 'Current Month',
  PREVIOUS_MONTH = 'Previous Month',
  CURRENT_QUARTER = 'Current Quarter',
  PREVIOUS_QUARTER = 'Previous Quarter',
  CURRENT_HALF_YEAR = 'Current Half Year',
  PREVIOUS_HALF_YEAR = 'Previous Half Year',
  CURRENT_YEAR = 'Current Year',
  PREVIOUS_YEAR = 'Previous Year',
  SPECIFIC_DATE = 'Specific Date',
  DATE_RANGE = 'Date Range'
}

export const QE_DATE_DURATION_LIST = [
  { id: QE_DATE_DURATION_KEY_ENUM.CURRENT_DAY, name: QE_DATE_DURATION_KEY_LABEL_ENUM.CURRENT_DAY },
  {
    id: QE_DATE_DURATION_KEY_ENUM.PREVIOUS_DAY,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.PREVIOUS_DAY
  },
  { id: QE_DATE_DURATION_KEY_ENUM.LAST_3_DAYS, name: QE_DATE_DURATION_KEY_LABEL_ENUM.LAST_3_DAYS },
  { id: QE_DATE_DURATION_KEY_ENUM.LAST_7_DAYS, name: QE_DATE_DURATION_KEY_LABEL_ENUM.LAST_7_DAYS },
  { id: QE_DATE_DURATION_KEY_ENUM.WEEK, name: QE_DATE_DURATION_KEY_LABEL_ENUM.WEEK },
  {
    id: QE_DATE_DURATION_KEY_ENUM.CURRENT_WEEK,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.CURRENT_WEEK
  },
  {
    id: QE_DATE_DURATION_KEY_ENUM.PREVIOUS_WEEK,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.PREVIOUS_WEEK
  },
  {
    id: QE_DATE_DURATION_KEY_ENUM.LAST_15_DAYS,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.LAST_15_DAYS
  },
  {
    id: QE_DATE_DURATION_KEY_ENUM.LAST_30_DAYS,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.LAST_30_DAYS
  },
  { id: QE_DATE_DURATION_KEY_ENUM.MONTH, name: QE_DATE_DURATION_KEY_LABEL_ENUM.MONTH },
  {
    id: QE_DATE_DURATION_KEY_ENUM.CURRENT_MONTH,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.CURRENT_MONTH
  },
  {
    id: QE_DATE_DURATION_KEY_ENUM.PREVIOUS_MONTH,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.PREVIOUS_MONTH
  },
  {
    id: QE_DATE_DURATION_KEY_ENUM.CURRENT_QUARTER,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.CURRENT_QUARTER
  },
  {
    id: QE_DATE_DURATION_KEY_ENUM.PREVIOUS_QUARTER,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.PREVIOUS_QUARTER
  },
  {
    id: QE_DATE_DURATION_KEY_ENUM.CURRENT_HALF_YEAR,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.CURRENT_HALF_YEAR
  },
  {
    id: QE_DATE_DURATION_KEY_ENUM.PREVIOUS_HALF_YEAR,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.PREVIOUS_HALF_YEAR
  },
  {
    id: QE_DATE_DURATION_KEY_ENUM.CURRENT_YEAR,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.CURRENT_YEAR
  },
  {
    id: QE_DATE_DURATION_KEY_ENUM.PREVIOUS_YEAR,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.PREVIOUS_YEAR
  },
  {
    id: QE_DATE_DURATION_KEY_ENUM.SPECIFIC_DATE,
    name: QE_DATE_DURATION_KEY_LABEL_ENUM.SPECIFIC_DATE
  },
  { id: QE_DATE_DURATION_KEY_ENUM.DATE_RANGE, name: QE_DATE_DURATION_KEY_LABEL_ENUM.DATE_RANGE }
];
