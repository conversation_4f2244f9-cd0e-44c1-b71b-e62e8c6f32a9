import { Injectable } from '@angular/core';
import { CommonFilter } from './common-filter.model';
import { AlertService } from '../../services';

@Injectable({
  providedIn: 'root'
})
export class FilterHelperService {
  constructor(private readonly toastService: AlertService) {}
  private filteredFieldNames: Record<string, string> = {
    customerId: 'Customer',
    customerIds: 'Customer',
    portfolioId: 'Portfolio',
    portfolioIds: 'Portfolio',
    siteId: 'Site',
    siteIds: 'Site',
    datePerformed: 'Date',
    date: 'Date',
    year: 'Year',
    month: 'Month',
    secondaryMetric: 'Column Metric',
    primaryMetric: 'Row Metric'
  };

  private dateFields = ['date', 'openDate', 'closeDate', 'activityRange', 'exclusionTo', 'exclusionFrom'];

  checkRequiredFilteredFields(
    filterModel: CommonFilter | Record<string, any>,
    requiredFields: string[] = [],
    optionalFields: string[] = [],
    isValidFilter: boolean = true,
    friendlyFieldNames: Record<string, string> = this.filteredFieldNames
  ): boolean {
    const getFieldValueStatus = (field: string): boolean => {
      const value = filterModel?.[field];

      const isEmpty = value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0);

      const isSubFieldInvalid = this.dateFields.includes(field) && (value?.start == null || value?.end == null);

      return !(isEmpty || isSubFieldInvalid);
    };

    const missingRequiredFields = requiredFields.filter(field => !getFieldValueStatus(field));
    const allRequiredPresent = missingRequiredFields.length === 0;
    const atLeastOneOptionalPresent = optionalFields.some(field => getFieldValueStatus(field));

    let messages: string[] = [];

    if (requiredFields.length > 0 && !allRequiredPresent) {
      const missingNames = missingRequiredFields.map(f => friendlyFieldNames[f] || f);
      messages.push(`select ${missingNames.join(', ')}`);
    }

    if (optionalFields.length > 0 && !atLeastOneOptionalPresent) {
      const optionalNames = optionalFields.map(f => friendlyFieldNames[f] || f);
      messages.push(`at least one of ${optionalNames.join(', ')}`);
    }

    if (messages.length > 0) {
      const message = `Please ${messages.join(' and ')} before proceeding.`;
      if (isValidFilter) {
        this.toastService.showErrorToast(message);
      }
      return false;
    }

    return true;
  }
}
