import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { AppConstants } from '../../../../@shared/constants';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../../@shared/components/filter/filter.model';
import { CommonFilter } from '../../../../@shared/components/filter/common-filter.model';
import { UtilityAssessmentService } from '../utility-assessment.service';
import { AlertService, SharedCPSDataService } from '../../../../@shared/services';
import { StorageService } from '../../../../@shared/services/storage.service';
import { CommonService } from '../../../../@shared/services/common.service';
import { ShowerrorComponent } from '../../../../@shared/components/showerror/showerror.component';
import { ConfirmDialogComponent } from '../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import {
  GroupedScopeWithFrequencyAndDeviceTypeList,
  UtilityAssessmentCreateUpdateResponse,
  UtilityAssessmentFilterData,
  UtilityAssessmentList,
  UtilityScopeDetailPayload
} from '../utility-assessment.model';

@Component({
  selector: 'sfl-utility-assessment-listing',
  templateUrl: './utility-assessment-listing.component.html',
  styleUrls: ['./utility-assessment-listing.component.scss']
})
export class UtilityAssessmentListingComponent implements OnInit, OnDestroy {
  @ViewChild('importAssessmentDataFile') importAssessmentDataFileInput;
  private subscription: Subscription = new Subscription();

  utilityAssesmentList: UtilityAssessmentList[] = [];
  scopeListWithFrequency: GroupedScopeWithFrequencyAndDeviceTypeList[] = [];
  loading = false;
  scopeListWithFrequencyLoading = false;
  modalRef: BsModalRef;
  currentPage = 1;
  pageSize = AppConstants.rowsPerPage;
  total: number;
  viewPage = FILTER_PAGE_NAME.PMU_SCOPE_LISTING;
  filterModel: CommonFilter = new CommonFilter();
  filterDetails: FilterDetails = new FilterDetails();
  isFilterDisplay = false;
  isFullView = false;
  viewFilterSection = 'preventiveMaintenanceUtilityAssessmentFilterSection';
  sortOptionList = {
    siteName: 'asc',
    state: 'asc',
    customerPortfolio: 'asc'
  };

  constructor(
    private readonly utilityAssessmentService: UtilityAssessmentService,
    private readonly modalService: BsModalService,
    private readonly alertService: AlertService,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService,
    private readonly sharedCPSDataService: SharedCPSDataService
  ) {}

  ngOnInit(): void {
    const filter = this.storageService.get(this.viewPage),
      localFilterData = this.storageService.get('userDefaultFilter'),
      defaultFilterData = this.storageService.get('user').userFilterSelection,
      filterSection = this.storageService.get(this.viewFilterSection),
      sharedFilter = this.storageService.get(AppConstants.SHARED_FILTER_KEY),
      jumpToMenuFilterData = this.sharedCPSDataService.getSharedCPSDataForCommonFilterWithResetData();

    if (jumpToMenuFilterData) {
      this.filterModel.customerIds = jumpToMenuFilterData?.customerIds ?? [];
      this.filterModel.portfolioIds = jumpToMenuFilterData?.portfolioIds ?? [];
      this.filterModel.siteIds = jumpToMenuFilterData?.siteIds ?? [];
      this.storageService.set(this.viewPage, this.filterModel);
    } else if (filter) {
      this.filterModel = filter;
    } else {
      this.filterModel.portfolioIds = (localFilterData || defaultFilterData).portfolioIds;
      this.storageService.set(this.viewPage, this.filterModel);
    }

    this.filterModel.itemsCount = this.pageSize;
    this.isFilterDisplay = filterSection ? filterSection : false;

    if (this.filterModel.direction && this.filterModel.sortBy) {
      this.sortOptionList[this.filterModel.sortBy] = this.filterModel.direction;
    }
    if (this.filterModel.page) {
      this.currentPage = this.filterModel.page + 1;
    }
    if (this.filterModel.itemsCount) {
      this.pageSize = this.filterModel.itemsCount;
    }

    if (!this.filterModel.year) {
      this.filterModel.year = this.commonService.getCurrentYear();
    }
    this.initFilterDetails();
    this.filterModel.customerIds = (sharedFilter?.customerIds?.length ? sharedFilter.customerIds : this.filterModel.customerIds) || [];
    this.filterModel.portfolioIds = (sharedFilter?.portfolioIds?.length ? sharedFilter.portfolioIds : this.filterModel.portfolioIds) || [];
    this.filterModel.siteIds = (sharedFilter?.siteIds?.length ? sharedFilter.siteIds : this.filterModel.siteIds) || [];
    const scopeAssessmentsPageFilterKeys = ['customerIds', 'portfolioIds', 'siteIds'];
    this.getAllScopeListWithFrequencyAndDeviceType(
      this.storageService.shouldCallListApi(filter, defaultFilterData, localFilterData, sharedFilter, scopeAssessmentsPageFilterKeys)
    );
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.SITE.show = true;
    filterItem.SITE.multi = true;
    filterItem.SCOPE_TYPE.show = true;
    filterItem.START_YEAR.show = true;
    this.filterDetails.filter_item = filterItem;
  }

  cellText(valAbb: string, val: string): string {
    if (valAbb !== 'NA' && valAbb !== 'Alltime') {
      return valAbb;
    } else {
      return val;
    }
  }

  getTotalColSpan(scopeListWithFrequency: GroupedScopeWithFrequencyAndDeviceTypeList[], extraCols = 0): number {
    if (!scopeListWithFrequency?.length) return extraCols;

    const total = scopeListWithFrequency.reduce((sum, item) => sum + (item.scopeList?.length || 0), 0);

    return total + extraCols;
  }

  expandView(template: TemplateRef<any>): void {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'utility-assessment-listing-modal-full-view-dialog'
    };
    this.isFullView = true;
    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  compressView(): void {
    this.isFullView = false;
    this.modalRef.hide();
  }

  onPageChange(obj): void {
    this.currentPage = obj;
    this.filterModel.page = this.currentPage - 1;
    this.getAllUtilityAssessmentList();
  }

  sort(sortBy: string, changeSort: string): void {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllUtilityAssessmentList();
  }

  onChangeSize(): void {
    this.filterModel.page = 0;
    this.currentPage = 0;
    this.filterModel.itemsCount = Number(this.pageSize);
    this.getAllUtilityAssessmentList();
  }

  refreshList(filterParams: CommonFilter): void {
    this.currentPage = filterParams.page;
    this.getAllScopeListWithFrequencyAndDeviceType(true, filterParams);
  }

  showErrorOnImport(message): void {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: message
      }
    };
    this.modalRef = this.modalService.show(ShowerrorComponent, ngModalOptions);
  }

  setUtilityAssementList(data: UtilityAssessmentFilterData): void {
    this.utilityAssesmentList = data.utilityAssesments;
    this.total = data.totalAssesment;
    this.loading = false;
  }

  selectAndUploadAssessmentDataFile(): void {
    this.importAssessmentDataFileInput.nativeElement.click();
  }

  exportToCSV(data: UtilityAssessmentList[]): void {
    const title = 'PM Utility Scope';

    const groupHeaders = [...Array(18).fill('')];
    for (const item of this.scopeListWithFrequency) {
      groupHeaders.push(...Array(item.scopeList.length).fill(item.scopeTypeName + ' Scope Details'));
    }

    const headers = [
      'Customer',
      'Portfolio',
      'Site Name',
      'StartYear',
      'Site Address',
      'State',
      'City',
      'ZipCode',
      'Latitude',
      'Longitude',
      'Array Type',
      'Inv Type',
      'AC Size',
      'DC Size',
      '# of INV',
      '# of CB',
      '# of Panelboards',
      'Transformers'
    ];
    for (const item of this.scopeListWithFrequency) {
      for (const scope of item.scopeList) {
        headers.push(scope.abbrivation);
      }
    }

    const rows: any = [groupHeaders, headers];
    for (const i of data) {
      const tempData = [
        i.customerName,
        i.portfolioName,
        i.siteName,
        i.startYear,
        i.address,
        i.state,
        i.city,
        `="${i.zipCode}"`,
        i.latitude,
        i.logitude,
        i.siteTypeStr,
        i.invType ? i.invType : '',
        i.acSize,
        i.dcSize,
        i.invNumber,
        i.cbPanelboards ? i.cbPanelboards : '',
        i.panelboards ? i.panelboards : '',
        i.xfmr
      ];
      for (const item of this.scopeListWithFrequency) {
        for (const scope of item.scopeList) {
          tempData.push(i[scope.valueKey + 'Str'] ? i[scope.valueKey + 'Str'] : '');
        }
      }
      rows.push(tempData);
    }
    this.commonService.exportExcel(rows, title);
    this.loading = false;
  }

  exportAssessmentData(): void {
    this.loading = true;
    const model: CommonFilter = JSON.parse(JSON.stringify(this.filterModel));
    model.itemsCount = this.total;
    model.page = 0;
    model.isExport = true;
    this.subscription.add(
      this.utilityAssessmentService.getAllUtilityAssessmentsByfilter(model).subscribe({
        next: (data: UtilityAssessmentFilterData) => {
          this.exportToCSV(data.utilityAssesments);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  importAssessmentData(files: File[]): void {
    if (files.length > 0) {
      this.loading = true;
      const formData: FormData = new FormData();
      formData.append('uploadedCSVFile', files[0] as File);
      this.subscription.add(
        this.utilityAssessmentService.uploadUtilityAssessmentFile(formData).subscribe({
          next: (res: UtilityAssessmentCreateUpdateResponse) => {
            if (res.status === 200) {
              this.alertService.showSuccessToast(res.message);
            }
            if (res.status === 201) {
              this.showErrorOnImport(res);
            }
            this.importAssessmentDataFileInput.nativeElement.value = '';
            this.getAllUtilityAssessmentList();
          },
          error: (e: UtilityAssessmentCreateUpdateResponse) => {
            this.loading = false;
            this.alertService.showWarningToast(e.message);
          }
        })
      );
    }
  }

  getAllScopeListWithFrequencyAndDeviceType(isCallAssessmentList = false, filterParams?: CommonFilter): void {
    this.scopeListWithFrequencyLoading = true;
    const scopeTypeIds = filterParams?.scopeTypeIds || this.filterModel?.scopeTypeIds || [];
    const payload = new UtilityScopeDetailPayload(false, false, scopeTypeIds);
    this.subscription.add(
      this.utilityAssessmentService.getAllScopeListWithFrequencyAndDeviceType(payload).subscribe({
        next: (res: GroupedScopeWithFrequencyAndDeviceTypeList[]) => {
          this.scopeListWithFrequency = res;
          this.scopeListWithFrequencyLoading = false;
          if (isCallAssessmentList) {
            this.getAllUtilityAssessmentList(Boolean(filterParams), filterParams);
          }
        },
        error: e => {
          this.scopeListWithFrequencyLoading = false;
        }
      })
    );
  }

  getAllUtilityAssessmentList(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.subscription.add(
      this.utilityAssessmentService.getAllUtilityAssessmentsByfilter(this.filterModel).subscribe({
        next: (data: UtilityAssessmentFilterData) => {
          this.setUtilityAssementList(data);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onDelete(event: number): void {
    if (event) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to delete this assessment?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          this.loading = true;
          this.subscription.add(
            this.utilityAssessmentService.deleteUtilityAssessment(event).subscribe({
              next: res => {
                if (res) {
                  if (this.currentPage !== 0 && this.utilityAssesmentList.length === 1) {
                    this.onChangeSize();
                    this.alertService.showSuccessToast(res.message);
                  } else {
                    this.getAllUtilityAssessmentList();
                    this.alertService.showSuccessToast(res.message);
                  }
                }
              },
              error: e => {
                this.loading = false;
              }
            })
          );
        }
      });
    }
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
