import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as signalR from '@microsoft/signalr';
import { BehaviorSubject, Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ApiUrl } from '../constants';
import { AppSettings, User, appInitialsBgColors } from '../models/user.model';
import { StorageService } from './storage.service';
@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  hubConnection: signalR.HubConnection;
  hubUrl = environment.hubURL;
  user = this.storageService.get('user');
  connectionId: string;
  notificationCount$ = new BehaviorSubject<number>(0);
  loggedUser$ = new BehaviorSubject<User>(this.user);
  userDeactivationEvent$ = new BehaviorSubject<Pick<User, 'userId' | 'email'> | null>(null);
  constructor(private readonly storageService: StorageService, private readonly http: HttpClient) {}

  updateLoggedInUser(user) {
    this.loggedUser$.next(user);
  }

  connectToSignalR() {
    this.hubConnection = new signalR.HubConnectionBuilder().withUrl(this.hubUrl).build();

    this.hubConnection
      .start()
      .then(() => {
        this.connectionId = this.hubConnection.connectionId;
        this.registerHandlers();
      })
      .catch(error => {});
  }

  registerHandlers() {
    this.hubConnection
      .invoke('ReadUserCount', this.user.userId, this.connectionId)
      .then(res => {})
      .catch(err => {});
    this.getUserCount();
    this.loggedOutUserOnDeactivation();
  }

  getUserCount() {
    this.hubConnection.on('SendUserCount', count => {
      this.notificationCount$.next(count);
    });
  }

  loggedOutUserOnDeactivation(): void {
    this.hubConnection.on('SendUserDeactivationUpdate', (event: Pick<User, 'userId' | 'email'>) => {
      this.userDeactivationEvent$.next(event);
    });
  }

  getAllNotification(modal): Observable<any> {
    return this.http.post<AppSettings>(ApiUrl.GET_ALL_NOTIFICATIONS, modal);
  }

  markAsReadUnRead(modal): Observable<any> {
    return this.http.put<AppSettings>(ApiUrl.MARK_AS_READ_UNREAD, modal);
  }

  getUpdatedNotificationCount(userId): Observable<any> {
    return this.http.get<any>(`${ApiUrl.GET_NOTIFICATION_COUNT}/${userId}`);
  }

  clearCustomerCache() {
    return this.http.put(`${ApiUrl.CLEAR_CUSTOMER_CACHE}`, {});
  }

  getMoreDetailsOfNotification(notification): Observable<any> {
    return this.http.get<any>(
      `${ApiUrl.GET_NOTIFICATION_MORE_DETAILS}?entityId=${notification.entityId}&auditactionid=${notification.auditActionId}`
    );
  }

  getRandomColor() {
    const randomIndex = Math.floor(Math.random() * appInitialsBgColors.length);
    return appInitialsBgColors[randomIndex].bgColor;
  }

  getInitialsColorForBgColor(bgColor: string, initialsBgColors = appInitialsBgColors) {
    const matchedColor = appInitialsBgColors.find(item => item.bgColor === bgColor);
    if (matchedColor) {
      return matchedColor.color;
    }
    return '#fff';
  }
}
