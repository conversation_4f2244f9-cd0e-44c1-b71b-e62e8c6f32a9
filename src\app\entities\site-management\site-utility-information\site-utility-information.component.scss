// Chart container styles following application patterns
::ng-deep .nb-theme-dark {
  .chart-box {
    box-shadow: 0 0 7px 2px #151a30;
    border-radius: 9px;
    height: 100%;
    overflow: hidden;

    .chart-header {
      background: rgba(21, 26, 48, 0.8);
      border-bottom: 1px solid #2c3753;

      h6 {
        color: #ffffff;
        margin: 0;
        font-weight: 600;

        &.small {
          font-size: 0.875rem;
        }
      }
    }

    .chart-content {
      background: transparent;
      min-height: 200px;

      .no-data-message {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #888;
        font-style: italic;

        p {
          margin: 0;
        }
      }
    }
  }
}

::ng-deep .nb-theme-default,
::ng-deep .nb-theme-corporate,
::ng-deep .nb-theme-cosmic {
  .chart-box {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border-radius: 9px;
    border: 1px solid #eaeef4;
    height: 100%;
    overflow: hidden;

    .chart-header {
      background: #f8f9fa;
      border-bottom: 1px solid #dee2e6;

      h6 {
        color: #333;
        margin: 0;
        font-weight: 600;

        &.small {
          font-size: 0.875rem;
        }
      }
    }

    .chart-content {
      background: #ffffff;
      min-height: 200px;

      .no-data-message {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #6c757d;
        font-style: italic;

        p {
          margin: 0;
        }
      }
    }
  }
}

// Chart height classes
.chart-height-400 {
  height: 400px;
  width: 100%;
}

.chart-height-200 {
  height: 200px;
  width: 100%;
}

// Responsive adjustments
@media (max-width: 991.98px) {
  .chart-box {
    margin-bottom: 1rem;
  }

  .chart-height-400 {
    height: 300px;
  }

  .chart-height-200 {
    height: 180px;
  }
}

@media (max-width: 767.98px) {
  .chart-height-400 {
    height: 250px;
  }

  .chart-height-200 {
    height: 150px;
  }

  .chart-box .chart-header {
    padding: 0.75rem !important;

    h6 {
      font-size: 0.875rem;

      &.small {
        font-size: 0.75rem;
      }
    }
  }

  .chart-box .chart-content {
    padding: 0.75rem !important;
  }
}

// Field charts flex layout
.field-charts-container {
  gap: 1rem;
}

.field-chart-item {
  flex: 1 1 calc(50% - 0.5rem);
  min-width: 280px;
  max-width: 100%;
}

// Small screen adjustments
@media (max-width: 576px) {
  .col-lg-8,
  .col-lg-4 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .field-chart-item {
    flex: 1 1 100%;
    min-width: 100%;
  }

  .chart-box {
    margin-bottom: 1rem;

    .chart-header {
      padding: 0.5rem !important;

      h6 {
        font-size: 0.875rem;

        &.small {
          font-size: 0.75rem;
        }
      }
    }

    .chart-content {
      padding: 0.75rem !important;
    }
  }
}

// Medium screen adjustments
@media (max-width: 991.98px) {
  .field-chart-item {
    flex: 1 1 100%;
    min-width: 100%;
  }
}
