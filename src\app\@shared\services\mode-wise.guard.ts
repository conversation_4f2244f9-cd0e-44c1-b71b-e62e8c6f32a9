import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router } from '@angular/router';
import { UserService } from '../../entities/user-management/user.service';
import { APP_ROUTES } from '../constants';
import { AUTHORITY_ROLE_STRING, ROLE_TYPE } from '../enums';

@Injectable({
  providedIn: 'root'
})
export class ModeWiseGuard implements CanActivate {
  constructor(private readonly userService: UserService, private router: Router) {}

  canActivate(route: ActivatedRouteSnapshot): boolean {
    if (route.params['mode'] in route.data) {
      const allowedRoles = (route.data[route.params['mode']] ?? []) as Array<ROLE_TYPE>;
      const allowedAuthorities = allowedRoles?.map(item => AUTHORITY_ROLE_STRING[item]);
      if (allowedAuthorities) {
        if (this.userService.roleMatch(allowedAuthorities)) {
          return true;
        } else {
          this.router.navigateByUrl(APP_ROUTES.DASHBOARD);
          return false;
        }
      }
    } else {
      this.router.navigateByUrl(APP_ROUTES.DASHBOARD);
      return false;
    }
    return true;
  }
}
