import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { delay, Observable, of } from 'rxjs';
import { ApiUrl } from '../../../@shared/constants';
import {
  GroupedScopeWithFrequencyAndDeviceTypeList,
  ScopeWithFrequencyAndDeviceTypeList,
  UtilityAssessment,
  UtilityAssessmentCreateUpdateRequest,
  UtilityAssessmentCreateUpdateResponse,
  UtilityAssessmentFilterData,
  UtilityAssessmentList,
  UtilityScopeDetailPayload
} from './utility-assessment.model';
import { WO_GENERATION_STATUS_ENUM } from '../utility-workorder-management/utility-workorder.model';

@Injectable({
  providedIn: 'root'
})
export class UtilityAssessmentService {
  constructor(private readonly http: HttpClient) {}

  frequencyList = [
    {
      id: 1,
      name: 'Annual',
      abbrivation: 'A'
    },
    {
      id: 2,
      name: 'Semi-Annual',
      abbrivation: 'SA'
    },
    {
      id: 3,
      name: 'Quarterly',
      abbrivation: 'Q'
    },
    {
      id: 4,
      name: 'Monthly',
      abbrivation: 'M'
    },
    {
      id: 5,
      name: 'Biennial',
      abbrivation: 'BE'
    },
    {
      id: 6,
      name: 'Biennial (Even Years)',
      abbrivation: 'BE'
    },
    {
      id: 7,
      name: 'Biennial (Odd Years)',
      abbrivation: 'BO'
    },
    {
      id: 8,
      name: '24/7',
      abbrivation: '24x7'
    },
    {
      id: 9,
      name: 'Dated/Multiple',
      abbrivation: 'D'
    },
    {
      id: 10,
      name: 'N/A',
      abbrivation: 'NA'
    }
  ];
  deviceTypes = [
    {
      id: 1,
      name: 'Inverter'
    },
    {
      id: 2,
      name: 'Meter'
    },
    {
      id: 3,
      name: 'DAS'
    },
    {
      id: 4,
      name: 'Combiner Box'
    },
    {
      id: 5,
      name: 'Recombiner'
    },
    {
      id: 6,
      name: 'Modules'
    },
    {
      id: 7,
      name: 'Racking'
    },
    {
      id: 8,
      name: 'Relay'
    },
    {
      id: 9,
      name: 'Transformer'
    },
    {
      id: 10,
      name: 'Cell Modem'
    },
    {
      id: 11,
      name: 'Datalogger'
    },
    {
      id: 12,
      name: 'Weather Sensors'
    },
    {
      id: 13,
      name: 'Tracker'
    },
    {
      id: 15,
      name: 'AC Disconnect'
    },
    {
      id: 16,
      name: 'DC Disconnect'
    },
    {
      id: 17,
      name: 'Recloser'
    },
    {
      id: 18,
      name: 'Switchboard'
    },
    {
      id: 20,
      name: 'PV Tie'
    },
    {
      id: 21,
      name: 'AC Breaker'
    },
    {
      id: 23,
      name: 'UPS'
    },
    {
      id: 24,
      name: 'Weather Station'
    },
    {
      id: 28,
      name: 'AC Panelboard'
    },
    {
      id: 29,
      name: 'General'
    },
    {
      id: 30,
      name: 'Camera'
    },
    {
      id: 31,
      name: 'Switchgear'
    },
    {
      id: 32,
      name: 'Rapid Shutdown Box'
    },
    {
      id: 33,
      name: 'MV Disconnect'
    },
    {
      id: 34,
      name: 'GOAB'
    }
  ];
  scopeDetailList: ScopeWithFrequencyAndDeviceTypeList[] = [
    {
      id: 1,
      name: 'Site Visit',
      valueKey: 'siteVisit',
      abbrivation: 'SV',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: true,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        siteVisit: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 2,
      name: 'Electrical - IV Curve',
      valueKey: 'electricalIVCurve',
      abbrivation: 'IV',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: false
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        electricalIVCurve: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 3,
      name: 'Electrical - VOC',
      valueKey: 'electricalVOC',
      abbrivation: 'VOC',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        electricalVOC: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 4,
      name: 'Tracker Preventative Maintenance',
      valueKey: 'trackerPreventativeMaintenance',
      abbrivation: 'TPM',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        trackerPreventativeMaintenance: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 5,
      name: 'Medium Voltage PM',
      valueKey: 'mediumVoltagePM',
      abbrivation: 'MVPM',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        mediumVoltagePM: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 6,
      name: 'Aerial Scan',
      valueKey: 'aerialScan',
      abbrivation: 'AS',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        aerialScan: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 7,
      name: 'Thermal',
      valueKey: 'thermal',
      abbrivation: 'T',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        thermal: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 8,
      name: 'Monitoring',
      valueKey: 'monitoring',
      abbrivation: 'M',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        monitoring: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 9,
      name: 'Vegetation',
      valueKey: 'vegetation',
      abbrivation: 'V',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        vegetation: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 10,
      name: 'Module Torque',
      valueKey: 'moduleTorque',
      abbrivation: 'MT',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        moduleTorque: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 11,
      name: 'Performance Report',
      valueKey: 'performanceReport',
      abbrivation: 'PR',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        performanceReport: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 12,
      name: 'MV Thermal',
      valueKey: 'mvThermal',
      abbrivation: 'MVTH',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        mvThermal: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 13,
      name: 'Inverter PM',
      valueKey: 'inverterPM',
      abbrivation: 'IPM',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        inverterPM: null,
        deviceTypeIds: []
      },
      scopeTypeId: 1,
      scopeTypeName: 'PV'
    },
    {
      id: 14,
      name: 'GSU / Main Power Transformers',
      valueKey: 'gsuMainPowerTransformers',
      abbrivation: 'GSU',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        gsuMainPowerTransformers: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 15,
      name: 'HV Circuit Breakers (SF6, vacuum, oil)',
      valueKey: 'hvCircuitBreakers',
      abbrivation: 'HVCB',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        hvCircuitBreakers: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 16,
      name: 'Disconnect / Isolator Switches',
      abbrivation: 'DIS',
      valueKey: 'disconnectSwitches',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        disconnectSwitches: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 17,
      name: 'Instrument Transformers (CTs, PTs, CVTs)',
      abbrivation: 'IT',
      valueKey: 'instrumentTransformers',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        instrumentTransformers: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 18,
      name: 'Surge Arresters',
      abbrivation: 'SA',
      valueKey: 'surgeArresters',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        surgeArresters: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 19,
      name: 'Buswork & Insulators',
      abbrivation: 'BI',
      valueKey: 'busworkInsulators',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        busworkInsulators: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 20,
      name: 'Neutral Grounding Equipment (NGR, reactors, grounding transformer)',
      abbrivation: 'NGE',
      valueKey: 'neutralGroundingEquipment',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        neutralGroundingEquipment: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 21,
      name: 'MV Switchgear / Breakers',
      abbrivation: 'MVSG',
      valueKey: 'mvSwitchgearBreakers',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        mvSwitchgearBreakers: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 22,
      name: 'MV Disconnect Switches',
      abbrivation: 'MVDIS',
      valueKey: 'mvDisconnectSwitches',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        mvDisconnectSwitches: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 23,
      name: 'Capacitor / Reactor Banks',
      abbrivation: 'CRB',
      valueKey: 'capacitorReactorBanks',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        capacitorReactorBanks: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 24,
      name: 'Station Batteries (VLA / VRLA)',
      abbrivation: 'SB',
      valueKey: 'stationBatteries',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        stationBatteries: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 25,
      name: 'Battery Chargers',
      abbrivation: 'BC',
      valueKey: 'batteryChargers',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        batteryChargers: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 26,
      name: 'DC Panels & Ground Detectors',
      abbrivation: 'DCPGD',
      valueKey: 'dcPanelsGroundDetectors',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        dcPanelsGroundDetectors: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 27,
      name: 'Protective Relays',
      abbrivation: 'PR',
      valueKey: 'protectiveRelays',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        protectiveRelays: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 28,
      name: 'Trip/Close Circuits, Trip Coils',
      abbrivation: 'TCC',
      valueKey: 'tripCloseCircuits',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        tripCloseCircuits: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 29,
      name: 'Meters & Fault Recorders',
      abbrivation: 'MFR',
      valueKey: 'metersFaultRecorders',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        metersFaultRecorders: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 30,
      name: 'RTUs / Data Gateways',
      abbrivation: 'RTU',
      valueKey: 'rtusDataGateways',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        rtusDataGateways: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 31,
      name: 'Network Gear (switches, firewalls)',
      abbrivation: 'NG',
      valueKey: 'networkGear',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        networkGear: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 32,
      name: 'Time Clocks (GPS / IRIG-B)',
      abbrivation: 'TC',
      valueKey: 'timeClocks',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        timeClocks: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 33,
      name: 'Backup Generator',
      valueKey: 'backupGenerator',
      abbrivation: 'BG',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        backupGenerator: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 34,
      name: 'Automatic Transfer Switch (ATS)',
      abbrivation: 'ATS',
      valueKey: 'automaticTransferSwitch',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        automaticTransferSwitch: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 35,
      name: 'Control Building HVAC',
      abbrivation: 'CBHVAC',
      valueKey: 'controlBuildingHVAC',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        controlBuildingHVAC: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 36,
      name: 'Fire Protection Systems',
      abbrivation: 'FPS',
      valueKey: 'fireProtectionSystems',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        fireProtectionSystems: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 37,
      name: 'Security Systems',
      abbrivation: 'SS',
      valueKey: 'securitySystems',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        securitySystems: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 38,
      name: 'Grounding System',
      abbrivation: 'GS',
      valueKey: 'groundingSystem',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        groundingSystem: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 39,
      name: 'Lightning Protection',
      abbrivation: 'LP',
      valueKey: 'lightningProtection',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        lightningProtection: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 2,
      scopeTypeName: 'Substation'
    },
    {
      id: 40,
      name: 'Battery Racks / Containers',
      abbrivation: 'BRC',
      valueKey: 'batteryRacksContainers',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        batteryRacksContainers: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 41,
      name: 'Battery Management System (BMS)',
      abbrivation: 'BMS',
      valueKey: 'batteryManagementSystem',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        batteryManagementSystem: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 42,
      name: 'Battery Cooling Systems (liquid or HVAC)',
      abbrivation: 'BCS',
      valueKey: 'batteryCoolingSystems',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        batteryCoolingSystems: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 43,
      name: 'Inverters / Converters',
      abbrivation: 'IC',
      valueKey: 'invertersConverters',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        invertersConverters: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 44,
      name: 'MV Transformers (step-up)',
      abbrivation: 'MVT',
      valueKey: 'mvTransformers',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        mvTransformers: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 45,
      name: 'Switchgear / Breakers',
      abbrivation: 'SB',
      valueKey: 'switchgearBreakers',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        switchgearBreakers: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 46,
      name: 'Protective Relays',
      abbrivation: 'PR',
      valueKey: 'protectiveRelaysBess',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        protectiveRelaysBess: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 47,
      name: 'SCADA / EMS',
      abbrivation: 'SE',
      valueKey: 'scadaEms',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        scadaEms: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 48,
      name: 'Fire Detection & Suppression',
      abbrivation: 'FDS',
      valueKey: 'fireDetectionSuppression',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        fireDetectionSuppression: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 49,
      name: 'Station Batteries & Chargers',
      abbrivation: 'SBC',
      valueKey: 'stationBatteriesChargers',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        stationBatteriesChargers: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 50,
      name: 'UPS Systems',
      abbrivation: 'UPS',
      valueKey: 'upsSystems',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        upsSystems: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 51,
      name: 'Auxiliary Transformers',
      abbrivation: 'AT',
      valueKey: 'auxiliaryTransformers',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        auxiliaryTransformers: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 52,
      name: 'Container / Enclosure HVAC',
      abbrivation: 'CEHVAC',
      valueKey: 'containerEnclosureHvac',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        containerEnclosureHvac: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 53,
      name: 'Ventilation / Exhaust Fans',
      abbrivation: 'VEF',
      valueKey: 'ventilationExhaustFans',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        ventilationExhaustFans: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 54,
      name: 'Security Systems',
      abbrivation: 'SS',
      valueKey: 'securitySystemsBess',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        securitySystemsBess: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 55,
      name: 'Grounding & Lightning Protection',
      abbrivation: 'GLP',
      valueKey: 'groundingLightningProtection',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        groundingLightningProtection: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 56,
      name: 'Backup Generator & ATS',
      abbrivation: 'BGA',
      valueKey: 'backupGeneratorAts',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        backupGeneratorAts: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    },
    {
      id: 57,
      name: 'Safety Equipment (Eyewash, Spill Kits, PPE)',
      abbrivation: 'SE',
      valueKey: 'safetyEquipment',
      isRequired: true,
      isMultiple: false,
      deviceTypeSelection: {
        isRequired: false,
        isMultiple: true
      },
      frequency: this.frequencyList,
      deviceTypes: this.deviceTypes,
      values: {
        safetyEquipment: 10,
        deviceTypeIds: []
      },
      scopeTypeId: 3,
      scopeTypeName: 'BESS'
    }
  ];
  groupedScopes = this.scopeDetailList.reduce((acc, item) => {
    const existingGroup = acc.find(g => g.scopeTypeId === item.scopeTypeId);
    if (existingGroup) {
      existingGroup.scopeList.push(item);
    } else {
      acc.push({
        scopeTypeId: item.scopeTypeId,
        scopeTypeName: item.scopeTypeName,
        scopeList: [item]
      });
    }
    return acc;
  }, [] as GroupedScopeWithFrequencyAndDeviceTypeList[]);

  generateGetAllUtilityAssessmentsByfilterResponse(pageNumber: number, pageSize: number, isWOList = false): UtilityAssessmentFilterData {
    const randomFrequency = () => this.frequencyList[Math.floor(Math.random() * this.frequencyList.length)];

    const getRandomWoStatus = (): WO_GENERATION_STATUS_ENUM => {
      const values = Object.values(WO_GENERATION_STATUS_ENUM);
      const randomIndex = Math.floor(Math.random() * values.length);
      return values[randomIndex] as WO_GENERATION_STATUS_ENUM;
    };

    const generator = () => {
      const keys = this.scopeDetailList.map(item => item.valueKey);

      const randomFreq = randomFrequency();

      const response = keys.reduce((acc, key) => {
        acc[key] = randomFreq.id;
        acc[`${key}Str`] = randomFreq.name;
        acc[`${key}Abb`] = randomFreq.abbrivation;
        if (isWOList) {
          acc[`${key}Gen`] = getRandomWoStatus();
        }
        return acc;
      }, {} as Record<string, any>);

      return {
        id: Math.floor(Math.random() * 1000),
        customerId: Math.floor(Math.random() * 1000),
        customerName: Math.random().toString(36).substring(7),
        portfolioId: Math.floor(Math.random() * 1000),
        portfolioName: Math.random().toString(36).substring(7),
        customerPortfolio: Math.random().toString(36).substring(7),
        siteId: Math.floor(Math.random() * 1000),
        siteIds: [Math.floor(Math.random() * 1000)],
        siteName: Math.random().toString(36).substring(7),
        startYear: Math.floor(Math.random() * 10) + 2010,
        endYear: Math.floor(Math.random() * 10) + 2010,
        woGenerated: Math.random() > 0.5,
        state: Math.random().toString(36).substring(7),
        stateAbb: Math.random().toString(36).substring(2),
        address: Math.random().toString(36).substring(7),
        city: Math.random().toString(36).substring(7),
        zipCode: Math.floor(Math.random() * 100000).toString(),
        latitude: Math.random() * 180 - 90,
        logitude: Math.random() * 360 - 180,
        xfmr: Math.floor(Math.random() * 100),
        siteTypeStr: Math.random().toString(36).substring(7),
        invType: Math.random().toString(36).substring(7),
        acSize: Math.floor(Math.random() * 1000),
        dcSize: Math.floor(Math.random() * 1000),
        invNumber: Math.floor(Math.random() * 100),
        panelboards: Math.floor(Math.random() * 100).toString(),
        cbPanelboards: Math.floor(Math.random() * 100).toString(),
        ...response
      };
    };

    const total = Math.floor(Math.random() * 170) + 30;

    const startIndex = (pageNumber - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, total);

    const list: UtilityAssessmentList[] = Array.from({ length: endIndex - startIndex }, generator);

    return {
      totalAssesment: total,
      utilityAssesments: list
    };
  }

  createUtilityAssessment(utilityAssessment: UtilityAssessmentCreateUpdateRequest): Observable<UtilityAssessmentCreateUpdateResponse> {
    return this.http.post<UtilityAssessmentCreateUpdateResponse>(ApiUrl.CREATE_UTILITY_ASSESSMENT, utilityAssessment);
  }

  updateUtilityAssessment(utilityAssessment: UtilityAssessmentCreateUpdateRequest): Observable<UtilityAssessmentCreateUpdateResponse> {
    return this.http.put<UtilityAssessmentCreateUpdateResponse>(ApiUrl.UPDATE_UTILITY_ASSESSMENT, utilityAssessment);
  }

  getUtilityAssessmentById(assessmentId: number): Observable<UtilityAssessmentCreateUpdateRequest> {
    const assessment = new UtilityAssessmentCreateUpdateRequest();
    assessment.id = assessmentId;
    assessment.customerId = null;
    assessment.portfolioId = null;
    assessment.siteIds = [];
    assessment.siteId = null;
    assessment.startYear = null;
    assessment.endYear = null;
    assessment.scopeDetails = this.groupedScopes;
    return of(assessment);
    return this.http.get<UtilityAssessmentCreateUpdateRequest>(`${ApiUrl.GET_UTILITY_ASSESSMENT_BY_ID}${assessmentId}`);
  }

  getAllUtilityAssessmentsByfilter(obj): Observable<UtilityAssessmentFilterData> {
    const data = this.generateGetAllUtilityAssessmentsByfilterResponse(obj.page, obj.itemsCount);
    console.log(data.utilityAssesments[0]);
    console.log(data);
    return of(data);
    return this.http.post<UtilityAssessmentFilterData>(ApiUrl.GET_ALL_UTILITY_ASSESSMENTS_BY_FILTER, obj);
  }

  deleteUtilityAssessment(id: number): Observable<UtilityAssessmentCreateUpdateResponse> {
    return this.http.delete<UtilityAssessmentCreateUpdateResponse>(`${ApiUrl.DELETE_UTILITY_ASSESSMENT}${id}`);
  }

  uploadUtilityAssessmentFile(file: FormData): Observable<UtilityAssessmentCreateUpdateResponse> {
    return this.http.post<UtilityAssessmentCreateUpdateResponse>(ApiUrl.IMPORT_UTILITY_ASSESSMENT, file);
  }

  getScopeTypeList(): Observable<Pick<GroupedScopeWithFrequencyAndDeviceTypeList, 'scopeTypeId' | 'scopeTypeName'>[]> {
    return of(this.groupedScopes.map(item => ({ scopeTypeId: item.scopeTypeId, scopeTypeName: item.scopeTypeName })));
    return this.http.get<GroupedScopeWithFrequencyAndDeviceTypeList[]>(ApiUrl.GET_ALL_UTILITY_ASSESSMENT_SCOPE_TYPE);
  }

  getScopeDetailList(payload: UtilityScopeDetailPayload): Observable<ScopeWithFrequencyAndDeviceTypeList[]> {
    return of(this.scopeDetailList);
    return this.http.post<ScopeWithFrequencyAndDeviceTypeList[]>(ApiUrl.GET_ALL_UTILITY_ASSESSMENT_SCOPE_DETAILS, payload);
  }

  getAllScopeListWithFrequencyAndDeviceType(payload: UtilityScopeDetailPayload): Observable<GroupedScopeWithFrequencyAndDeviceTypeList[]> {
    return of(this.groupedScopes);
    return this.http.post<GroupedScopeWithFrequencyAndDeviceTypeList[]>(
      ApiUrl.GET_ALL_UTILITY_ASSESSMENT_SCOPE_WITH_FREQUENCY_AND_DEVICE_TYPE,
      payload
    );
  }
}
