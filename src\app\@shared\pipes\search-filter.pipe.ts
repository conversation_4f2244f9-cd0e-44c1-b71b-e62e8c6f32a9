import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'searchFilter'
})
export class SearchFilterPipe implements PipeTransform {
  transform(value: any = [], args: string = null, key: string = null): any {
    if (!value) {
      return null;
    }
    if (!args) {
      return value;
    } else {
      args = args.toLowerCase();
    }
    if (!key) {
      return value.filter(function (data) {
        return JSON.stringify(data).toLowerCase().includes(args);
      });
    } else {
      return value.filter(x => {
        if (x[key]) {
          return x[key].toLowerCase().includes(args);
        }
      });
    }
  }
}
