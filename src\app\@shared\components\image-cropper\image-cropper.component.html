<div class="alert-box" *ngIf="!imageFiles.length" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <h4 class="modal-title">Image Cropper</h4>

    <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
      <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
    </button>
  </div>
  <div class="modal-body">
    <div class="row">
      <div class="col-6">
        <h5>Uploaded Image</h5>
        <image-cropper
          [imageFile]="imageFile"
          [maintainAspectRatio]="true"
          [aspectRatio]="4 / 3"
          [format]="selectedImageFormat"
          (imageCropped)="imageCropped($event)"
          [canvasRotation]="canvasRotation"
          [transform]="transform"
        ></image-cropper>
      </div>
      <div class="col-6" *ngIf="croppedImage">
        <h5>Cropped Image</h5>
        <img class="cropedimage" [src]="croppedImage" alt="croppedImages" />
      </div>
    </div>
    <div class="row mt-3">
      <div class="col-12 d-flex flex-wrap justify-content-center justify-content-md-start">
        <button nbButton status="primary" size="small" (click)="rotateLeft()" class="me-2 mb-2">Rotate Left</button>
        <button nbButton status="primary" size="small" (click)="rotateRight()" class="me-2 mb-2">Rotate Right</button>
        <!-- <button nbButton status="primary" size="small" (click)="flipHorizontal()" class="me-2">Flip Horizontal</button>
        <button nbButton status="primary" size="small" (click)="flipVertical()" class="me-2">Flip Vertical</button> -->
        <button class="me-2 mb-2" nbButton status="primary" size="small" (click)="zoomOut()">Zoom -</button>
        <button nbButton status="primary" size="small" (click)="zoomIn()" class="me-2 mb-2">Zoom +</button>
        <button nbButton status="primary" size="small" (click)="resetImage()" class="me-2 mb-2">Reset Image</button>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button nbButton status="primary" size="medium" (click)="onConfirm(true)">Crop Image</button>
    <button nbButton status="basic" size="medium" (click)="onCancel()">Cancel</button>
  </div>
</div>

<div class="alert-box" *ngIf="imageFiles.length" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <h4 class="modal-title">Image Cropper</h4>

    <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
      <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
    </button>
  </div>
  <div class="modal-body">
    <div class="modal-div">
      <div *ngFor="let item of imageStringFile; let i = index" class="for-div">
        <span (click)="openThumbnail(item, i)">
          <img [src]="sanitizeUrl(item)" class="thumbnail" [ngClass]="selectedImage === item ? 'activeItem' : 'image'" alt="thumbImages" />
        </span>
      </div>
    </div>
    <div class="row" *ngIf="selectedImage">
      <div class="col-6">
        <h5>Uploaded Image</h5>
        <image-cropper
          [imageBase64]="selectedImage"
          [maintainAspectRatio]="true"
          [aspectRatio]="4 / 3"
          [format]="selectedImageFormat"
          (imageCropped)="imageCropped($event)"
          [canvasRotation]="canvasRotation"
          [transform]="transform"
        ></image-cropper>
      </div>
      <div class="col-6" *ngIf="croppedImage">
        <h5>Preview</h5>
        <img class="cropedimage" [src]="croppedImage" alt="croppedImages" />
        <div class="mt-2">
          <button nbButton status="primary" class="ms-auto" size="small" (click)="onCropAndNext()">Crop</button>
        </div>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col-12 d-flex flex-wrap justify-content-center justify-content-md-start">
        <button nbButton status="primary" size="small" (click)="rotateLeft()" class="me-2 mb-2">Rotate Left</button>
        <button nbButton status="primary" size="small" (click)="rotateRight()" class="me-2 mb-2">Rotate Right</button>
        <!-- <button nbButton status="primary" size="small" (click)="flipHorizontal()" class="me-2">Flip Horizontal</button>
        <button nbButton status="primary" size="small" (click)="flipVertical()" class="me-2">Flip Vertical</button> -->
        <button class="me-2 mb-2" nbButton status="primary" size="small" (click)="zoomOut()">Zoom -</button>
        <button nbButton status="primary" size="small" (click)="zoomIn()" class="me-2 mb-2">Zoom +</button>
        <button nbButton status="primary" size="small" (click)="resetImage()" class="me-2 mb-2">Reset Image</button>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <div class="row w-110">
      <div class="m-0 col-12 d-flex">
        <h5>Final Image<span class="ms-1 small-text">(click on save button)</span></h5>
        <div class="ms-auto">
          <button nbButton status="primary" size="medium" class="me-2" (click)="onConfirm(true)">Save</button>
          <button nbButton status="basic" size="medium" (click)="onCancel()">Cancel</button>
        </div>
      </div>
      <div class="col-12 m-0">
        <img
          *ngFor="let item of cropImageStringFile; let i = index"
          [src]="sanitizeUrl(item)"
          class="corp-thumbnail me-2"
          alt="thumbImages"
        />
      </div>
    </div>
  </div>
</div>
