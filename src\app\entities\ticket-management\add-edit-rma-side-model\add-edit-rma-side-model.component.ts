import { DatePipe } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription, forkJoin } from 'rxjs';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { ModelComponent } from '../../report/model/model.component';
import { RmaAttachments, TicketDeviceModelList, TicketDeviceTypeList, TicketRMAs } from '../ticket.model';
import { TicketService } from '../ticket.service';

@Component({
  selector: 'sfl-add-edit-rma-side-model',
  templateUrl: './add-edit-rma-side-model.component.html',
  styleUrls: ['./add-edit-rma-side-model.component.scss'],
  encapsulation: ViewEncapsulation?.None
})
export class AddEditRmaSideModelComponent implements OnInit {
  addUpdateRMAForm: FormGroup;
  public onClose: Subject<boolean>;
  @Input() ticketId;
  @Input() rmaIndex;
  @Input() isFromDetailView;
  @Input() mode;
  @Input() ticketDetail;
  @Input() rmaDetails;
  needToUpdateRmaObj: TicketRMAs;
  isRmaCompleteToggleDisabled: boolean = true;
  deviceSerialFiles: RmaAttachments[] = [];
  deviceSerialImageFiles: RmaAttachments;
  returnTrackingFiles: RmaAttachments[] = [];
  returnTrackingImageFiles: RmaAttachments;
  dependentFields = [
    'isReturnRequired',
    'startDate',
    'completeDate',
    'deviceMFG',
    'rmaNumber',
    'deviceSerialNumber',
    'returnTrackingNumber'
  ];
  loading = false;
  isRmaUpload = false;
  modalRef: BsModalRef;
  deviceTypeList: TicketDeviceTypeList[] = [];
  deviceModelList: TicketDeviceModelList[] = [];
  selectedDevice: TicketDeviceModelList;
  subscription: Subscription = new Subscription();
  public event: EventEmitter<any> = new EventEmitter();
  deviceArray = [];
  addedDevices = [];
  isActive = false;
  constructor(
    private fb: FormBuilder,
    public _bsModalRef: BsModalRef,
    public datePipe: DatePipe,
    private readonly ticketService: TicketService,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.initFormGroup();
    this.onClose = new Subject();
    this.getAddedDevices();
    if (this.rmaIndex >= 0) {
      this.needToUpdateRmaObj = this.ticketDetail.ticketRMAs[this.rmaIndex];
      const deviceAttachments = this.needToUpdateRmaObj.deviceAttachments;
      const returnAttachments = this.needToUpdateRmaObj.returnAttachments;
      this.prepareExistingAttachments(deviceAttachments, true);
      this.prepareExistingAttachments(returnAttachments, false);
      this.prepareFormForUpdateRma(this.needToUpdateRmaObj);
    } else {
      this.onRMAToggleChange(this.isActive);
    }
    this.dependentFields.forEach(field => {
      this.addUpdateRMAForm.get(field).valueChanges.subscribe(() => {
        this.updateRmaCompleteToggleDisabled();
      });
    });

    this.updateRmaCompleteToggleDisabled();
  }

  prepareExistingAttachments(attachments, isSerialFile) {
    attachments.forEach(element => {
      if (element.fileType === 'image') {
        if (isSerialFile) {
          this.deviceSerialImageFiles = element;
        } else {
          this.returnTrackingImageFiles = element;
        }
      } else {
        if (isSerialFile) {
          this.deviceSerialFiles.push(element);
        } else {
          this.returnTrackingFiles.push(element);
        }
      }
    });
  }

  initFormGroup() {
    this.addUpdateRMAForm = this.fb.group({
      deviceName: [null],
      isReturnRequired: [1],
      startDate: [null],
      completeDate: [null],
      deviceMFG: [null],
      rmaNumber: [null],
      deviceSerialNumber: [null],
      returnTrackingNumber: [null],
      isRMAComplete: [{ value: false, disabled: this.isRmaCompleteToggleDisabled }],
      isActive: [false]
    });
  }

  prepareFormForUpdateRma(rmaDetails: TicketRMAs) {
    if (rmaDetails) {
      this.addUpdateRMAForm.patchValue({
        deviceName: rmaDetails.deviceName,
        isReturnRequired: rmaDetails.isReturnRequired ? rmaDetails.isReturnRequired : 0,
        deviceMFG: rmaDetails.deviceMFG,
        startDate: rmaDetails.startDate ? new Date(rmaDetails.startDate) : null,
        completeDate: rmaDetails.completeDate ? new Date(rmaDetails.completeDate) : null,
        rmaNumber: rmaDetails.rmaNumber,
        isRMAComplete: rmaDetails.isRMAComplete ? rmaDetails.isRMAComplete : false,
        deviceSerialNumber: rmaDetails.deviceSerialNumber,
        returnTrackingNumber: rmaDetails.returnTrackingNumber,
        isActive: rmaDetails.isActive ? rmaDetails.isActive : false
      });
    }
    this.getManufacturerName(rmaDetails.siteDeviceId);
    this.onRMAToggleChange(rmaDetails.isActive);
  }

  updateRmaCompleteToggleDisabled() {
    const isFieldsFilled =
      this.addUpdateRMAForm.get('isReturnRequired').value &&
      this.addUpdateRMAForm.get('startDate').value &&
      this.addUpdateRMAForm.get('completeDate').value &&
      this.addUpdateRMAForm.get('deviceMFG').value &&
      this.addUpdateRMAForm.get('rmaNumber').value &&
      (this.addUpdateRMAForm.get('deviceSerialNumber').value || this.deviceSerialFiles.length > 0 || this.deviceSerialImageFiles) &&
      (this.addUpdateRMAForm.get('returnTrackingNumber').value || this.returnTrackingFiles.length > 0 || this.returnTrackingImageFiles);

    this.isRmaCompleteToggleDisabled = !isFieldsFilled;

    if (this.isRmaCompleteToggleDisabled) {
      this.addUpdateRMAForm.get('isRMAComplete').disable();
    } else {
      this.addUpdateRMAForm.get('isRMAComplete').enable();
    }
  }

  getAddedDevices() {
    if (this.ticketDetail && this.ticketDetail.ticketDeviceMaps.length) {
      for (const deviceObj of this.ticketDetail.ticketDeviceMaps) {
        if (deviceObj) {
          const isDeviceFoundInRma = this.rmaDetails.some(rma => rma && rma.siteDeviceId === deviceObj.siteDeviceId && !rma.isDeleted);
          const deviceName = deviceObj.deviceTypeName ? `${deviceObj.label} - (${deviceObj.deviceTypeName})` : '';
          if (deviceName) {
            this.addedDevices.push({
              name: deviceName,
              siteDeviceId: deviceObj.siteDeviceId
            });
            if (!isDeviceFoundInRma) {
              this.deviceArray.push({
                name: deviceName,
                siteDeviceId: deviceObj.siteDeviceId
              });
            }
          }
        }
      }
    }
  }

  getSiteDeviceId(deviceName) {
    let siteDeviceId = null;
    if (deviceName) {
      this.addedDevices.forEach(device => {
        if (device.name.toLowerCase() === deviceName.toLowerCase()) {
          siteDeviceId = device.siteDeviceId;
        }
      });
    }
    return siteDeviceId;
  }

  getManufacturerName(selectedDevice) {
    if (selectedDevice) {
      const device = this.ticketDetail?.ticketDeviceMaps.find(device => device.siteDeviceId === selectedDevice);
      this.addUpdateRMAForm.controls.deviceMFG.setValue(device ? device.manufacturer : null);
    }
  }

  public onConfirm(): void {
    this.onClose.next(true);
    this._bsModalRef.hide();
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  onRMAToggleChange(isActive: boolean) {
    this.isActive = isActive;
    if (isActive) {
      this.addUpdateRMAForm.get('deviceName').enable();
      this.addUpdateRMAForm.get('isReturnRequired').enable();
      this.addUpdateRMAForm.get('startDate').enable();
      this.addUpdateRMAForm.get('completeDate').enable();
      this.addUpdateRMAForm.get('rmaNumber').enable();
      this.addUpdateRMAForm.get('deviceSerialNumber').enable();
      this.addUpdateRMAForm.get('returnTrackingNumber').enable();
      !this.isRmaCompleteToggleDisabled && this.addUpdateRMAForm.get('isRMAComplete').enable();
    } else {
      this.addUpdateRMAForm.get('deviceName').disable();
      this.addUpdateRMAForm.get('isReturnRequired').disable();
      this.addUpdateRMAForm.get('startDate').disable();
      this.addUpdateRMAForm.get('completeDate').disable();
      this.addUpdateRMAForm.get('rmaNumber').disable();
      this.addUpdateRMAForm.get('deviceSerialNumber').disable();
      this.addUpdateRMAForm.get('returnTrackingNumber').disable();
      this.addUpdateRMAForm.get('isRMAComplete').disable();
    }
  }

  getUploadedFiles(files, isSerialFiles) {
    const file = files.item(0);
    if (file && file.size <= 10485760) {
      if (file.type.match('image.*')) {
        const reader = new FileReader();
        reader.onload = event => {
          const dataUrl = event.target.result as string;
          if (isSerialFiles) {
            this.deviceSerialImageFiles = this.prepareFileObj(file, dataUrl, 1);
          } else {
            this.returnTrackingImageFiles = this.prepareFileObj(file, dataUrl, 2);
          }
          this.updateRmaCompleteToggleDisabled();
        };
        reader.readAsDataURL(file);
      } else {
        if (isSerialFiles) {
          this.deviceSerialFiles.push(this.prepareFileObj(file, '', 1));
        } else {
          this.returnTrackingFiles.push(this.prepareFileObj(file, '', 2));
        }
        this.updateRmaCompleteToggleDisabled();
      }
      this.isRmaUpload = true;
    } else {
      this.alertService.showErrorToast('File size should not be allowed more than 10MB');
      return;
    }
  }

  prepareFileObj(file, dataUrl, docType) {
    return {
      ticketId: this.ticketId,
      rmaDocId: 0,
      rmaId: this.needToUpdateRmaObj && this.needToUpdateRmaObj.rmaId ? this.needToUpdateRmaObj.rmaId : 0,
      siteDeviceId: 0,
      documentUrl: dataUrl,
      fileExtension: file.type.split('/')[1],
      fileName: file.name,
      fileType: file.type.split('/')[0] && file.type.split('/')[0] === 'application' ? 'document' : file.type.split('/')[0],
      isDeleted: false,
      docType: docType,
      attachFile: file
    };
  }

  downloadAttachment(url, fileName, rmaDocId) {
    if (!rmaDocId) {
      fetch(url)
        .then(response => response.blob())
        .then(blob => {
          const link = this.commonService.createObject(blob, '');
          link.download = fileName;
          link.click();
        })
        .catch(error => console.error('Error downloading image:', error));
    } else {
      this.loading = true;
      this.ticketService.downloadRmaAttachment(rmaDocId).subscribe({
        next: data => {
          if (data) {
            const link = this.commonService.createObject(data, data.type);
            link.download = fileName;
            link.click();
            this.loading = false;
          } else {
            this.loading = false;
          }
        },
        error: e => {
          this.loading = false;
        }
      });
    }
  }

  deleteUploadedAttachment(index: number, isSerialFile: boolean, isImg: boolean) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: 'Are you sure you want to delete?'
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(
      result => {
        if (result) {
          this.loading = true;
          this.isRmaUpload = true;
          if (isSerialFile) {
            this.removeSerialDeviceFile(isImg, index);
          } else {
            this.removeReturnTrackingFile(isImg, index);
          }
          this.loading = false;
        }
      },
      err => {
        this.loading = false;
      }
    );
  }

  removeSerialDeviceFile(isImg, index) {
    if (this.mode === 'create') {
      if (isImg) {
        this.deviceSerialImageFiles = null;
      } else {
        this.deviceSerialFiles.splice(index, 1);
      }
    } else if (this.mode === 'edit') {
      if (isImg) {
        this.deviceSerialImageFiles = {
          ...this.deviceSerialImageFiles,
          isDeleted: true
        };
      } else {
        this.deviceSerialFiles[index].isDeleted = true;
      }
    }
  }

  removeReturnTrackingFile(isImg, index) {
    if (this.mode === 'create') {
      if (isImg) {
        this.returnTrackingImageFiles = null;
      } else {
        this.returnTrackingFiles.splice(index, 1);
      }
    } else if (this.mode === 'edit') {
      if (isImg) {
        this.returnTrackingImageFiles = {
          ...this.returnTrackingImageFiles,
          isDeleted: true
        };
      } else {
        this.returnTrackingFiles[index].isDeleted = true;
      }
    }
  }

  imagePopup(item, index = 0) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-xl img-view-section',
      initialState: {
        imageList: [item],
        selectedImageIndex: index
      }
    };
    this.modalRef = this.modalService.show(ModelComponent, ngModalOptions);
  }

  onRmaForDevice() {
    this.loading = true;
    console.log(this.addUpdateRMAForm.valid);
    if (this.addUpdateRMAForm.valid) {
      console.log(this.isActive);
      if (!this.isActive) {
        this.addUpdateRMAForm.get('deviceName').setValue(null);
        this.addUpdateRMAForm.get('deviceMFG').setValue(null);
        this.addUpdateRMAForm.get('isReturnRequired').setValue(1);
        this.addUpdateRMAForm.get('startDate').setValue(null);
        this.addUpdateRMAForm.get('completeDate').setValue(null);
        this.addUpdateRMAForm.get('rmaNumber').setValue(null);
        this.addUpdateRMAForm.get('deviceSerialNumber').setValue(null);
        this.addUpdateRMAForm.get('returnTrackingNumber').setValue(null);
        this.addUpdateRMAForm.get('isRMAComplete').setValue(false);
      }
      const formValue = this.addUpdateRMAForm.value;
      const deviceAttachments = this.deviceSerialImageFiles
        ? [...this.deviceSerialFiles, this.deviceSerialImageFiles]
        : this.deviceSerialFiles;
      const returnAttachments = this.returnTrackingImageFiles
        ? [...this.returnTrackingFiles, this.returnTrackingImageFiles]
        : this.returnTrackingFiles;
      deviceAttachments.forEach(obj => {
        obj.siteDeviceId =
          this.needToUpdateRmaObj && this.needToUpdateRmaObj.siteDeviceId
            ? this.needToUpdateRmaObj.siteDeviceId
            : this.getSiteDeviceId(formValue.deviceName);
      });
      returnAttachments.forEach(obj => {
        obj.siteDeviceId =
          this.needToUpdateRmaObj && this.needToUpdateRmaObj.siteDeviceId
            ? this.needToUpdateRmaObj.siteDeviceId
            : this.getSiteDeviceId(formValue.deviceName);
      });
      const ticketRMAs = {
        ...formValue,
        startDate: this.ticketService.formateDate(formValue.startDate),
        completeDate: this.ticketService.formateDate(formValue.completeDate),
        rmaId: this.mode !== 'create' ? this.needToUpdateRmaObj.rmaId : 0,
        ticketId: this.ticketId,
        isDeleted: false,
        siteDeviceId: this.getSiteDeviceId(formValue.deviceName),
        deviceAttachments: deviceAttachments && deviceAttachments.length ? deviceAttachments : [],
        returnAttachments: returnAttachments && returnAttachments.length ? returnAttachments : []
      };
      if (this.mode === 'create') {
        this.ticketDetail.ticketRMAs = [...this.ticketDetail.ticketRMAs, ticketRMAs];
      } else {
        this.ticketDetail.ticketRMAs[this.rmaIndex] = ticketRMAs;
      }
      if (this.isFromDetailView) {
        this.ticketService.addSingleRmaDetails(ticketRMAs).subscribe({
          next: data => {
            this.sendRmaFileAttachmentsToServer(deviceAttachments, returnAttachments);
            this.alertService.showSuccessToast('RMA Created Successfully');
          },
          error: e => {
            this.loading = false;
          }
        });
      } else {
        this.deviceArray = [];
        this.addedDevices = [];
        this.event.emit({
          ticketDetail: this.ticketDetail,
          isRmaUpload: this.isRmaUpload
        });
      }
      this.loading = false;
      this._bsModalRef.hide();
    } else {
      this.loading = false;
      this.alertService.showErrorToast('Please fill the mandatory fields to submit the form.');
      return;
    }
  }

  sendRmaFileAttachmentsToServer(deviceAttachments, returnAttachments) {
    this.loading = true;
    let combinedAttachments = [];
    if (deviceAttachments.length > 0) {
      combinedAttachments = [...deviceAttachments];
    }
    if (returnAttachments.length > 0) {
      combinedAttachments = [...combinedAttachments, ...returnAttachments];
    }
    combinedAttachments.forEach(element => {
      element.ticketId = this.ticketId;
    });
    const tempArray: any = [];
    for (const obj of combinedAttachments) {
      const formData: FormData = new FormData();
      formData.append('attachFile', obj.attachFile as File);
      formData.append('ticketId', obj.ticketId);
      formData.append('rmaDocId', obj.rmaDocId);
      formData.append('rmaId', obj.rmaId);
      formData.append('siteDeviceId', obj.siteDeviceId);
      formData.append('documentUrl', obj.documentUrl);
      formData.append('fileName', obj.fileName);
      formData.append('fileType', obj.fileType);
      formData.append('fileExtension', obj.fileExtension);
      formData.append('isDeleted', obj.isDeleted);
      formData.append('docType', obj.docType);
      tempArray.push(this.ticketService.uploadRmaAttachment(formData));
    }
    forkJoin(tempArray).subscribe({
      next: (response: any) => {
        this.isRmaUpload = false;
        this.getRMAUpdatesList();
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getRMAUpdatesList() {
    this.ticketService.getRmaListByTicketNumber(this.ticketDetail.ticketNumber).subscribe({
      next: data => {
        this.ticketDetail.ticketRMAs = data;
        this.deviceArray = [];
        this.addedDevices = [];
        this.event.emit({
          ticketDetail: this.ticketDetail,
          isRmaUpload: this.isRmaUpload
        });
        this._bsModalRef.hide();
        this.loading = false;
      },
      error: e => {
        this.loading = false;
      }
    });
  }
}
