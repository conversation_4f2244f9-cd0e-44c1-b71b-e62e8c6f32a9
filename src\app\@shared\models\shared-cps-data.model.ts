export class SharedCPSNames {
  customerName: string | string[];
  portfolioName: string | string[];
  siteName: string | string[];

  constructor(sharedCPSNames: Partial<SharedCPSNames>) {
    Object.assign(this, sharedCPSNames);
  }
}

export class SharedCPSIds {
  customerId: number | number[];
  portfolioId: number | number[];
  siteId: number | number[];

  constructor(sharedCPSIds: Partial<SharedCPSIds>) {
    Object.assign(this, sharedCPSIds);
  }
}

export interface SharedCPSData extends SharedCPSNames, SharedCPSIds {}

export class SharedCPSData implements SharedCPSNames {
  customerName: string;
  portfolioName: string;
  siteName: string;
  customerId: number;
  portfolioId: number;
  siteId: number;
  customerNames: string | string[];
  portfolioNames: string | string[];
  siteNames: string | string[];
  customerIds: number | number[];
  portfolioIds: number | number[];
  siteIds: number | number[];

  constructor(sharedCPSData: Partial<SharedCPSData>) {
    Object.assign(this, sharedCPSData);
  }
}

export const CommonFilterDataForSharedCPSDataForKeys = [
  'customerNames',
  'portfolioNames',
  'siteNames',
  'customerIds',
  'portfolioIds',
  'siteIds'
];

export const ResponseDataForSharedCPSDataForKeys = ['customerName', 'portfolioName', 'siteName', 'customerId', 'portfolioId', 'siteId'];

export const CombinedDataForSharedCPSDataForKeys = [...CommonFilterDataForSharedCPSDataForKeys, ...ResponseDataForSharedCPSDataForKeys];

export enum PICKABLE_KEYS_FOR_SHARED_CPS_DATA {
  COMMON_FILTER_DATA = 1,
  RESPONSE_DATA,
  COMBINED_DATA
}

export const SharedCPSDataForKeys = {
  [PICKABLE_KEYS_FOR_SHARED_CPS_DATA.COMMON_FILTER_DATA]: CommonFilterDataForSharedCPSDataForKeys,
  [PICKABLE_KEYS_FOR_SHARED_CPS_DATA.RESPONSE_DATA]: ResponseDataForSharedCPSDataForKeys,
  [PICKABLE_KEYS_FOR_SHARED_CPS_DATA.COMBINED_DATA]: CombinedDataForSharedCPSDataForKeys
};

export const SharedCPSDataKeyMappingToCommonFilterKey: Record<string, string> = {
  customerName: 'customerNames',
  portfolioName: 'portfolioNames',
  siteName: 'siteNames',
  customerId: 'customerIds',
  portfolioId: 'portfolioIds',
  siteId: 'siteIds'
};
