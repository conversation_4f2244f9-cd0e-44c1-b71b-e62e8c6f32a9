export class SiteCheckinOut {
  id: string;
  userId: number;
  siteId: number;
  driveTime: number;
  date: Date | string;
  latitude: number;
  longitude: number;
  notes: string;
  reason: string;
  reasons: string[] = [];
  status: number;
  isDeleted: number;
  isUploaded: number;
  siteTimeZoneOffset: number;
  utcTimeStamp: string;
  staySafe: boolean;
  otherReason: string;
  dateEpoch: number;
  userName: string;
  customer: string;
  portfolio: string;
  siteName: string;
  reasonArrays: ReasonArrays[] = [];
  vegetationImagePath: string;
  vegetationImageFileName: string;
  gateImagePath: string;
  gateImageFileName: string;
}

export interface ReasonArrays {
  name: string;
  checked: boolean;
  val: string;
}

export interface SiteCheckinDetails {
  userId: number;
  userName: string;
  siteId: number;
  customer: string;
  portfolio: string;
  siteName: string;
  date: string;
  utcTimeStamp: string;
  siteCheckInCheckOutAuditDtos: SiteCheckinOut[];
}

export interface SiteDetails {
  abbreviation: string;
  customerId: number;
  endDate: string;
  id: number;
  isActive: boolean;
  isArchive: boolean;
  isAutomationSite: boolean;
  name: string;
  portfolioId: number;
  siteNumber: number;
  siteTimeZoneOffset: number;
  startDate: string;
}

export interface UploadImagesRequest {
  gateImage: File;
  vegetationImage: File;
}
