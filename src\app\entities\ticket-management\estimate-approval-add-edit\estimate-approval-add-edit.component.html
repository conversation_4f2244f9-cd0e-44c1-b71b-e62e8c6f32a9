<div class="alert-box" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <div class="d-flex align-items-center w-100">
      <h6>{{ isApprovalCreate ? 'Create' : 'Edit' }} Approval</h6>
      <div class="ms-auto">
        <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
          <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
        </button>
      </div>
    </div>
  </div>
  <div class="modal-body ModalBody approval-detail-modal-body">
    <div>
      <form
        name="approvalForm"
        #approvalForm="ngForm"
        aria-labelledby="title"
        autocomplete="off"
        (ngSubmit)="approvalForm?.form?.valid && onAddEditApprovalItem()"
      >
        <div class="row">
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-exclusionFrom">Status</label>
            <ng-select
              name="TruckRollId"
              [items]="statusDropDownList"
              bindLabel="name"
              bindValue="ticketEstimateApprovalStatusID"
              [(ngModel)]="approvalDetails.ticketEstimateApprovalStatusID"
              notFoundText="No status Found"
              placeholder="Select status"
              [closeOnSelect]="true"
              [clearable]="false"
              [disabled]="isApprovalCreate"
            >
            </ng-select>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-exclusionFrom">Approval Sent</label>
            <div>
              {{ approvalDetails.createdDate ? (approvalDetails.createdDate | date : 'MM/dd/yyyy') : '--' }}
            </div>
          </div>
          <div class="col-12 mb-3">
            <label class="label" for="input-exclusionFrom">Notes</label>
            <textarea
              nbInput
              fullWidth
              placeholder="Enter notes"
              rows="4"
              [(ngModel)]="approvalDetails.notes"
              (ngModelChange)="onNotesChange()"
              name="notes"
              id="notes"
              maxlength="512"
            ></textarea>
          </div>
          <div class="col-12 mb-3">
            <label class="label" for="input-exclusionFrom">Recipients</label>
            <nb-tag-list (tagRemove)="onTagRemove($event)">
              <nb-tag *ngFor="let recipient of recipientsArray" [text]="recipient" removable></nb-tag>
              <input
                type="text"
                nbTagInput
                (tagAdd)="onTagAdd($event)"
                (blur)="onTagInputBlur($event)"
                (keydown)="onTagInputKeyDown($event)"
                maxlength="500"
                fullWidth
              />
            </nb-tag-list>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-exclusionFrom">Approved On</label>
            <div>
              {{ approvalDetails.ticketEstimateApprovalStatusID === 3 ? (approvalDetails.actionOn | date : 'MM/dd/yyyy') : '--' }}
            </div>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-exclusionFrom">Denied On</label>
            <div>
              {{ approvalItem.ticketEstimateApprovalStatusID === 2 ? (approvalItem.actionOn | date : 'MM/dd/yyyy') : '--' }}
            </div>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-exclusionFrom">Approved By</label>
            <div>
              {{ approvalItem.ticketEstimateApprovalStatusID === 3 ? approvalItem.actionByName : '--' }}
            </div>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-exclusionFrom">Denied By</label>
            <div>
              {{ approvalItem.ticketEstimateApprovalStatusID === 2 ? approvalItem.actionByName : '--' }}
            </div>
          </div>
          <div class="col-12 mb-3">
            <label class="label" for="input-exclusionFrom">Submitted By</label>
            <div>
              {{ approvalDetails.createdByName || '--' }}
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer ModalFooter">
      <button nbButton status="basic" size="medium" type="button" (click)="onCancel()">Cancel</button>
      <button nbButton status="primary" size="medium" id="deviceSubmit" type="submit" (click)="onAddEditApprovalItem()">
        <!-- {{ isSendEmail === 1 ? 'Send' : 'Save' }} -->Send
      </button>
    </div>
  </div>
</div>

<ng-template #approvalReasonModel>
  <div class="alert-box" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
    <div class="modal-header justify-content-between align-items-center">
      <h5 class="mb-0">
        {{ this.approvalDetails.ticketEstimateApprovalStatusID === 3 ? 'Approval Reasoning' : 'Denial Reasoning' }}
        <span class="ms-1 text-danger">*</span>
      </h5>
    </div>

    <div class="modal-body alert-details-body">
      <form
        name="approvalReasonForm"
        #approvalReasonForm="ngForm"
        aria-labelledby="title"
        autocomplete="off"
        (ngSubmit)="approvalReasonForm?.form?.valid && saveSendApproval()"
      >
        <div class="row g-3">
          <div class="col-12">
            <textarea
              nbInput
              name="reason"
              #reason="ngModel"
              id="input-reason"
              class="reason col-12 size-large"
              rows="4"
              [(ngModel)]="approvalDetails.actionReason"
              required
              placeholder="Enter reasoning"
              maxlength="500"
            ></textarea>
            <sfl-error-msg
              [control]="reason"
              [isFormSubmitted]="approvalReasonForm?.submitted"
              fieldName="{{ this.approvalDetails.ticketEstimateApprovalStatusID === 3 ? 'Approval Reasoning' : 'Denial Reasoning' }}"
            ></sfl-error-msg>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer ModalFooter">
      <button nbButton status="basic" size="medium" type="button" (click)="approvalReasonModalRef.hide()">Back</button>
      <button
        nbButton
        status="primary"
        size="medium"
        [disabled]="!approvalDetails.actionReason"
        id="deviceSubmit"
        type="submit"
        (click)="saveSendApproval()"
      >
        Save
      </button>
    </div>
  </div>
</ng-template>
