export function debounce(timeout: number) {
  return function (target: any, propertyKey: string, descriptor: any): void {
    const originalMethod = descriptor.value!;
    let timeoutRef: ReturnType<typeof setTimeout> | null = null;

    descriptor.value = function (...args: any[]) {
      clearTimeout(timeoutRef!);
      timeoutRef = setTimeout(() => originalMethod.apply(this, args), timeout);
    };

    descriptor.value.cancelDebounce = () => clearTimeout(timeoutRef!);
  };
}
