import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NbThemeService } from '@nebular/theme';
import { Subscription } from 'rxjs';
import { AlertService } from '../../@shared/services';
import { PowerCardsService } from '../performance/power-cards/power-cards.service';
import {
  ChartViewList,
  ErrorCodeEnum,
  FilterModel,
  ShareableChartDataResponse,
  ShareablePowerChartModel
} from '../performance/power-chart/power-chart.model';
import { PowerChartService } from '../performance/power-chart/power-chart.service';

@Component({
  selector: 'sfl-shared-power-chart',
  templateUrl: './shared-power-chart.component.html',
  styleUrls: ['./shared-power-chart.component.scss']
})
export class SharedPowerChartComponent implements OnInit, OnDestroy {
  private subscription: Subscription = new Subscription();

  loading = false;
  selectedSiteChart: any = null;
  currentTheme = 'dark';
  message: string;
  powerChartData: ShareablePowerChartModel;
  filterModel: FilterModel;
  chartViewList: ChartViewList[];
  isValid: boolean;
  errorCode: number;
  errorCodeEnum = ErrorCodeEnum;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly powerChartService: PowerChartService,
    private readonly alertService: AlertService,
    private readonly themeService: NbThemeService,
    private readonly powerCardsService: PowerCardsService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      const token = atob(params['token']);
      this.getChartData(token);
    });
    this.themeService.onThemeChange().subscribe(themeName => {
      this.currentTheme = themeName.name;
    });
  }

  getChartData(token) {
    this.loading = true;
    this.subscription.add(
      this.powerChartService.getSharableChartData(token).subscribe({
        next: (res: ShareableChartDataResponse) => {
          if (res.isValid) {
            this.powerChartData = res.result;
            this.filterModel = this.powerChartData.filterModel;
            this.chartViewList = this.powerChartData.chartViewList;
            this.isValid = res.isValid;
            this.showSiteChart();
          } else {
            this.isValid = res.isValid;
            this.message = res.message;
            this.errorCode = res.errorCodeEnum;
            this.loading = false;
          }
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  showSiteChart() {
    this.selectedSiteChart = null;

    this.subscription.add(
      this.powerCardsService.getSiteSunriseSunsetChartsInfo(this.powerChartData.filterModel).subscribe({
        next: sunriseSunsetData => {
          const powerChartSiteSunriseSunsetData = this.powerChartService.setPowerChartSiteSunriseSunsetData(
            this.powerChartData.filterModel,
            sunriseSunsetData
          );
          this.powerCardsService.getChartsData(this.powerChartData.filterModel).subscribe({
            next: deviceData => {
              const powerChartSitesDateTimeData = this.powerChartService.setPowerChartSiteTimeData(
                this.powerChartData.filterModel,
                deviceData.powerChartSitesDateTime
              );

              this.selectedSiteChart = this.powerCardsService.generateSingleSiteChart(
                deviceData.powerChartData,
                powerChartSiteSunriseSunsetData,
                powerChartSitesDateTimeData,
                this.powerChartData.siteName,
                this.powerChartData.isSingleTooltip,
                this.chartViewList
              );
              this.loading = false;
            },
            error: error => {
              this.alertService.showErrorToast('Failed to load chart data. Please try again later.');
              this.loading = false;
            }
          });
        },
        error: error => {
          this.alertService.showErrorToast('Error loading sunrise/sunset data. Please try again later.');
          this.loading = false;
        }
      })
    );
  }

  goToHome() {
    this.router.navigate(['/entities/dashboard']);
  }

  goToSiteInfo() {
    this.router.navigate(['/entities/sites']);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
