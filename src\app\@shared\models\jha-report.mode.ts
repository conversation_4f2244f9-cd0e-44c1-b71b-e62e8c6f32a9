export class JhaReportModel {
  public id: number;
  public customerName: string;
  public portflioName: string;
  public siteName: string;
  public workorderId: number;
  public workorderName: string;
  public createdDate: string;
  public reportDocument: string;
}

export class JhaReportModelDTO {
  public totalReport: number;
  public jhaReport: JhaReportModel[];
}

export class MasterJHAReportModelList {
  public customerName: string;
  public portfolioName: string;
  public siteName: string;
  public workorderName: string;
  public workorderId: number;
  public reportTitle: string;
  public jhaReportDetail: JhaReportMaster;
  public reportPdf: string;
  public siteId: number;
  public customerId: number;
  public portfolioId: number;
  public assesmentId: number;
  public assesmentType: string;
  public frequencyType: string;
}

export class JhaReportMaster {
  public reportTitle: string;
  public reporterName: string;
  public reportCreatedOn: Date;
  public reportCreatedOnStr: string;
  public reportUploadOn: Date;
  public reportUploadOnStr: string;
  public reportId: string;
  public jhaReports: JHAReportModelList;
  public generalImages: GeneralImagesMaster[];
  public comments: CommentMaster;
  public isFinalReportImageCount: boolean;
  public versionId: number;
}

export class JHAReportModelList {
  public order: number;
  public isFinal: boolean;
  public reportTitle: string;
  public reporterName: string;
  public reportCreatedOn: Date;
  public reportCreatedOnStr: string;
  public reportUploadOn: Date;
  public reportUploadOnStr: string;
  public reportGuid: string;
  public jhaGuid: string;
  public createdDate: Date;
  public createdDateStr: string;
  public siteFacility: string;
  public otherHazards: string;
  public siteContact: string;
  public sitePhone: string;
  public notes: string;
  public conductedBy: string;
  public descriptionOfWork: DescriptionWork[];
  public potentialHazardValues: ExpectedValue[];
  public selectedItems: number[];
  public jhaSPH: JHASPHModel;
  public jhaPersonalAttending: JHAPAModel[];
  public jhaQERepresentitive: QERepresentativeModel[];
}

export class DescriptionWork {
  public id: number;
  public name: string;
  public sectionId: number;
  public sectionName: string;
  public sectionAbbreviation: string;
  public abbreviation: string;
  public isSelected: boolean;
}

export class ExpectedValue {
  public id: number;
  public name: string;
  public sectionId: number;
  public sectionName: string;
  public sectionAbbreviation: string;
  public abbreviation: string;
  public isSelected: boolean;
}

export class QERepresentativeModel {
  public id: number;
  public userID: number;
  public name: string;
  public phone: number;
}

export class JHAPAModel {
  public paGuid: string;
  public jhaGuid: string;
  public name: string;
  public visitDates: Date;
  public visitDatestr: string;
  public imageGuid: string;
  public imageURL: string;
  public thumbnailUrl: string;
  public isIncludeReport: boolean;
  public originalImage: string;
}

export class JHASPHModel {
  public basicJobSteps: BasicJobSteps[];
  public potential: BasicJobSteps[];
  public hazardsControl: BasicJobSteps[];
}

export class BasicJobSteps {
  public jhaGuid: string;
  public sphGuid: string;
  public value: string;
  public type: string;
  public order: number;
}

export class GeneralImagesMaster {
  public imageGuid: string;
  public giGuid: string;
  public imageUrl: string;
  public thumbnailUrl: string;
  public reportId: string;
  public isIncludeinReport: boolean;
  public originalImage: string;
  public type: string;
  public order: number;
}

export class CommentMaster {
  public id: string;
  public comment: string;
  public reportId: string;
  public createdBy: number;
  public createdOn: Date;
  public createByName: string;
  public updatedBy: number;
  public updatedOn: Date;
  public updatedByName: string;
}
