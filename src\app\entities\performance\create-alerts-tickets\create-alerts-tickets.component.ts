import { DatePipe } from '@angular/common';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { AppConstants } from '../../../@shared/constants';
import { AlertService } from '../../../@shared/services';
import { NavigationBack } from '../../ticket-management/ticket.model';
import { CreatePowerAlertTickets, CreatePowerAlertTicketsResponse } from '../power-cards/power-cards-model';
import { PowerChartService } from '../power-chart/power-chart.service';

@Component({
  selector: 'sfl-create-alerts-tickets',
  templateUrl: './create-alerts-tickets.component.html'
})
export class CreateAlertsTicketsComponent implements OnInit, OnDestroy {
  loading = false;
  isCreated = false;
  minDate = new Date();
  @Input() selectedAlertDetails: any[] = [];
  modalRef: BsModalRef;
  public onClose: Subject<any> = new Subject();
  subscription: Subscription = new Subscription();
  createAlertTicketModel: CreatePowerAlertTickets = new CreatePowerAlertTickets();

  constructor(
    private readonly _bsModalRef: BsModalRef,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly powerChartService: PowerChartService,
    private readonly datePipe: DatePipe,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    this.createAlertTicketModel.openDate = new Date();
    this.selectedAlertDetails.forEach(alert => {
      alert.isSelected = this.selectedAlertDetails.length === 1;
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onCloseModel(val) {
    this.onClose.next(val);
    this._bsModalRef.hide();
  }

  selectAllMissingDevices() {
    this.selectedAlertDetails.forEach(alert => {
      const hasProductionLoss = alert.ticketsData?.some((ticket: any) => ticket.isProductionLoss === true);
      if (!alert.ticketNumber && !hasProductionLoss) {
        alert.isSelected = true;
      } else {
        alert.isSelected = false;
      }
    });
  }

  singleDeviceSelectionChanged(alert: any) {
    alert.isSelected = !alert.isSelected;
  }

  isDeviceInProgress(ticketData: any[]): boolean {
    return ticketData?.some((item: any) => item.isProductionLoss === true);
  }

  CreateBulkTickets() {
    const selectedDeviceIds = this.selectedAlertDetails.filter(alert => alert.isSelected).map(alert => alert.deviceId);

    if (!selectedDeviceIds.length) {
      this.alertService.showWarningToast('Please select at least one device to create tickets.');
      return;
    }

    this.loading = true;
    const requestParams = {
      siteDeviceIds: selectedDeviceIds,
      siteId: this.selectedAlertDetails[0].siteId,
      ticketIssue: this.createAlertTicketModel.ticketIssue,
      openDate: this.datePipe.transform(this.createAlertTicketModel.openDate, AppConstants.fullDateFormat)
    };

    this.subscription.add(
      this.powerChartService.createAlertsTickets(requestParams).subscribe({
        next: (response: CreatePowerAlertTicketsResponse) => {
          this.loading = false;
          if (response) {
            this.alertService.showSuccessToast(`Tickets created Successfully.`);
            const updatedAlertsData = this.selectedAlertDetails.map(alert => {
              const matchedDevice = response?.siteDeviceIds?.find(siteDeviceId => siteDeviceId === alert.deviceId);
              if (matchedDevice) {
                alert.ticketsData.push({
                  siteDeviceId: alert.deviceId,
                  ticketNumber: response.ticketNumber,
                  ticketId: 0,
                  isProductionLoss: true
                });
              }
              return alert;
            });
            this.onCloseModel({ success: true, data: updatedAlertsData });
          } else {
            this.alertService.showErrorToast('No tickets were created. Please try again.');
          }
        },
        error: error => {
          this.loading = false;
          this.alertService.showErrorToast('Failed to create tickets. Please try again later.');
        }
      })
    );
  }

  openLink(ticketNumber: string, inNewWindow: boolean) {
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['../entities/ticket/detail/view', `${ticketNumber}`], { queryParams: { back: NavigationBack.TICKETS } })
    );
    if (inNewWindow) {
      window.open(url, '_blank', 'width=' + screen.availWidth + ',height=' + screen.availHeight);
    } else {
      window.open(url, '_blank');
    }
  }
}
