import { Component, Input, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { ReportService } from '../../../entities/report/report.service';
import { NC_ACTION_ISSUE_TYPE } from '../../models/report.model';
import { AlertService } from '../../services';
import {
  BulkNCTicketCreateResponse,
  NonConformanceDto,
  TicketPriorityMapping,
  TicketTypeMapping
} from './bulk-create-non-cpnformance.model';

@Component({
  selector: 'sfl-bulk-create-non-conformance-tickets',
  templateUrl: './bulk-create-non-conformance-tickets.component.html',
  styleUrls: ['./bulk-create-non-conformance-tickets.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class BulkCreateNonConformanceTicketsComponent implements OnInit {
  public onClose: Subject<any>;
  @Input() nonConformances: NonConformanceDto[];
  @Input() workOrderId: number;
  @Input() reportId: string;
  @Input() assessmentId: number;
  @Input() assessmentType: string;
  @Input() frequencyType: string;
  @Input() actionsList;
  @Input() issueList;
  subscription: Subscription = new Subscription();
  @ViewChild('cancelConfirmModelTemplate', { static: false }) cancelConfirmModelTemplate: TemplateRef<any>;
  isCreated = false;
  loading = false;
  priorityList = TicketPriorityMapping;
  ticketTypeList = TicketTypeMapping;
  selectedBulkNC = [];
  isMasterSel = false;
  isNCItemUpdated = false;
  modalRef: BsModalRef;
  isSelectAllAllowed = false;
  constructor(
    public _bsModalRef: BsModalRef,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    public options: ModalOptions,
    private readonly router: Router,
    private readonly reportService: ReportService
  ) {}

  ngOnInit(): void {
    console.log(this.assessmentType, 'this.assessmentType');

    this.onClose = new Subject();
    this.isSelectAllAllowed = this.nonConformances.every(n => n.ticketNumber);
    if (this.assessmentType === 'SV') {
      this.addIssueAndActionListOnEachNCItem();
    }
  }

  public onCancel(template: TemplateRef<any>): void {
    this._bsModalRef.setClass('d-none');
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-md'
    };
    setTimeout(() => {
      this.modalRef = this.modalService.show(template, ngModalOptions);
    }, 0);
  }

  closeModel() {
    this.modalRef.hide();
    // navigate to Work oder
    this.router.navigate(['/entities/workorders/add'], {
      queryParams: {
        id: this.assessmentId,
        assementType: this.assessmentType,
        frequencyType: this.frequencyType
      }
    });
  }

  selectDeselectAll() {
    const selectedIds = new Set(this.selectedBulkNC.map(items => items.ncGuid));

    this.nonConformances.forEach(nc => {
      if (!nc.ticketNumber) {
        nc.isSelected = this.isMasterSel;
      }

      if (this.isMasterSel) {
        if (!selectedIds.has(nc.ncGuid)) {
          this.selectedBulkNC.push(nc);
        }
      } else {
        this.selectedBulkNC = this.selectedBulkNC.filter(item => item.ncGuid !== nc.ncGuid && !item.ticketNumber);
      }
    });
  }

  singleNCCheckChanged(ncItem) {
    if (ncItem.isSelected) {
      if (!this.selectedBulkNC.some(items => items.ncGuid === ncItem.ncGuid)) {
        this.selectedBulkNC.push(ncItem);
      }
    } else {
      this.selectedBulkNC = this.selectedBulkNC.filter(items => items.ncGuid !== ncItem.ncGuid);
    }

    this.isMasterSel = this.nonConformances.every(ncItem => ncItem.isSelected);
  }

  onCreateBulkTickets() {
    this.loading = true;
    const params = {
      workOrderId: this.workOrderId,
      reportId: this.reportId,
      ncData: this.selectedBulkNC
        .filter(item => !item.ticketNumber)
        .map(item => ({
          ncGuid: item.ncGuid,
          order: item.order,
          component: item.component || 0,
          componentStr: item.componentStr,
          issue: item.issue === NC_ACTION_ISSUE_TYPE.OTHER ? item.otherIssue : item.issue,
          location: item.location,
          actions: item.actions === NC_ACTION_ISSUE_TYPE.OTHER ? item.otherAction : item.actions,
          priority: item.priority,
          ticketType: item.ticketType,
          isNCItemUpdated: this.isNCItemUpdated,
          isLiftRequired: item.isLiftRequired,
          specialTools: item.specialTools,
          estimatedHours: item.estimatedHours,
          materialsEstimate: item.materialsEstimate,
          numberOfPeopleRequired: item.numberOfPeopleRequired
        }))
    };
    this.subscription.add(
      this.reportService.createNCBulkTicket(params).subscribe({
        next: (res: BulkNCTicketCreateResponse) => {
          if (res && res.status === 1) {
            this.nonConformances.forEach(element => {
              const match = res.resultTicket.find(a1 => a1.ncGuid === element.ncGuid);
              if (match) {
                element.ticketNumber = match.ticketNumber;
                element.otherIssue = element.issue;
                element.otherAction = element.actions;
              } else {
                element.ticketNumber = element.ticketNumber ? element.ticketNumber : '';
                element.otherIssue = element.issue;
                element.otherAction = element.actions;
              }
            });
            this.alertService.showSuccessToast(res.message);
            this.isCreated = true;
          } else {
            this.alertService.showErrorToast(res.message);
          }
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  addIssueAndActionListOnEachNCItem() {
    this.nonConformances = this.nonConformances.map(ncItem => {
      const clonedNcItem = JSON.parse(JSON.stringify(ncItem));
      const issueList = this.getIssueNcItemBasedOnComponent(clonedNcItem);
      const actionList = this.getActionListBasedOnIssue(clonedNcItem);

      return {
        ...clonedNcItem,
        issueList: issueList,
        actionsList: actionList,
        otherIssue: clonedNcItem.issue,
        otherAction: clonedNcItem.actions,
        actualActionText: clonedNcItem.actions,
        actualIssueText: clonedNcItem.issue,
        issue: this.setIssueForEachNCItem(clonedNcItem, issueList),
        actions: this.setActionForEachNCItem(clonedNcItem, actionList)
      };
    });
  }

  setIssueForEachNCItem(ncItem, issueList) {
    return issueList.find(item => item.issueObservation === ncItem.issue)?.issueObservation || NC_ACTION_ISSUE_TYPE.OTHER;
  }

  setActionForEachNCItem(ncItem, actionList) {
    return actionList.find(item => item.issueObservationStr === ncItem.issue)?.actionRecommendation || NC_ACTION_ISSUE_TYPE.OTHER;
  }

  getIssueNcItemBasedOnComponent(ncItem) {
    const issueBaseOnComponent = this.issueList.filter(item => item.componentId === ncItem.component);
    return [
      ...issueBaseOnComponent,
      {
        issueObservation: NC_ACTION_ISSUE_TYPE.OTHER,
        id: 0,
        componentId: 0,
        isActive: true,
        componentStr: ''
      }
    ];
  }

  getActionListBasedOnIssue(ncItem) {
    const actionBaseOnIssue = this.actionsList.filter(item => item.issueObservationStr === ncItem.issue);
    return [
      ...actionBaseOnIssue,
      {
        id: 0,
        isActive: true,
        action: NC_ACTION_ISSUE_TYPE.OTHER,
        componentId: 0,
        issueObservationId: 0,
        componentStr: '',
        issueObservationStr: '',
        actionRecommendation: NC_ACTION_ISSUE_TYPE.OTHER
      }
    ];
  }

  updateActionListBasedOnIssue(ncItem) {
    const actionBaseOnIssue = this.actionsList.filter(item => item.issueObservationStr === ncItem.issue);
    this.nonConformances.forEach(nc => {
      if (nc.ncGuid === ncItem.ncGuid) {
        nc.actionsList = [
          ...actionBaseOnIssue,
          {
            id: 0,
            isActive: true,
            action: NC_ACTION_ISSUE_TYPE.OTHER,
            componentId: 0,
            issueObservationId: 0,
            componentStr: '',
            issueObservationStr: '',
            actionRecommendation: NC_ACTION_ISSUE_TYPE.OTHER
          }
        ];
      }
    });
  }
}
