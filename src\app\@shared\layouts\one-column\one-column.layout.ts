import { Component, HostListener, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'qesolar-one-column-layout',
  styleUrls: ['./one-column.layout.scss'],
  template: `
    <nb-layout center>
      <nb-layout-header id="header" *ngIf="showHeaderFooter"><qesolar-header></qesolar-header></nb-layout-header>

      <nb-layout-column id="main-container">
        <ng-content select="router-outlet"></ng-content>
      </nb-layout-column>

      <nb-layout-footer id="footer" *ngIf="showHeaderFooter"><qesolar-footer></qesolar-footer></nb-layout-footer>
    </nb-layout>
  `,
  encapsulation: ViewEncapsulation.None
})
export class OneColumnLayoutComponent implements OnInit {
  constructor(public readonly router: Router) {}

  ngOnInit(): void {
    if (!this.router.url.includes('fill-mobile-form')) {
      setTimeout(() => {
        this.setFixedContainer();
      }, 300);
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(e) {
    if (!this.router.url.includes('fill-mobile-form')) {
      setTimeout(() => {
        this.setFixedContainer();
      }, 500);
    }
  }

  get showHeaderFooter(): boolean {
    const hiddenRoutes = ['fill-mobile-form', 'shared-power-chart'];
    return !hiddenRoutes.some(route => this.router.url.includes(route));
  }

  setFixedContainer() {
    const headerHeight = document.getElementById('header');
    const footerHeight = document.getElementById('footer');
    const layoutContainer: any = document.getElementsByClassName('layout-container');
    layoutContainer[0].style.minHeight = `${window.innerHeight - headerHeight.offsetHeight}px`;
    layoutContainer[0].style.maxHeight = `${window.innerHeight - headerHeight.offsetHeight}px`;
    document.getElementById('main-container').style.minHeight = `${
      window.innerHeight - headerHeight.offsetHeight - footerHeight.offsetHeight - 10
    }px`;
    document.getElementById('main-container').style.maxHeight = `${
      window.innerHeight - headerHeight.offsetHeight - footerHeight.offsetHeight - 10
    }px`;
  }
}
