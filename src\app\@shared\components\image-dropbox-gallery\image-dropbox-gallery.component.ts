import { Component, EventEmitter, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription, forkJoin } from 'rxjs';
import { AppConstants } from '../../constants';
import { AlertService } from '../../services';
import { CommonService } from '../../services/common.service';
import { DropboxImageGalleryService } from '../../services/dropbox-image-gallery.service';
import { StorageService } from '../../services/storage.service';
import { ConfirmDialogComponent } from '../confirm-dialog/confirm-dialog.component';
import { CommonGalleryModalConfig, ImageList, TagListResponseModel } from './drop-box.model';
import { ROLE_TYPE } from '../../enums';
import { checkAuthorisations } from '../../utils';

@Component({
  selector: 'sfl-image-dropbox-gallery',
  templateUrl: './image-dropbox-gallery.component.html',
  styleUrls: ['./image-dropbox-gallery.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ImageDropboxGalleryComponent implements OnInit, OnDestroy {
  @Input() requestParamsConfig: CommonGalleryModalConfig = new CommonGalleryModalConfig();
  @Input() activityDate: string;
  @Input() isTruckRollGallery = false;
  subscription: Subscription = new Subscription();
  public isParentRefresh: EventEmitter<boolean> = new EventEmitter();
  dataHasBeenUpdated = false;
  previewExpandIcon = AppConstants.previewExpandIcon;
  loading = false;
  activeIndex: number = 0;
  totalCount: number = 0;
  isAllImagesSelected = false;
  dropBoxImageList: ImageList[] = [];
  dateFormat = AppConstants.fullDateFormat;
  // pageSize = 15;
  galleryFilterModel = {
    sortedConditionTags: [],
    sortedDeviceTags: []
  };
  appliedTags = {
    conditionalTags: [],
    deviceTags: []
  };
  filteredAppliedTags = {
    conditionalTags: [],
    deviceTags: [],
    singleImageConditionalTag: [],
    singleImageDeviceTag: [],
    sortedConditionTags: [],
    sortedDeviceTags: []
  };
  conditionalTagsList: TagListResponseModel[] = [];
  deviceTagsList: TagListResponseModel[] = [];
  @ViewChild('fileInput') fileInput;
  modalRef: BsModalRef;
  currentPage = 1;
  roleType = ROLE_TYPE;
  checkAuthorisationsFn = checkAuthorisations;

  constructor(
    public _bsModalRef: BsModalRef,
    private readonly alertService: AlertService,
    private readonly dropBoxService: DropboxImageGalleryService,
    private readonly commonService: CommonService,
    private readonly storageService: StorageService,
    private readonly modalService: BsModalService
  ) {}

  ngOnInit(): void {
    if (this.checkAuthorisationsFn([this.roleType.CUSTOMER])) {
      this.requestParamsConfig.isCustomerFacing = true;
    }
    this.activeIndex = this.requestParamsConfig.imagePreviewId;
    const apiArray = [this.dropBoxService.getDevicesTagList()];
    const tempObj = ['deviceTagsList'];
    apiArray.push(this.dropBoxService.getConditionalTagList());
    tempObj.push('conditionalTagsList');
    this.getAllLists(apiArray, tempObj);
  }

  // @HostListener('scroll', ['$event'])
  // onScroll(event: any) {
  //   const element = event.target;
  //   const scrollOffset = 2;
  //   if (element.scrollHeight - element.scrollTop <= element.clientHeight + scrollOffset) {
  //     if (this.dropBoxImageList.length !== this.totalCount) {
  //       this.loading = true;
  //       this.requestParamsConfig = {
  //         ...this.requestParamsConfig,
  //         itemsCount: 15,
  //         page: this.requestParamsConfig.page + 1
  //       };
  //       this.getGalleryImageListing(this.requestParamsConfig, true);
  //     }
  //   }
  // }

  // Pagesize Change
  onChangeSize() {
    this.dropBoxImageList = [];
    this.requestParamsConfig.page = 0;
    this.currentPage = 0;
    // this.requestParamsConfig.itemsCount = Number(this.pageSize);
    this.getGalleryImageListing(this.requestParamsConfig, false, false);
  }

  onPageChange(obj) {
    this.dropBoxImageList = [];
    this.currentPage = obj;
    this.requestParamsConfig.page = this.currentPage - 1;
    this.getGalleryImageListing(this.requestParamsConfig, false, false);
  }

  getAllLists(apiArray: any, mapResultList: string[]) {
    this.loading = true;
    forkJoin(apiArray).subscribe({
      next: (res: any) => {
        for (const [index, value] of mapResultList.entries()) {
          this[value] = res[index];
        }
        this.getGalleryImageListing(this.requestParamsConfig);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getGalleryImageListing(requestParams, isScrolled = false, isRemoveSelectAll = true) {
    this.subscription.add(
      this.dropBoxService.getGalleryImageFiles(requestParams, this.isTruckRollGallery).subscribe({
        next: res => {
          const galleryList: ImageList[] = res.fileGallery.map(item => {
            const conditionalTagTxt = item.conditionalTagTxt.filter(tagTxt => tagTxt !== 'Customer Facing');
            return {
              ...item,
              isSelectedForPreview: false,
              isPreviewImageEditTag: false,
              conditionalTagTxt: this.checkAuthorisationsFn([this.roleType.CUSTOMER]) ? conditionalTagTxt : item.conditionalTagTxt
            };
          });
          this.totalCount = res.totalCount;
          if (isScrolled) {
            this.dropBoxImageList.push(...galleryList);
          } else {
            this.dropBoxImageList = galleryList;
          }
          if (!galleryList.length) {
            this.requestParamsConfig.isCustomerFacing = false;
          }
          if (isRemoveSelectAll) {
            this.isAllImagesSelected = false;
            this.selectAllImages();
          } else {
            this.selectAllImages();
          }
          this.activeIndex = 0;
          this.loading = false;
        },
        error: err => {
          this.loading = false;
        }
      })
    );
  }

  expandImage(fileUrl) {
    window.open(fileUrl, '_blank');
  }

  isAnyImageSelected() {
    for (let obj of this.dropBoxImageList) {
      if (obj.hasOwnProperty('isSelectedForPreview') && obj.isSelectedForPreview === true) {
        return true;
      }
    }
    return false;
  }

  uploadImagesToGallery(event: any) {
    const files: FileList = event.target.files;
    if (files.length === 0) {
      this.fileInput.nativeElement.value = '';
      return;
    }
    const formData: FormData = new FormData();
    const allowedImageExtensions = AppConstants.allowedDropboxImages;
    for (let i = 0; i < files.length; i++) {
      const file: File = files[i];
      // Check if the file is an image
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!allowedImageExtensions.includes(fileExtension)) {
        this.alertService.showErrorToast(`Unsupported file type`);
        continue;
      }
      // Add file to FormData object
      formData.append('files', file);
    }

    if (formData.getAll('files').length === 0) {
      return;
    }
    this.loading = true;
    formData.append('customerId', `${this.requestParamsConfig.customerId}`);
    formData.append('id', '0');
    formData.append('portfolioId', `${this.requestParamsConfig.portfolioId}`);
    formData.append('siteId', `${this.requestParamsConfig.siteId}`);
    formData.append('entityId', `${this.requestParamsConfig.entityId}`);
    formData.append('entityNumber', `${this.requestParamsConfig.entityNumber}`);
    formData.append('moduleType', `${this.requestParamsConfig.moduleType}`);
    formData.append('parentId', `${this.requestParamsConfig.parentId ? this.requestParamsConfig.parentId : null}`);
    formData.append('fileTagIds', null);
    formData.append('notes', '');
    formData.append('fileType', `image`);
    this.subscription.add(
      this.dropBoxService.uploadFilesToGallery(formData).subscribe({
        next: res => {
          this.requestParamsConfig.page = 0;
          const requestParams = {
            ...this.requestParamsConfig,
            itemsCount: 15,
            page: 0
          };
          this.dropBoxImageList = [];
          this.activeIndex = 0;
          this.getGalleryImageListing(requestParams);
          this.alertService.showSuccessToast(`${files.length > 1 ? 'Images' : 'Image'} uploaded successfully.`);
          this.fileInput.nativeElement.value = '';
          this.dataHasBeenUpdated = true;
          this.loading = false;
        },
        error: e => {
          this.alertService.showWarningToast('Fail to upload images.');
          this.loading = false;
        }
      })
    );
  }

  addRemoveMultipleImagesTags(isApplyTags: boolean) {
    const filesIds: number[] = this.dropBoxImageList.filter(item => item.isSelectedForPreview === true).map(item => item.id);
    const multipleImagesTags = {
      filesIds: filesIds,
      conditionalTagIds: this.appliedTags.conditionalTags,
      deviceTagIds: this.appliedTags.deviceTags,
      filesId: 0,
      isApplyTags: isApplyTags
    };
    if (filesIds && filesIds.length) {
      this.addRemoveTags(multipleImagesTags, true);
    } else {
      this.alertService.showWarningToast(`Please select at least one image.`);
    }
  }

  addRemovePreviewImagesTags(id: number, cTags: number[], dTags: number[], isApplyTags: boolean) {
    const previewImagesTags = {
      filesIds: [],
      conditionalTagIds: cTags,
      deviceTagIds: dTags,
      filesId: id,
      isApplyTags: isApplyTags
    };
    if ((cTags && cTags.length) || (dTags && dTags.length)) {
      this.addRemoveTags(previewImagesTags);
    } else {
      this.alertService.showWarningToast(`Please select at least one tag.`);
    }
  }

  addRemoveTags(imageTagsParams, forMultiple = false) {
    this.loading = true;
    this.subscription.add(
      this.dropBoxService.applyTagsToImages(imageTagsParams).subscribe({
        next: res => {
          this.requestParamsConfig.page = 0;
          const requestParams = {
            ...this.requestParamsConfig,
            itemsCount: 15,
            page: 0
          };
          this.dropBoxImageList = [];
          this.activeIndex = 0;
          if (forMultiple) {
            this.appliedTags = {
              conditionalTags: [],
              deviceTags: []
            };
          }
          this.getGalleryImageListing(requestParams);
        },
        error: err => {
          this.loading = false;
        }
      })
    );
  }

  copyImageLink(imageLink) {
    const inputElement = document.createElement('input');
    inputElement.value = imageLink;
    document.body.appendChild(inputElement);
    inputElement.select();
    document.execCommand('copy');
    document.body.removeChild(inputElement);
  }

  downloadPreviewFile(imageId, fileName) {
    this.loading = true;
    this.dropBoxService.downloadPreviewedImage(imageId).subscribe({
      next: data => {
        if (data) {
          const link = this.commonService.createObject(data, data.type);
          link.download = fileName;
          link.click();
          this.loading = false;
        } else {
          this.loading = false;
        }
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  downloadAsFolder() {
    this.loading = true;
    if (this.isAllImagesSelected) {
      const filterSortingParam = {
        ...this.requestParamsConfig,
        itemsCount: 15,
        page: 0,
        conditionalTagIds: this.galleryFilterModel.sortedConditionTags,
        deviceTagIds: this.galleryFilterModel.sortedDeviceTags
      };
      this.dropBoxService.downloadAsAllGalleryImages(filterSortingParam).subscribe({
        next: data => {
          if (data) {
            const ngModalOptions: ModalOptions = {
              backdrop: 'static',
              keyboard: false,
              animated: true,
              initialState: {
                message: `${data.message}`,
                confirmBtnText: 'Okay',
                showCancelButton: false
              }
            };
            this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
            this.loading = false;
            this.isAllImagesSelected = false;
            this.selectAllImages();
          } else {
            this.loading = false;
          }
        },
        error: e => {
          this.loading = false;
        }
      });
    } else {
      const filesIds: number[] = this.dropBoxImageList.filter(item => item.isSelectedForPreview === true).map(item => item.id);
      const params = {
        entityId: this.requestParamsConfig.entityId,
        filesIds: filesIds
      };
      this.dropBoxService.downloadAsFolder(params).subscribe({
        next: data => {
          if (data) {
            const link = this.commonService.createObject(data, data.type);
            link.download = `${this.requestParamsConfig.entityNumber}-ImageGallery.zip`;
            link.click();
            this.loading = false;
          } else {
            this.loading = false;
          }
        },
        error: e => {
          this.loading = false;
        }
      });
    }
  }

  deleteSelectedImages() {
    const filesIds: number[] = this.dropBoxImageList.filter(item => item.isSelectedForPreview === true).map(item => item.id);
    if (filesIds && filesIds.length) {
      const ngModalOptions: ModalOptions = {
        backdrop: 'static',
        keyboard: false,
        animated: true,
        initialState: {
          message: 'Are you sure you want to delete?'
        }
      };
      this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
      this.modalRef.content.onClose.subscribe(result => {
        if (result) {
          const param = {
            fileIds: filesIds
          };
          this.deleteImages(filesIds, param);
        }
      });
    } else {
      this.alertService.showWarningToast(`Please select at least one image.`);
    }
  }

  deleteImages(filesIds, params) {
    this.dropBoxService.deleteMultipleImageGalleryFiles(params).subscribe({
      next: data => {
        this.alertService.showSuccessToast(`Selected ${filesIds.length > 1 ? 'images' : 'image'} deleted Successfully.`);
        this.requestParamsConfig.page = 0;
        const requestParams = {
          ...this.requestParamsConfig,
          itemsCount: 15,
          page: 0
        };
        this.dataHasBeenUpdated = true;
        this.dropBoxImageList = [];
        this.activeIndex = 0;
        this.getGalleryImageListing(requestParams);
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  getActiveIndexChanges(currentPreviewIndex) {
    this.activeIndex = currentPreviewIndex;
  }

  next() {
    if (this.activeIndex === this.dropBoxImageList.length - 1) {
      return;
    } else {
      this.activeIndex++;
    }
  }

  prev() {
    if (this.activeIndex === 0) {
      return;
    } else {
      this.activeIndex--;
    }
  }

  selectAllImages() {
    for (const element of this.dropBoxImageList) {
      element.isSelectedForPreview = this.isAllImagesSelected;
    }
  }

  getItAsCurrentPreview(asCurrentPreviewIndex: number) {
    this.activeIndex = asCurrentPreviewIndex;
  }

  isPlPlusUser(): boolean {
    return this.checkAuthorisationsFn([
      this.roleType.PORTFOLIOMANAGER,
      this.roleType.ADMIN,
      this.roleType.DIRECTOR,
      this.roleType.MANAGER,
      this.roleType.SUPPORT
    ]);
  }

  singleImageCheckChange(event) {
    if (event === false && this.isAllImagesSelected) {
      this.isAllImagesSelected = false;
    }
  }

  onFilter(event: any, filterListType: string) {
    if (event.term) {
      this.filteredAppliedTags[filterListType] = event.items?.map(element => element.id);
    } else {
      this.filteredAppliedTags[filterListType] = [];
    }
  }

  toggleSelectUnselectAllTags(tagType, tagsList, isSelect = false) {
    const appliedTags = this.appliedTags[tagType];
    const filteredAppliedTags = this.filteredAppliedTags[tagType];

    if (isSelect) {
      if (filteredAppliedTags.length) {
        this.appliedTags[tagType] = [...new Set([...appliedTags, ...JSON.parse(JSON.stringify(filteredAppliedTags))])];
      } else {
        this.appliedTags[tagType] = tagsList.map(tags => tags.id);
      }
    } else {
      if (filteredAppliedTags.length) {
        this.appliedTags[tagType] = appliedTags.filter(x => !filteredAppliedTags.includes(x));
      } else {
        this.appliedTags[tagType] = [];
      }
    }
  }

  toggleSingleImageSelectUnselectAllTags(currentTagItem, tagType, tagsList, isSelect = false) {
    const singleImageTagType = tagType === 'singleImageConditionalTag' ? 'conditionalTag' : 'deviceTag';
    const appliedTags = currentTagItem[singleImageTagType];
    const filteredAppliedTags = this.filteredAppliedTags[tagType];

    if (isSelect) {
      if (filteredAppliedTags.length) {
        this.dropBoxImageList[this.activeIndex][singleImageTagType] = [
          ...new Set([...appliedTags, ...JSON.parse(JSON.stringify(filteredAppliedTags))])
        ];
      } else {
        this.dropBoxImageList[this.activeIndex][singleImageTagType] = tagsList.map(tags => tags.id);
      }
    } else {
      if (filteredAppliedTags.length) {
        this.dropBoxImageList[this.activeIndex][singleImageTagType] = appliedTags.filter(x => !filteredAppliedTags.includes(x));
      } else {
        this.dropBoxImageList[this.activeIndex][singleImageTagType] = [];
      }
    }
  }

  toggleSelectUnselectSortedTags(tagType, tagsList, isSelect = false) {
    const sortedTags = this.galleryFilterModel[tagType];
    const filteredAppliedTags = this.filteredAppliedTags[tagType];

    if (isSelect) {
      if (filteredAppliedTags.length) {
        this.galleryFilterModel[tagType] = [...new Set([...sortedTags, ...JSON.parse(JSON.stringify(filteredAppliedTags))])];
      } else {
        this.galleryFilterModel[tagType] = tagsList.map(tags => tags.id);
      }
    } else {
      if (filteredAppliedTags.length) {
        this.galleryFilterModel[tagType] = sortedTags.filter(x => !filteredAppliedTags.includes(x));
      } else {
        this.galleryFilterModel[tagType] = [];
      }
    }
  }

  onTagSorting(isRemoveFilers) {
    if (isRemoveFilers) {
      this.galleryFilterModel.sortedConditionTags = [];
      this.galleryFilterModel.sortedDeviceTags = [];
      this.requestParamsConfig.isCustomerFacing = false;
    }
    this.loading = true;
    this.requestParamsConfig.page = 0;
    const filterSortingParam = {
      ...this.requestParamsConfig,
      itemsCount: 15,
      page: 0,
      conditionalTagIds: this.galleryFilterModel.sortedConditionTags,
      deviceTagIds: this.galleryFilterModel.sortedDeviceTags
    };
    this.dropBoxImageList = [];
    this.activeIndex = 0;
    this.getGalleryImageListing(filterSortingParam);
  }

  public onCancel(): void {
    this.isParentRefresh.emit(this.dataHasBeenUpdated);
    this._bsModalRef.hide();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
