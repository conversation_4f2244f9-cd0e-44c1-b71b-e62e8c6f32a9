/**
 * @sunflowerlab
 * <AUTHOR>
 * @contributors <PERSON><PERSON><PERSON><PERSON> shah
 */
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { AppConstants } from '../@shared/constants/app.constant';
import { ApiUrl } from '../@shared/constants/url.constant';
import { ChangePassword } from '../@shared/models/changePassword.model';
import { AppSettings, SecurityQuestionItem, User, UserAuthorisation } from '../@shared/models/user.model';
import { RefreshTokenService } from '../@shared/services';
import { StorageService } from '../@shared/services/storage.service';
import { LoginTokenResponse, UserAuthenticationReq, UserAuthenticationRes } from './models/user-authentication.model';
import * as uuid from 'uuid';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  constructor(
    private readonly http: HttpClient,
    private readonly storageService: StorageService,
    private readonly refreshTokenService: RefreshTokenService
  ) {}

  login(credentials): Observable<any> {
    const userAuthenticationReq: UserAuthenticationReq = {
      email: credentials.email,
      password: credentials.password,
      mfaCode: credentials.mfaCode,
      questionID: credentials.questionID,
      question: credentials.question,
      questionAnswer: credentials.questionAnswer,
      rememberMe: credentials.rememberMe,
      isFromCustomerGatewayAPI: credentials.isFromCustomerGatewayAPI,
      hideInputValues: credentials.hideInputValues,
      sessionId: uuid.v4()
    };
    delete userAuthenticationReq.hideInputValues;
    return this.http.post(ApiUrl.login, userAuthenticationReq).pipe(map(this.authenticateSuccess.bind(this)));
  }

  authenticateSuccess(resp: UserAuthenticationRes): UserAuthenticationRes {
    const loginResp = new UserAuthenticationRes(resp);
    const bearerToken = loginResp.id_Token;
    const refreshToken = loginResp.refresh_Token;
    const isMFAUserLoggedIn = loginResp.isMFAUserLoggedIn;
    const qeAnalyticsSessionIdKey = loginResp.sessionId;
    this.storageService.set(AppConstants.isForcedToChangePasswordKey, resp.isForcedToChangePassword);
    if (qeAnalyticsSessionIdKey && bearerToken && refreshToken && loginResp.isAuthenticated) {
      this.storageService.set(AppConstants.qeAnalyticsSessionIdKey, qeAnalyticsSessionIdKey);
      const jwtUserTokens = new LoginTokenResponse(bearerToken, refreshToken, isMFAUserLoggedIn);
      this.refreshTokenService.storeAuthTokens(jwtUserTokens);
      this.refreshTokenService.startRefreshTokenTimer(bearerToken, refreshToken);
    }
    return loginResp;
  }

  forgotPassword(obj): Observable<any> {
    return this.http.post(ApiUrl.FORGOT_PASSWORD, obj);
  }

  resetPassword(dto: ChangePassword): Observable<any> {
    return this.http.post(ApiUrl.RESET_PASSWORD, dto);
  }

  checkResetPasswordTokenExpire(dto: ChangePassword): Observable<any> {
    return this.http.post(ApiUrl.CHECK_RESET_PASSWORD_TOKEN, dto);
  }

  logout(): Observable<any> {
    return new Observable(observer => {
      this.refreshTokenService.stopRefreshTokenTimer();
      this.refreshTokenService.ngOnDestroy();
      this.storageService.clear(AppConstants.authenticationToken);
      this.storageService.clear(AppConstants.refreshToken);
      this.storageService.clear(AppConstants.isMFAUserLoggedIn);
      this.storageService.clear(AppConstants.qeAnalyticsSessionIdKey);
      observer.complete();
    });
  }

  setAuthorisationIds(response: User): User {
    const userAuthorisation = new UserAuthorisation(response.authorities);
    this.storageService.set(AppConstants.userAuthorisations, userAuthorisation);
    return response;
  }

  getAccount(): Observable<User> {
    return this.http.get<User>(ApiUrl.account).pipe(map(response => this.setAuthorisationIds(response)));
  }

  getVersAccount(): Observable<AppSettings> {
    return this.http.get<AppSettings>(ApiUrl.VERSION_API);
  }

  getSecurityQuestionList(): Observable<SecurityQuestionItem[]> {
    return this.http.get<SecurityQuestionItem[]>(ApiUrl.GET_ALL_SECURITY_QUESTIONS);
  }

  clearCustomerCache(): Observable<any> {
    return this.http.put(`${ApiUrl.CLEAR_CUSTOMER_CACHE}`, {});
  }
}
