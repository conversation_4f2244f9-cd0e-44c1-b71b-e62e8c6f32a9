import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  PICKABLE_KEYS_FOR_SHARED_CPS_DATA,
  SharedCPSData,
  SharedCPSDataForKeys,
  SharedCPSDataKeyMappingToCommonFilterKey
} from '../models/shared-cps-data.model';
import { CommonFilter } from '../components/filter/common-filter.model';
import {
  FIELD_VALIDATION_TYPE,
  JUMP_TO_MENU_FIELD_VALIDATIONS,
  JUMP_TO_MENU_OPTIONAL_FIELDS,
  JUMP_TO_MENU_REQUIRED_FIELDS,
  QE_MENU_ENUMS
} from '../constants';
import { QEMenuConfigs } from '../models/header-menu.model';

@Injectable({
  providedIn: 'root'
})
export class SharedCPSDataService {
  private disableJumpToMenu$ = new BehaviorSubject<boolean>(false);
  private sharedCPSData = new BehaviorSubject<SharedCPSData>(new SharedCPSData({}));
  private clonedSharedCPSData = new BehaviorSubject<SharedCPSData>(new SharedCPSData({}));

  constructor() {}

  setSharedCPSData(data: SharedCPSData) {
    const sharedCPSData = new SharedCPSData(data);
    this.sharedCPSData.next(sharedCPSData);
  }

  getSharedCPSData() {
    return this.sharedCPSData.getValue();
  }

  setClonedSharedCPSData(data: SharedCPSData) {
    const clonedSharedCPSData = new SharedCPSData(data);
    this.clonedSharedCPSData.next(clonedSharedCPSData);
  }

  getClonedSharedCPSData() {
    return this.clonedSharedCPSData.getValue();
  }

  setClonedSharedCPSDataAndResetSharedCPSData(): void {
    const sharedCPSData = JSON.parse(JSON.stringify(this.getSharedCPSData()));
    this.setClonedSharedCPSData(sharedCPSData);
    this.resetSharedCPSData();
  }

  getSharedCPSDataForCommonFilter(isSingleSelect = false, isCloned = true): Partial<CommonFilter> {
    const sharedCPSData = JSON.parse(JSON.stringify(isCloned ? this.getClonedSharedCPSData() : this.getSharedCPSData()));
    const result: Partial<CommonFilter> = {};

    Object.entries(SharedCPSDataKeyMappingToCommonFilterKey).forEach(([singularKey, pluralKey]) => {
      if (isSingleSelect) {
        const value = sharedCPSData[singularKey] ?? sharedCPSData[pluralKey];
        result[singularKey] = Array.isArray(value) ? value[0] ?? null : value ?? null;
      } else {
        const value = sharedCPSData[pluralKey] ?? sharedCPSData[singularKey];
        result[pluralKey] = Array.isArray(value) ? value : value != null ? [value] : [];
      }
    });

    return result;
  }

  getSharedCPSDataForCommonFilterWithResetData(isSingleSelect = false, isCloned = true): Partial<CommonFilter> {
    const sharedCPSData = JSON.parse(JSON.stringify(this.getSharedCPSDataForCommonFilter(isSingleSelect, isCloned)));
    this.resetCloneSharedCPSData();

    const removeEmptyKeys = (obj: Record<string, any>): Record<string, any> => {
      return Object.fromEntries(
        Object.entries(obj).filter(([_, value]) => {
          if (Array.isArray(value)) return value.length > 0;
          if (typeof value === 'string') return value.trim() !== '';
          return value !== null && value !== undefined;
        })
      );
    };
    const sharedCPSCleanedData = removeEmptyKeys(sharedCPSData);
    return Object.keys(sharedCPSCleanedData).length ? sharedCPSCleanedData : null;
  }

  pickSharedCPSData<T extends Record<string, any>, K extends keyof T>(
    obj: T,
    keys: K[] = SharedCPSDataForKeys[PICKABLE_KEYS_FOR_SHARED_CPS_DATA.RESPONSE_DATA] as K[]
  ): Pick<T, K> {
    return keys.reduce((acc, key) => {
      acc[key] = obj[key];
      return acc;
    }, {} as Pick<T, K>);
  }

  checkRequiredCPSFieldsForJumpToMenu(qeMenuEnum: QE_MENU_ENUMS): boolean {
    const sharedCPSData: SharedCPSData | Record<string, any> = this.getSharedCPSData();
    const requiredFieldsForJumpToMenu = JUMP_TO_MENU_REQUIRED_FIELDS[qeMenuEnum] || [];
    const optionalFieldsForJumpToMenu = JUMP_TO_MENU_OPTIONAL_FIELDS[qeMenuEnum] || [];
    const fieldValidationsForJumpToMenu = JUMP_TO_MENU_FIELD_VALIDATIONS[qeMenuEnum] || {};

    const getFieldValueStatus = (field: string): boolean => {
      const value = sharedCPSData?.[field];
      const minValidation = fieldValidationsForJumpToMenu?.[field]?.[FIELD_VALIDATION_TYPE.MIN];
      const maxValidation = fieldValidationsForJumpToMenu?.[field]?.[FIELD_VALIDATION_TYPE.MAX];
      const exactValidation = fieldValidationsForJumpToMenu?.[field]?.[FIELD_VALIDATION_TYPE.EXACT];

      const isEmpty = value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0);

      if (isEmpty) return !isEmpty;

      const valueLength = Array.isArray(value) || typeof value === 'string' ? value?.length : undefined;

      if (valueLength !== undefined) {
        if (minValidation && minValidation !== undefined && valueLength < minValidation) return false;
        if (maxValidation && maxValidation !== undefined && valueLength > maxValidation) return false;
        if (exactValidation && exactValidation !== undefined && exactValidation !== valueLength) return false;
      }

      return true;
    };

    const missingRequiredFields = requiredFieldsForJumpToMenu.filter(field => !getFieldValueStatus(field));
    const allRequiredPresent = missingRequiredFields.length === 0;

    const atLeastOneOptionalPresent =
      optionalFieldsForJumpToMenu.length === 0 || optionalFieldsForJumpToMenu.some(field => getFieldValueStatus(field));

    return allRequiredPresent && atLeastOneOptionalPresent;
  }

  changeSharedCPSDataForJumpToMenuDisableStatus(qeMenuEnum: QE_MENU_ENUMS): void {
    const isDisableJumpToMenu = !this.checkRequiredCPSFieldsForJumpToMenu(qeMenuEnum);
    this.disableJumpToMenu$.next(isDisableJumpToMenu);
  }

  checkDisableJumpToMenu(): Observable<boolean> {
    return this.disableJumpToMenu$.asObservable();
  }

  setAndExtractSharedCPSData<T, K extends keyof T>(
    qeMenuConfigs: QEMenuConfigs,
    extrabledata: T,
    pickableKeysForSharedCPSData: PICKABLE_KEYS_FOR_SHARED_CPS_DATA = PICKABLE_KEYS_FOR_SHARED_CPS_DATA.RESPONSE_DATA
  ): void {
    const pickableKeys = SharedCPSDataForKeys[pickableKeysForSharedCPSData] as K[];
    const pickSharedCPSData = this.pickSharedCPSData(extrabledata, pickableKeys);
    this.setSharedCPSData(new SharedCPSData(pickSharedCPSData));
    this.changeSharedCPSDataForJumpToMenuDisableStatus(qeMenuConfigs.qeMenuEnum);
  }

  resetSharedCPSData(): void {
    this.setSharedCPSData(new SharedCPSData({}));
  }

  resetCloneSharedCPSData(): void {
    this.setClonedSharedCPSData(new SharedCPSData({}));
  }

  resetSharedCPSDataAndCloneSharedCPSData(): void {
    this.resetSharedCPSData();
    this.resetCloneSharedCPSData();
  }
}
