import { Component, Input, OnInit } from '@angular/core';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { BsModalRef, ModalOptions } from 'ngx-bootstrap/modal';
import { ImageCroppedEvent } from 'ngx-image-cropper';
import { Subject } from 'rxjs';
import { ImageMimeType, ImageTransform, ImageTransformFormat, ImageTransformMimeType } from './image-transform.interface';

@Component({
  selector: 'sfl-image-cropper',
  templateUrl: './image-cropper.component.html',
  styleUrls: ['./image-cropper.component.scss']
})
export class ImageCropperComponent implements OnInit {
  public onClose: Subject<any>;
  @Input() imageFile: any;
  @Input() imageFiles: any;
  croppedImage: any = '';
  selectedImage: any;
  currentProcessingIndex: any = 0;
  finalImageList: any = [];
  imageStringFile = [];
  cropImageStringFile = [];
  scale = 1;
  transform: ImageTransform = {};
  canvasRotation = 0;
  rotation = 0;
  selectedImageFormat = 'png';
  loading = false;
  isSubmitted = false;
  private filesToProcess = 0;
  private filesProcessed = 0;

  constructor(public _bsModalRef: BsModalRef, public options: ModalOptions, private readonly sanitizer: DomSanitizer) {}

  public ngOnInit(): void {
    this.onClose = new Subject();
    if (this.imageFile) {
      this.setSelectedImageFormat(true, this.imageFile);
    }
    if (this.imageFiles.length) {
      for (const i of this.imageFiles) {
        this.onUploadChange(i);
      }
    } else {
      this.onConfirm();
    }
  }

  onUploadChange(selectedFile: any): void {
    this.loading = true;
    this.filesToProcess++;
    const file = selectedFile;
    if (file) {
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => this.handleReaderLoaded(e, file);
      reader.readAsBinaryString(file);
    }
  }

  handleReaderLoaded(e: ProgressEvent<FileReader>, file: File): void {
    const base64Initials = `data:${ImageTransformMimeType[file.type] || ImageTransformMimeType[ImageMimeType.JPEG]};base64,`;
    this.imageStringFile.push(base64Initials + btoa((e.target as FileReader).result as string));
    if (this.imageStringFile.length && !this.selectedImage) {
      this.setSelectedImageFormat(true, file);
      this.selectedImage = this.imageStringFile[0];
      this.currentProcessingIndex = 0;
    }
    this.cropImageStringFile = JSON.parse(JSON.stringify(this.imageStringFile));
    this.filesProcessed++;
    if (this.filesProcessed === this.filesToProcess) {
      this.loading = false;
    }
  }

  public onConfirm(isSubmitted = false): void {
    if (!this.isSubmitted) {
      this.isSubmitted = isSubmitted;
      if (this.imageFiles.length > 1) {
        this.cropImageStringFile[this.currentProcessingIndex] = this.croppedImage;
        this.onClose.next(this.cropImageStringFile);
      } else {
        this.onClose.next(this.croppedImage);
      }
      this._bsModalRef.hide();
    }
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  imageCropped(event: ImageCroppedEvent) {
    this.croppedImage = event.base64;
  }

  openThumbnail(item, i) {
    this.setSelectedImageFormat(false, item);
    this.selectedImage = item;
    this.canvasRotation = 0;
    this.currentProcessingIndex = i;
  }

  onCropAndNext() {
    this.cropImageStringFile[this.currentProcessingIndex] = this.croppedImage;
    this.currentProcessingIndex++;
    if (this.imageStringFile[this.currentProcessingIndex]) {
      this.setSelectedImageFormat(false, this.imageStringFile[this.currentProcessingIndex]);
      this.selectedImage = this.imageStringFile[this.currentProcessingIndex];
    } else {
      this.currentProcessingIndex = 0;
      this.setSelectedImageFormat(false, this.imageStringFile[0]);
      this.selectedImage = this.imageStringFile[0];
    }
    if (this.canvasRotation === -1 || this.canvasRotation === +1) {
      this.canvasRotation = 0;
    }
  }

  zoomOut() {
    this.scale -= 0.1;
    this.transform = {
      ...this.transform,
      scale: this.scale
    };
  }

  zoomIn() {
    this.scale += 0.1;
    this.transform = {
      ...this.transform,
      scale: this.scale
    };
  }

  resetImage() {
    this.scale = 1;
    this.rotation = 0;
    this.canvasRotation = 0;
    this.transform = {};
  }

  rotateLeft() {
    this.canvasRotation--;
    this.flipAfterRotate();
  }

  rotateRight() {
    this.canvasRotation++;
    this.flipAfterRotate();
  }

  //   flipHorizontal() {
  //     this.transform = {
  //         ...this.transform,
  //         flipH: !this.transform.flipH
  //     };
  // }

  // flipVertical() {
  //     this.transform = {
  //         ...this.transform,
  //         flipV: !this.transform.flipV
  //     };
  //   }

  private flipAfterRotate() {
    const flippedH = this.transform.flipH;
    const flippedV = this.transform.flipV;
    this.transform = {
      ...this.transform,
      flipH: flippedV,
      flipV: flippedH
    };
  }

  sanitizeUrl(url: string): SafeUrl {
    return this.sanitizer.bypassSecurityTrustUrl(url);
  }

  private setSelectedImageFormat(isFile: boolean, fileOrBased64: File | string): void {
    if (isFile) {
      if (fileOrBased64 instanceof File) {
        this.selectedImageFormat = ImageTransformFormat[fileOrBased64.type] || ImageTransformFormat[ImageMimeType.JPEG];
      }
    } else {
      if (typeof fileOrBased64 === 'string') {
        const matches = fileOrBased64.match(/^data:(.*);base64,(.*)$/);
        this.selectedImageFormat = ImageTransformFormat[matches[1]] || ImageTransformFormat[ImageMimeType.JPEG];
      }
    }
  }
}
