export class RmaReportsDetail {
  public totalRecords: number;
  public listData: RmaReportsList[];
}
export class RmaReportsList {
  public rmaId: number;
  public ticketId: number;
  public ticketNumber: number;
  public siteDeviceId: number;
  public deviceName: string;
  public deviceMFG: string;
  public isReturnRequired: number;
  public rmaNumber: string;
  public isRMAComplete: boolean;
  public deviceSerialNumber: string;
  public returnTrackingNumber: string;
  public startDate: string;
  public completeDate: string;
  public isDeleted: boolean;
  public isActive: boolean;
  public duration: number;
  public customerPortfolio: string;
  public siteName: string;
  public customerName: string;
  public portfolioName: string;
  public tracking: string;
  public deviceAttachments: RmaReportAttachments[];
  public returnAttachments: RmaReportAttachments[];
}

export class RmaReportAttachments {
  public ticketId: number;
  public rmaId: number;
  public rmaDocId: number;
  public siteDeviceId: number;
  public documentUrl: string;
  public fileExtension: string;
  public fileName: number;
  public fileType: string;
  public isDeleted: boolean;
  public docType: number;
  public attachFile: File;
}

export const cmRMAReportPageFilterKeys = [
  'customerIds',
  'portfolioIds',
  'siteIds',
  'regionIds',
  'subregionIds',
  'rmaComplete',
  'rmaReturnRequired',
  'rmaTracking',
  'mfgs'
];
