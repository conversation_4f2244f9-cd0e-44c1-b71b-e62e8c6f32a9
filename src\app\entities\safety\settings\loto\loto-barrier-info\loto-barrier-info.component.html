<nb-card class="loto-barrier-info-spinner appSpinner" [nbSpinner]="lotoBarrierLoader" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h5>LOTO</h5>
        <div class="ms-auto d-flex">
          <button
            class="float-end ms-2"
            nbButton
            status="primary"
            size="medium"
            type="button"
            [disabled]="lotoBarrierLoader"
            (click)="onCreateUpdateLOTOBarrier()"
          >
            <span class="d-none d-lg-inline-block">Save</span>
            <i class="d-inline-block d-lg-none fa-solid fa-save"></i>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body class="dropdownOverlap">
    <form [formGroup]="lotoBarrierForm" aria-labelledby="title" autocomplete="off">
      <div class="form-group row mt-2">
        <div class="col-12 col-sm-6 col-md-3">
          <label class="label" for="input-risk">Risk<span class="ms-1 text-danger">*</span></label>
          <ng-select
            bindLabel="name"
            bindValue="id"
            formControlName="riskLevel"
            [items]="riskLevelList"
            notFoundText="Risk not found"
            placeholder="Select risk"
            [clearable]="false"
            appendTo="body"
          >
          </ng-select>
          <sfl-error-msg [control]="lotoBarrierForm?.controls?.riskLevel" fieldName="Risk"></sfl-error-msg>
        </div>
      </div>
      <div class="form-group row mt-2">
        <div class="col-12 col-sm-8 col-lg-5">
          <label class="label" for="input-control-barrier">Control Barrier</label>
          <input nbInput fullWidth id="input-control-barrier" class="form-control" fieldSize="large" formControlName="controlBarrier" />
          <sfl-error-msg [control]="lotoBarrierForm?.controls?.controlBarrier" fieldName="Control Barrier"></sfl-error-msg>
        </div>
      </div>
      <div class="form-group row mt-2">
        <div class="col-12 col-sm-8 col-lg-5">
          <label class="label" for="input-protective-barrier">Protective Barrier </label>
          <input
            nbInput
            fullWidth
            id="input-protective-barrier"
            class="form-control"
            fieldSize="large"
            formControlName="protectiveBarrier"
          />
          <sfl-error-msg [control]="lotoBarrierForm?.controls?.protectiveBarrier" fieldName="Protective Barrier"></sfl-error-msg>
        </div>
      </div>
      <div class="form-group row mt-2">
        <div class="col-12 col-sm-8 col-lg-5">
          <label class="label" for="input-support-barrier">Support Barrier </label>
          <input nbInput fullWidth id="input-support-barrier" class="form-control" fieldSize="large" formControlName="supportBarrier" />
          <sfl-error-msg [control]="lotoBarrierForm?.controls?.supportBarrier" fieldName="Support Barrier"></sfl-error-msg>
        </div>
      </div>
    </form>
  </nb-card-body>
</nb-card>
