<!-- Modal -->
<div class="modal-content" id="activitylogticket" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header modal-fixed-header">
    <div class="d-flex align-items-center w-100">
      <h6 *ngIf="!isEdit">Add Activity Log</h6>
      <h6 *ngIf="isEdit">Edit Activity Log</h6>
      <div class="ms-auto">
        <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
          <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
        </button>
      </div>
    </div>
  </div>
  <form
    name="activitylogForm"
    #activitylogForm="ngForm"
    aria-labelledby="title"
    autocomplete="off"
    (ngSubmit)="removeSelectedErrors(activitylogForm); activitylogForm?.form?.valid && onActivityLogTicket()"
  >
    <div class="modal-body">
      <div class="row">
        <div class="col-12">
          <label class="label" for="input-date">Activity Date<span class="ms-1 text-danger">*</span></label>
          <input
            nbInput
            name="date"
            #date="ngModel"
            [(ngModel)]="activityLogItem.date"
            (ngModelChange)="onActivityDateChange()"
            placeholder="Select Date"
            fullWidth
            [nbDatepicker]="activityDate"
            required
            readonly
            autocomplete="off"
          />
          <nb-datepicker #activityDate [min]="min"></nb-datepicker>
          <sfl-error-msg [control]="date" [isFormSubmitted]="activitylogForm?.submitted" fieldName="Activity Date"></sfl-error-msg>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-md-12">
          <label class="label">Activity Description<span class="ms-1 text-danger">*</span></label>
          <textarea
            nbInput
            fullWidth
            name="woDescription"
            id="input-woDescription"
            #description="ngModel"
            [(ngModel)]="activityLogItem.description"
            maxlength="5120"
            required
          >
          </textarea>
          <sfl-error-msg
            [control]="description"
            [isFormSubmitted]="activitylogForm?.submitted"
            fieldName="Activity Description"
          ></sfl-error-msg>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-4">
          <label class="label" for="input-truckRolls">Truck Roll<span class="ms-1 text-danger">*</span></label>
          <div>
            <nb-radio-group class="d-flex" name="truckRole" required #truckRoll="ngModel" [(ngModel)]="activityLogItem.truckRole">
              <nb-radio [value]="true">Yes</nb-radio>
              <nb-radio [value]="false">No</nb-radio>
            </nb-radio-group>
            <sfl-error-msg [control]="truckRoll" [isFormSubmitted]="activitylogForm?.submitted" fieldName="Truck Roll"></sfl-error-msg>
          </div>
        </div>

        <div class="col-4">
          <label class="label" for="input-truckRolls">Resolved<span class="ms-1 text-danger">*</span></label>
          <div>
            <nb-radio-group
              class="d-flex"
              (valueChange)="onChange($event)"
              name="isResolve"
              required
              #isResolve="ngModel"
              [(ngModel)]="activityLogItem.isResolve"
            >
              <nb-radio [value]="true">Yes</nb-radio>
              <nb-radio [value]="false">No</nb-radio>
            </nb-radio-group>
            <sfl-error-msg [control]="isResolve" [isFormSubmitted]="activitylogForm?.submitted" fieldName="Resolved"></sfl-error-msg>
          </div>
        </div>
        <div class="col-4">
          <label class="label" for="input-isADeviceOutage">Device Outage<span class="ms-1 text-danger">*</span></label>
          <div>
            <nb-radio-group
              class="d-flex"
              name="isADeviceOutage"
              [required]="ticketModel.isGADSReporting"
              [disabled]="!ticketModel.isGADSReporting"
              #isADeviceOutage="ngModel"
              [(ngModel)]="activityLogItem.isADeviceOutage"
              (valueChange)="onDeviceOutageChange($event)"
            >
              <nb-radio [value]="true">Yes</nb-radio>
              <nb-radio [value]="false">No</nb-radio>
            </nb-radio-group>
            <sfl-error-msg
              [control]="isADeviceOutage"
              [isFormSubmitted]="activitylogForm?.submitted"
              fieldName="Device Outage"
            ></sfl-error-msg>
          </div>
        </div>
      </div>
      <ng-container *ngIf="activityLogItem.isADeviceOutage">
        <div class="row mt-2">
          <div class="col-12">
            <nb-accordion class="device-outage-accordion mb-1">
              <nb-accordion-item [expanded]="true" class="border-bottom">
                <nb-accordion-item-header class="accordion_head">Device Outage</nb-accordion-item-header>
                <nb-accordion-item-body>
                  <ng-container *ngFor="let item of activityLogItem.ticketSiteDeviceOutage; let i = index">
                    <ng-container *ngIf="!item.isDeleted">
                      <div class="row mt-2 d-flex align-items-center">
                        <div class="col-11">
                          <label class="label" for="Device"
                            >Device <span class="ms-1 text-danger" *ngIf="activityLogItem.isADeviceOutage">*</span></label
                          >
                          <ng-select
                            id="{{ 'deviceId-' + i }}"
                            name="{{ 'deviceId-' + i }}"
                            [items]="deviceListForTicketActivity"
                            bindLabel="deviceName"
                            bindValue="deviceId"
                            [(ngModel)]="item.deviceId"
                            #deviceId="ngModel"
                            notFoundText="No Device Found"
                            placeholder="Select Device"
                            [required]="activityLogItem.isADeviceOutage"
                            [disabled]="!ticketModel.isGADSReporting"
                            [closeOnSelect]="true"
                            [clearable]="false"
                            (change)="onDeviceChange($event, item)"
                          >
                          </ng-select>
                          <div *ngIf="(deviceId.invalid && (deviceId.dirty || deviceId.touched)) || activitylogForm.submitted">
                            <div class="input-error f-s-13" *ngIf="deviceId.errors?.required">Device is required.</div>
                          </div>
                        </div>
                        <div class="col-1 ps-0 text-center">
                          <em
                            class="fa fa-trash text-danger px-2 pointer"
                            [ngClass]="{ 'cursor-blocked': activityLogItem.ticketSiteDeviceOutage.length === 1 }"
                            (click)="
                              activityLogItem.ticketSiteDeviceOutage.length !== 1 && deleteTicketSiteDeviceOutage(i, activitylogForm)
                            "
                          ></em>
                        </div>
                      </div>
                      <div class="row mt-2 d-flex align-items-center">
                        <div class="col-11">
                          <div class="row">
                            <div class="col-lg-6 mb-lg-0 mb-2">
                              <div class="mb-2 d-flex align-items-center">
                                <label class="label mb-0">
                                  Start Date & Time<span class="ms-1 text-danger" *ngIf="activityLogItem.isADeviceOutage">*</span></label
                                >
                                <ng-container *ngIf="item.deviceId && item.startDateTimeUTCDate && item.refTicketNumber">
                                  <div
                                    class="ps-1 d-flex"
                                    nbPopoverTrigger="hover"
                                    nbPopoverPlacement="bottom"
                                    [nbPopover]="startDateTimePopover"
                                  >
                                    <em class="fa fa-info-circle text-primary pointer f-s-12"></em>
                                  </div>
                                  <ng-template #startDateTimePopover>
                                    <div class="card p-2">
                                      <a [href]="env.baseUrl + '/entities/ticket/detail/view/' + item.refTicketNumber" target="_blank">{{
                                        item.refTicketNumber
                                      }}</a>
                                    </div>
                                  </ng-template>
                                </ng-container>
                              </div>
                              <input
                                nbInput
                                id="{{ 'startDateTime-' + i }}"
                                name="{{ 'startDateTime-' + i }}"
                                #startDateTime="ngModel"
                                [(ngModel)]="item.startDateTimeUTCDate"
                                (ngModelChange)="onActivityStartDateChange(item)"
                                placeholder="Select Start Date & Time"
                                fullWidth
                                [nbDatepicker]="activityDeviceStartDateTime"
                                readonly
                                autocomplete="off"
                                nbDateTimePickerValidator
                                [minDateTime]="item.minStartDateTimeUTCDate"
                                [maxDateTime]="item.maxStartDateTimeUTCDate"
                                [disabled]="!item.deviceId && !ticketModel.isGADSReporting"
                              />
                              <nb-date-timepicker
                                [min]="item.minStartDateTimeUTCDate"
                                [max]="item.maxStartDateTimeUTCDate"
                                #activityDeviceStartDateTime
                                [format]="dateTimeFormat"
                                twelveHoursFormat
                              ></nb-date-timepicker>
                            </div>
                            <div class="col-lg-6">
                              <div class="mb-2 d-flex align-items-center">
                                <label class="label mb-0"> End Date & Time</label>
                              </div>
                              <input
                                nbInput
                                id="{{ 'endDateTime-' + i }}"
                                name="{{ 'endDateTime-' + i }}"
                                #endDateTime="ngModel"
                                [(ngModel)]="item.endDateTimeUTCDate"
                                placeholder="Select End Date & Time"
                                fullWidth
                                readonly
                                autocomplete="off"
                                nbDateTimePickerValidator
                                [minDateTime]="item.minEndDateTimeUTCDate"
                                [maxDateTime]="item.maxEndDateTimeUTCDate"
                                [nbDatepicker]="activityDeviceEndDateTime"
                                [disabled]="!item.startDateTimeUTCDate && !item.deviceId && !ticketModel.isGADSReporting"
                              />
                              <nb-date-timepicker
                                #activityDeviceEndDateTime
                                [min]="nbMinEndDateTimeUTCDate(item.minEndDateTimeUTCDate)"
                                [max]="nbMaxEndDateTimeUTCDate(item.maxEndDateTimeUTCDate)"
                                [format]="dateTimeFormat"
                                twelveHoursFormat
                              ></nb-date-timepicker>
                              <div *ngIf="(endDateTime.invalid && (endDateTime.dirty || endDateTime.touched)) || activitylogForm.submitted">
                                <div
                                  class="input-error f-s-13"
                                  *ngIf="
                                    item.endDateTimeUTCDate &&
                                    (endDateTime.errors?.minDateTimeError || endDateTime.errors?.maxDateTimeError)
                                  "
                                >
                                  <ng-container *ngIf="endDateTime.errors?.minDateTimeError">
                                    End Date & Time should be greater or same as {{ item.minEndDateTimeUTCDate | date : dateTimeFormat }}.
                                  </ng-container>
                                  <ng-container *ngIf="endDateTime.errors?.maxDateTimeError">
                                    End Date & Time should be less or same as {{ item.maxEndDateTimeUTCDate | date : dateTimeFormat }}.
                                  </ng-container>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="row mt-2">
                        <div class="col-11">
                          <div class="row">
                            <div class="col">
                              <label class="label"> Override Losses (KW)</label>
                              <input
                                id="{{ 'overrideLosesKW-' + i }}"
                                name="{{ 'overrideLosesKW-' + i }}"
                                fullWidth
                                class="form-control"
                                #overrideLosesKW="ngModel"
                                [(ngModel)]="item.overrideLosesKW"
                                nbInput
                                [NeedDifferentValue]="item.acNameplateKW"
                                OnlyNumber
                                [disabled]="!item.deviceId && !ticketModel.isGADSReporting"
                              />
                              <div
                                *ngIf="
                                  (overrideLosesKW.invalid && (overrideLosesKW.dirty || overrideLosesKW.touched)) ||
                                  activitylogForm.submitted
                                "
                              >
                                <div class="input-error f-s-13" *ngIf="overrideLosesKW.errors?.needDifferentValue">
                                  Please enter different value from AC Nameplate.
                                </div>
                              </div>
                            </div>
                            <div class="col">
                              <label class="label">
                                Planned Downtime?<span class="ms-1 text-danger" *ngIf="activityLogItem.isADeviceOutage">*</span></label
                              >
                              <nb-radio-group
                                class="d-flex"
                                id="{{ 'isPlannedDowntime-' + i }}"
                                name="{{ 'isPlannedDowntime-' + i }}"
                                [required]="activityLogItem.isADeviceOutage && ticketModel.isGADSReporting"
                                #isPlannedDowntime="ngModel"
                                [(ngModel)]="item.isPlannedDowntime"
                                [disabled]="!item.deviceId && !ticketModel.isGADSReporting"
                              >
                                <nb-radio [value]="true">Yes</nb-radio>
                                <nb-radio [value]="false">No</nb-radio>
                              </nb-radio-group>
                              <div
                                *ngIf="
                                  (isPlannedDowntime.invalid && (isPlannedDowntime.dirty || isPlannedDowntime.touched)) ||
                                  activitylogForm.submitted
                                "
                              >
                                <div class="input-error f-s-13" *ngIf="isPlannedDowntime.errors?.required">
                                  Planned Downtime is required.
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="row mt-2">
                        <div class="col-11">
                          <label class="label">
                            Description<span class="ms-1 text-danger" *ngIf="activityLogItem.isADeviceOutage">*</span></label
                          >
                          <textarea
                            nbInput
                            fullWidth
                            id="{{ 'comments-' + i }}"
                            name="{{ 'comments-' + i }}"
                            id="input-comments"
                            #comments="ngModel"
                            [(ngModel)]="item.comments"
                            maxlength="1000"
                            [required]="activityLogItem.isADeviceOutage && ticketModel.isGADSReporting"
                            [disabled]="!item.deviceId && !ticketModel.isGADSReporting"
                          >
                          </textarea>
                          <div *ngIf="(comments.invalid && (comments.dirty || comments.touched)) || activitylogForm.submitted">
                            <div class="input-error f-s-13" *ngIf="comments.errors?.required">Description is required.</div>
                          </div>
                        </div>
                      </div>
                    </ng-container>
                  </ng-container>
                  <div class="row mt-2" *ngIf="ticketModel.isGADSReporting">
                    <div class="col-md-12">
                      <span class="text-primary pointerTicketNumberLink px-2 pointer" (click)="addDeviceOutage()">+ Add Device</span>
                    </div>
                  </div>
                </nb-accordion-item-body>
              </nb-accordion-item>
            </nb-accordion>
          </div>
        </div>
      </ng-container>

      <div class="row mt-2">
        <div class="col-12">
          <label class="label" for="TruckRollId"
            >Truck Roll ID <span class="ms-1 text-danger" *ngIf="activityLogItem.truckRole">*</span></label
          >
          <ng-select
            name="TruckRollId"
            [items]="truckRollDropDownList"
            bindLabel="truckRollNumber"
            bindValue="truckRollId"
            [(ngModel)]="activityLogItem.truckRollId"
            #TruckRollId="ngModel"
            (ngModelChange)="onTruckRoleIdChange($event)"
            notFoundText="No Truck Roll ID Found"
            placeholder="Select Truck Roll ID"
            [required]="activityLogItem.truckRole"
            [closeOnSelect]="true"
            [clearable]="false"
            [loading]="dropdownLoading['truckRoll']"
          >
          </ng-select>
          <sfl-error-msg [control]="TruckRollId" [isFormSubmitted]="activitylogForm?.submitted" fieldName="Truck Roll ID"></sfl-error-msg>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-12">
          <label class="label" for="input-Site">JHA </label>
          <ng-select
            name="JHA"
            [multiple]="true"
            [items]="jhaDetail"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="activityLogItem.jhaMapPost"
            #jhaArrays="ngModel"
            notFoundText="No JHA Found"
            placeholder="Select JHA"
            [closeOnSelect]="false"
            [clearable]="false"
            (search)="onFilter($event)"
            (close)="filteredJHAs = []"
            [loading]="dropdownLoading['jha']"
          >
            <ng-template ng-header-tmp *ngIf="jhaDetail && jhaDetail?.length">
              <button type="button" (click)="selectAndDeselectAll(true)" class="btn btn-sm btn-primary">Select all</button>
              <button type="button" (click)="selectAndDeselectAll(false)" class="btn btn-sm btn-primary ms-1">Unselect all</button>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input id="item-{{ index }}" type="checkbox" name="item-{{ index }}" [ngModel]="item$.selected" /> {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                <span class="ng-value-label">{{ item.name }}</span>
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value" *ngIf="items.length > 1">
                <span class="ng-value-label">+{{ items.length - 1 }} </span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="row mt-2 d-flex" *ngFor="let item of activityLogItem.ticketFieldTechDto; let i = index">
        <div class="col-12">
          <label class="label" for="TruckRollId"
            >Service
            <span class="ms-1 text-danger" *ngIf="(activityLogItem.truckRole || (item.id !== 0 && item.isDeleted)) && i === 0"
              >*</span
            ></label
          >
          <ng-select
            name="{{ 'services-' + i }}"
            [items]="servicesDropDownList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="item.operationServiceId"
            #services="ngModel"
            notFoundText="No Services Found"
            placeholder="Select Services"
            [required]="(activityLogItem.truckRole || (item.id !== 0 && item.isDeleted)) && i === 0"
            [closeOnSelect]="true"
            [clearable]="false"
            [loading]="dropdownLoading['services']"
          >
          </ng-select>
          <div *ngIf="(services.invalid && (services.dirty || services.touched)) || activitylogForm.submitted">
            <div class="input-error f-s-13" *ngIf="services.errors?.required">Service is required.</div>
          </div>
        </div>
        <div class="col-6">
          <label class="label" for="input-hours"
            >QE Tech<span
              class="ms-1 text-danger"
              *ngIf="
                (activityLogItem.truckRole && i === 0) ||
                (!activityLogItem.truckRole && item.operationServiceId) ||
                (activityLogItem.truckRole && item.operationServiceId)
              "
              >*</span
            ></label
          >
          <ng-select
            name="{{ 'fieldTechName-' + i }}"
            #fieldTechName="ngModel"
            [items]="fieldTechList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="item.userId"
            notFoundText="No QE Tech Found"
            placeholder="Select QE Tech"
            [clearable]="false"
            [closeOnSelect]="true"
            [required]="
              (activityLogItem.truckRole && i === 0) ||
              (!activityLogItem.truckRole && item.operationServiceId) ||
              (activityLogItem.truckRole && item.operationServiceId)
            "
            [loading]="dropdownLoading['fieldTech']"
          >
          </ng-select>
          <div *ngIf="(fieldTechName.invalid && (fieldTechName.dirty || fieldTechName.touched)) || activitylogForm.submitted">
            <div class="input-error f-s-13" *ngIf="fieldTechName.errors?.required">QE Tech is required.</div>
          </div>
        </div>
        <div class="col-5">
          <label class="label" for="input-hours"
            >Hours<span
              class="ms-1 text-danger"
              *ngIf="
                (activityLogItem.truckRole && i === 0) ||
                (!activityLogItem.truckRole && item.operationServiceId) ||
                (activityLogItem.truckRole && item.operationServiceId)
              "
              >*</span
            ></label
          >
          <ng-select
            name="{{ 'fieldTechHours-' + i }}"
            #fieldTechHours="ngModel"
            [items]="hoursList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="item.hours"
            notFoundText="No Hours Found"
            placeholder="Select Hours"
            [clearable]="false"
            [closeOnSelect]="true"
            [required]="
              (activityLogItem.truckRole && i === 0) ||
              (!activityLogItem.truckRole && item.operationServiceId) ||
              (activityLogItem.truckRole && item.operationServiceId)
            "
          >
          </ng-select>
          <div *ngIf="(fieldTechHours.invalid && (fieldTechHours.dirty || fieldTechHours.touched)) || activitylogForm.submitted">
            <div class="input-error f-s-13" *ngIf="fieldTechHours.errors?.required">Hours is required.</div>
          </div>
        </div>
        <div class="col-1 ps-0 d-flex align-items-center justify-content-end">
          <em class="fa fa-trash text-danger px-2 pointer" (click)="deleteFieldTech(i)"></em>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-md-12">
          <span class="text-primary pointerTicketNumberLink px-2 pointer" (click)="addFieldTech()">+ Add Service</span>
        </div>
      </div>

      <ng-container *ngFor="let item of activityLogItem.ticketActivityMaterials; let i = index">
        <div class="row mt-2 d-flex align-items-center">
          <div class="col-6">
            <label class="label">Costs</label>
            <div class="col-12 ps-0 pe-0">
              <ng-select
                name="{{ 'costTypeID-' + i }}"
                id="{{ 'costTypeID-' + i }}"
                #costTypeID="ngModel"
                [items]="costTypeList"
                bindLabel="costTypeName"
                bindValue="costTypeID"
                [(ngModel)]="item.costTypeID"
                notFoundText="No Cost Type Found"
                placeholder="Select Cost"
                [clearable]="false"
                [closeOnSelect]="true"
                [required]="!!item?.materialCost"
                (change)="item.costTypeName = $event.costTypeName"
                [loading]="dropdownLoading['costType']"
              >
              </ng-select>
            </div>
          </div>
          <div class="col-5">
            <label class="label">Amount</label>
            <div class="col-12 d-flex align-items-center ps-0 pe-0">
              <input
                nbInput
                fullWidth
                name="{{ 'materialCost-' + i }}"
                id="{{ 'materialCost-' + i }}"
                #materialCost="ngModel"
                [(ngModel)]="item.materialCost"
                currencyMask
                [options]="{ allowNegative: false }"
              />
            </div>
          </div>
          <div class="col-1 ps-0 text-center">
            <em class="fa fa-trash text-danger px-2 pointer" (click)="deleteMaterials(i)"></em>
          </div>
          <div class="col-12 input-error f-s-13">
            <div *ngIf="(costTypeID.invalid && (costTypeID.dirty || costTypeID.touched)) || activitylogForm.submitted">
              <div class="input-error f-s-13" *ngIf="costTypeID.errors?.required">Cost type is required.</div>
            </div>
          </div>
        </div>
        <div class="row mt-2 d-flex align-items-center">
          <div class="col-11 mt-2">
            <div class="col-12 d-flex align-items-center ps-0 pe-0">
              <input
                nbInput
                fullWidth
                name="{{ 'materials-' + i }}"
                id="{{ 'materials-' + i }}"
                #materials="ngModel"
                placeholder="Cost Name"
                [(ngModel)]="item.material"
                [required]="!!item?.materialCost"
                [disabled]="!item?.costTypeID"
              />
            </div>
            <div class="col-12 input-error f-s-13">
              <div *ngIf="(materials.invalid && (materials.dirty || materials.touched)) || activitylogForm.submitted">
                <div class="input-error f-s-13" *ngIf="materials.errors?.required">Cost Name is required.</div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <div class="row mt-2">
        <div class="col-md-12">
          <span class="text-primary pointerTicketNumberLink px-2 pointer" (click)="addMaterials()">+ Add Cost</span>
        </div>
      </div>

      <ng-container *ngFor="let item of activityLogItem.ticketActivityFaultCodeDeviceMaps; let i = index">
        <div class="row mt-2 d-flex">
          <div class="col-6">
            <label class="label">Fault Code</label>
            <nb-tag-list (tagRemove)="onTagRemove($event, item)" class="fault-code-tag-list">
              <nb-tag *ngFor="let code of item.faultCodes" [text]="code" [size]="tiny" removable></nb-tag>
              <input
                type="text"
                nbTagInput
                (tagAdd)="onTagAdd($event, item)"
                (keydown.enter)="$event.preventDefault(); $event.stopPropagation()"
                fullWidth
                [placeholder]="item.faultCodes?.length ? '' : 'Enter fault code'"
                maxlength="100"
              />
            </nb-tag-list>
          </div>
          <div class="col-5 fault-code-device-list">
            <label class="label" for="fault-code-device">Device</label>
            <ng-select
              id="{{ 'fault-code-device-' + i }}"
              name="{{ 'faultCodeDevice-' + i }}"
              #faultCodeDeviceIds="ngModel"
              [items]="faultCodeDeviceList"
              bindLabel="siteDeviceName"
              bindValue="siteDeviceId"
              [(ngModel)]="item.selectedDeviceIds"
              (change)="onDeviceSelectionChange(item)"
              groupBy="groupName"
              [selectableGroup]="true"
              [selectableGroupAsModel]="false"
              [multiple]="true"
              [searchable]="true"
              [clearable]="true"
              [closeOnSelect]="false"
              placeholder="Select device"
              notFoundText="No Device Found"
              (close)="filteredDeviceIds = []"
              (search)="onSiteDeviceFilter($event)"
              [loading]="dropdownLoading['device']"
            >
              <ng-template ng-header-tmp *ngIf="faultCodeDeviceList && faultCodeDeviceList?.length">
                <button type="button" (click)="selectAndDeselectAllSiteDevices(true, item)" class="btn btn-sm btn-primary">
                  Select all
                </button>
                <button type="button" (click)="selectAndDeselectAllSiteDevices(false, item)" class="btn btn-sm btn-primary ms-1">
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-optgroup-tmp let-groupItem="item" let-item$="item$" let-index="index">
                <input id="group-{{ index }}" name="group-{{ index }}" type="checkbox" [ngModel]="item$.selected" />
                <span class="text-wrap ms-1">{{ groupItem.groupName }}</span>
              </ng-template>
              <ng-template ng-option-tmp let-deviceItem="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" name="group-{{ index }}" type="checkbox" [ngModel]="item$.selected" />
                <span class="text-wrap ms-1">{{ deviceItem.siteDeviceName }}</span>
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label text-wrap">{{ item.siteDeviceName }}</span>
                  <span class="ng-value-icon right" (click)="clear(deviceId)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div class="col-1 ps-0 text-center">
            <em class="fa fa-trash text-danger px-2 pointer" (click)="deleteFaultCodesAndDevices(i)"></em>
          </div>
        </div>
      </ng-container>
      <div class="row mt-2">
        <div class="col-md-12">
          <span class="text-primary pointerTicketNumberLink px-2 pointer" (click)="addFaultCodesAndDevices()">+ Add Fault Code</span>
        </div>
      </div>

      <div class="row mt-2">
        <div class="col-md-12">
          <label class="label">Attachments</label>
          <div class="dropZone" ngFileDragDrop (fileDropped)="getUpload($event)">
            <input
              type="file"
              #file
              accept="image/*, video/*, .doc, .docx,.pdf,.xls,.xlsx, .txt"
              multiple
              (change)="getUpload($event.target.files)"
            />
            <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
            <h5 class="fw-bold">Drag & Drop files to attachment</h5>
            <label style="text-transform: none" class="fw-bold">or Browse to choose a file </label>
          </div>
          <ul *ngIf="files.length">
            <li *ngFor="let item of files; let i = index">
              <span>{{ item.name }}</span>
              <em
                (click)="deleteFile(i)"
                nbtooltip="Delete"
                nbtooltipplacement="top"
                nbtooltipstatus="text-danger"
                aria-hidden="true"
                class="fa fa-times-circle text-danger ms-2"
              ></em>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button nbButton type="button" status="basic" size="medium" (click)="onCancel()">Cancel</button>
      <button nbButton status="primary" size="medium" type="submit" id="closeSubmit">Save</button>
    </div>
  </form>
</div>
