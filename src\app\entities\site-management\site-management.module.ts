import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SharedModule } from '../../@shared/shared.module';
import { AutomationSiteDeviceListComponent } from './automation-site-device-list/automation-site-device-list.component';
import { SiteAddEditComponent } from './site-add-edit/site-add-edit.component';
import { SiteListingComponent } from './site-listing/site-listing.component';
import { SiteManagementRoutingModule } from './site-management-routing.module';
import { ViewDeviceDetailsComponent } from './view-device-details/view-device-details.component';
import { ViewSiteDetailsComponent } from './view-site-details/view-site-details.component';
import { NotesManagementModule } from '../notes-management/notes-management.module';

@NgModule({
  declarations: [
    SiteAddEditComponent,
    SiteListingComponent,
    ViewSiteDetailsComponent,
    AutomationSiteDeviceListComponent,
    ViewDeviceDetailsComponent
  ],
  imports: [CommonModule, SiteManagementRoutingModule, SharedModule, NotesManagementModule]
})
export class SiteManagementModule {}
