import { AppConstants } from '../constants';

export class Portfolio {
  public id: number;
  public name: string;
  public customerId: number;
  public customerName: string;
  public customerTitle: string;
  public email: string;
  public phone?: number;
  public noOfSite: number;
  public sumDcSize: number;
  public isActive: boolean;
  public isArchive: boolean;
  public portfolioManager: number[] = [];
  public commercialAssetsManager: number[] = [];
  public analystUser: number[] = [];
  public customerEmails: PortfolioCustomerContact[] = [];
  public emailAccount?: string;
  public outageSetting?: PortfolioOutageResponse;
  public archiveUpdatedData = [];
  public portfolioManagerName: string[];
  public analystUserName: string[];
  public commercialAssetsManagerName: string[];
}

export class PortfolioCustomerContact {
  public id = 0;
  public customerTitle: string;
  public contactName: string;
  public email: string;
  public phoneNumber: string;
  public portfolioId: number;
  public useAsTicketContact = false;
  public isTicketSummaryEnabled = false;
}

export class PortfolioFilterData {
  public totalPortfolio: number;
  public portfolios: Portfolio[];
}

export class CustomerModel {
  public id: number;
  public name: string;
}

export const PORTFOLIOFILTERLIST = {
  customer: 'Customer',
  portfolio: 'Portfolio',
  search: 'Search'
};
export class PortfolioFilter {
  public customerId: number;
  public portfolioId: number;
  public search: string;
  public page = 0;
  public sortBy = 'PortfolioName';
  public direction = 'asc';
  public itemsCount = +AppConstants.rowsPerPage;
}

export class PortfolioOutageResponse {
  public customerId: number;
  public isParentSetting: boolean;
  public outageId: number;
  public parentSettingId: number;
  public portfolioId: number;
  public siteId: number;
  public powerThreshold: number;
  public settingType: number;
  public zeroGeneration: boolean;
  public triggerCount: number;
  public timeSetting: PortfolioOutageTimeSetting[];
  public portfoliosSites: OutagePortfolioSitesSetting[];
  public sites: OutageSitesSetting[];
}

export class OutagePortfolioSitesSetting {
  public id: number;
  public isParentSetting: boolean;
  public name: string;
  public zeroGeneration: boolean;
  public sites: OutageSitesSetting[];
}
export class OutageSitesSetting {
  public id: number;
  public isParentSetting: boolean;
  public siteName: string;
  public zeroGeneration: boolean;
}

export enum Month {
  JANUARY = 'January',
  FEBRUARY = 'February',
  MARCH = 'March',
  APRIL = 'April',
  MAY = 'May',
  JUNE = 'June',
  JULY = 'July',
  AUGUST = 'August',
  SEPTEMBER = 'September',
  OCTOBER = 'October',
  NOVEMBER = 'November',
  DECEMBER = 'December'
}

export class PortfolioOutageTimeSetting {
  public month: number;
  public startTime: string;
  public endTime: string;
}
