import { DatePipe } from '@angular/common';
import { Component, Input, OnDestroy, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { NbTagComponent, NbTagInputAddEvent } from '@nebular/theme';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { AppConstants } from '../../../@shared/constants';
import { AlertService } from '../../../@shared/services';
import { CommonService } from '../../../@shared/services/common.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { TicketEstimatesApproval, TicketEstimatesApprovalStatus } from '../ticket.model';
import { TicketService } from '../ticket.service';
@Component({
  selector: 'sfl-estimate-approval-add-edit',
  templateUrl: './estimate-approval-add-edit.component.html',
  styleUrls: ['./estimate-approval-add-edit.component.scss'],
  encapsulation: ViewEncapsulation?.None
})
export class EstimateApprovalAddEditComponent implements OnInit, OnDestroy {
  public onClose: Subject<any>;
  @Input() isApprovalCreate: boolean = false;
  @Input() isTicketCreate: boolean = false;
  @Input() approvalItem: TicketEstimatesApproval;
  @Input() ticketId: number;
  @Input() userRole: string;
  @Input() customerContactEmails: any;
  approvalDetails: TicketEstimatesApproval;
  loading = false;
  subscription: Subscription = new Subscription();
  dateFormat = AppConstants.fullDateFormat;
  recipientsArray: Set<string> = new Set();
  statusDropDownList: TicketEstimatesApprovalStatus[] = [];
  @ViewChild('approvalReasonModel', { static: false }) approvalReasonModel: TemplateRef<any>;
  approvalReasonModalRef: BsModalRef;
  originalStatusId: number;
  originalNotes: string;
  originalRecipients: string;
  isSendEmail = 1;

  constructor(
    public _bsModalRef: BsModalRef,
    private readonly alertService: AlertService,
    private readonly modalService: BsModalService,
    private readonly ticketService: TicketService,
    private datePipe: DatePipe,
    private storageService: StorageService,
    private readonly commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.getApprovalStatus();
    this.approvalDetails = { ...(this.approvalItem || new TicketEstimatesApproval()) };

    this.onClose = new Subject();
    if (this.isApprovalCreate) {
      const user = this.storageService.get(AppConstants.userKey);
      const userInitials = `${user.firstName} ${user.lastName}`;
      this.recipientsArray = this.customerContactEmails?.length ? new Set(this.customerContactEmails) : new Set();
      this.approvalDetails.createdDate = this.datePipe.transform(new Date(), AppConstants.fullDateFormat);
      this.approvalDetails.createdByName = userInitials;
    } else {
      this.originalStatusId = this.approvalDetails.ticketEstimateApprovalStatusID;
      this.originalNotes = this.approvalDetails.notes || '';
      this.originalRecipients = this.approvalDetails.recipients || '';

      if (this.approvalDetails && this.approvalDetails.recipients) {
        const recipientsArray = this.approvalDetails.recipients
          .split(',')
          .map(email => email.trim())
          .filter(email => !!email);
        this.recipientsArray = new Set(recipientsArray);
      }
    }

    // this.updateSendEmailFlag();
  }

  public onConfirm(response = null): void {
    this.approvalReasonModalRef ? this.approvalReasonModalRef?.hide() : null;
    this.onClose.next(response);
    this._bsModalRef.hide();
  }

  public onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  getApprovalStatus() {
    this.subscription.add(
      this.ticketService.getApprovalStatus().subscribe({
        next: res => {
          this.statusDropDownList = res;
        }
      })
    );
  }

  onNotesChange(): void {
    // this.updateSendEmailFlag();
  }

  onTagRemove(tagToRemove: NbTagComponent): void {
    this.recipientsArray.delete(tagToRemove.text);
    // this.updateSendEmailFlag();
  }

  onTagAdd({ value, input }: NbTagInputAddEvent): void {
    if (value && !this.commonService.isValidEmail(value)) {
      this.alertService.showErrorToast('Invalid email.');
      return;
    }
    if (value) {
      this.recipientsArray.add(value);
    }
    input.nativeElement.value = '';
    // this.updateSendEmailFlag();
  }

  onTagInputKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      event.stopPropagation();
    }
  }

  onTagInputBlur(event: FocusEvent): void {
    const inputElement = event.target as HTMLInputElement;
    const value = inputElement.value.trim();
    if (value && !this.commonService.isValidEmail(value)) {
      this.alertService.showErrorToast('Invalid email.');
      return;
    }
    if (value) {
      this.recipientsArray.add(value);
    }
    inputElement.value = '';
    // this.updateSendEmailFlag();
  }

  /**
   * Update the isSendEmail flag based on whether notes or recipients have changed
   */
  updateSendEmailFlag(): void {
    if (this.isApprovalCreate) {
      this.isSendEmail = 1;
      return;
    }

    const currentNotes = this.approvalDetails.notes || '';
    const currentRecipients = Array.from(this.recipientsArray).join(', ');

    const notesChanged = currentNotes !== this.originalNotes;
    const recipientsChanged = currentRecipients !== this.originalRecipients;

    this.isSendEmail = notesChanged || recipientsChanged ? 1 : 0;
  }

  getStatusNameById(id: number): string {
    const status = this.statusDropDownList.find(item => item.ticketEstimateApprovalStatusID === id);
    return status ? status.name : '';
  }

  onAddEditApprovalItem(): void {
    if (this.recipientsArray.size === 0) {
      this.alertService.showWarningToast('Please add at least one recipient email.');
      return;
    }
    if (this.approvalDetails.ticketEstimateApprovalStatusID === 3 || this.approvalDetails.ticketEstimateApprovalStatusID === 2) {
      this.openApprovalReasonModal();
    } else {
      this.saveSendApproval();
    }
  }

  hasInValidEmails(): boolean {
    const uniqueEmails = [...new Set(this.recipientsArray)].map(e => e?.trim()).filter(Boolean);
    const validEmails = new Set<string>();
    for (const email of uniqueEmails) {
      if (!this.commonService.isValidEmail(email)) {
        this.alertService.showErrorToast('Invalid email in recipients.');
        return true;
      } else {
        validEmails.add(email);
      }
    }
    this.recipientsArray = validEmails;
    return false;
  }

  saveSendApproval(): void {
    if (this.hasInValidEmails()) return;
    this.loading = true;
    const recipientsString = Array.from(this.recipientsArray || []).join(', ');
    this.approvalDetails.recipients = recipientsString;
    const apiParams = {
      ...this.approvalDetails,
      id: this.approvalDetails.id || 0,
      ticketId: this.ticketId,
      ticketEstimateApprovalStatusID: this.approvalDetails.ticketEstimateApprovalStatusID || 1,
      ticketEstimateApprovalStatusName: this.getStatusNameById(this.approvalDetails.ticketEstimateApprovalStatusID) || '',
      notes: this.approvalDetails.notes || '',
      recipients: recipientsString || '',
      actionReason: this.approvalDetails.actionReason || '',
      isSendEmail: this.isSendEmail
    };
    if (this.isTicketCreate) {
      this.loading = false;
      this.onConfirm(apiParams);
      return;
    }
    this.subscription.add(
      this.ticketService.addUpdateTicketEstApproval(apiParams).subscribe({
        next: res => {
          this.loading = false;
          this.onConfirm(apiParams);
          this.alertService.showSuccessToast(res.message);
        },
        error: err => {
          this.loading = false;
        }
      })
    );
  }

  openApprovalReasonModal(): void {
    if (this.hasInValidEmails()) return;

    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-md'
    };
    this.approvalReasonModalRef = this.modalService.show(this.approvalReasonModel, ngModalOptions);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
