<div class="alert-box" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header justify-content-between align-items-center">
    <h5 class="mb-0">Create Ticket</h5>
    <button type="button" class="close" aria-label="Close" (click)="onCloseModel(false)">
      <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
    </button>
  </div>

  <div class="modal-body alert-details-body">
    <div *ngIf="selectedAlertDetails && selectedAlertDetails.length > 0">
      <a href="javascript:void(0)" (click)="selectAllMissingDevices()" class="mb-2">Select all Missing Production Loss Devices</a>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Select</th>
            <th>Device Name</th>
            <th>Last Data Value &amp; Time</th>
            <th>Ticket Information</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let alert of selectedAlertDetails">
            <td class="col-sm text-center" *ngIf="!isCreated">
              <nb-checkbox
                class="sfl-track-checkbox"
                id="select-{{ alert.deviceId }}"
                [checked]="alert.isSelected"
                (checkedChange)="singleDeviceSelectionChanged(alert)"
                [disabled]="isDeviceInProgress(alert.ticketsData)"
              ></nb-checkbox>
            </td>
            <td>
              <span [ngClass]="{ 'fw-bold': !alert.ticketsData.length }">
                {{ alert.deviceName }}
              </span>
            </td>
            <td>
              <ng-container *ngIf="alert.multipleAlertDate?.length; else bindDate">
                <ng-container *ngFor="let time of alert.multipleAlertDate">
                  <div [ngClass]="{ 'fw-bold': !alert.ticketsData.length }">
                    <span>{{ alert.binData }} kW </span>
                    <span *ngIf="time">({{ time }})</span>
                  </div>
                </ng-container>
              </ng-container>
              <ng-template #bindDate>
                <div [ngClass]="{ 'fw-bold': !alert.ticketsData.length }">
                  <span>{{ alert.binData }} kW </span>
                  <span *ngIf="alert.binDateTime">({{ alert.binDateTime }})</span>
                </div>
              </ng-template>
            </td>
            <td>
              <ng-container *ngIf="alert.ticketsData.length; else noTicket">
                <div *ngFor="let ticket of alert.ticketsData">
                  <a
                    [href]="'../entities/ticket/detail/view/' + ticket.ticketNumber"
                    [contextMenu]="actionMenu"
                    [contextMenuValue]="{ data: ticket.ticketNumber }"
                  >
                    {{ ticket?.ticketNumber }}
                  </a>
                </div>
              </ng-container>
              <ng-template #noTicket>
                <span>No Production Loss Ticket</span>
              </ng-template>
            </td>
          </tr>
        </tbody>
      </table>
      <form
        name="bulkCreateForm"
        #bulkCreateForm="ngForm"
        aria-labelledby="title"
        autocomplete="off"
        (ngSubmit)="bulkCreateForm?.form?.valid && CreateBulkTickets()"
      >
        <div class="mb-2">
          <label class="label" for="issueText"> Ticket Issue Field <span class="ms-1 text-danger">*</span> </label>
          <div>
            <textarea
              nbInput
              placeholder="Issue Text"
              rows="4"
              [(ngModel)]="createAlertTicketModel.ticketIssue"
              name="issueText"
              #issueText="ngModel"
              id="issue"
              placeholder="Issue"
              maxlength="5120"
              fullWidth
              required
            >
            </textarea>
            <div class="error-message-section">
              <sfl-error-msg [control]="issueText" [isFormSubmitted]="bulkCreateForm?.submitted" fieldName="Issue"></sfl-error-msg>
            </div>
          </div>
        </div>
        <div class="mb-2">
          <input
            nbInput
            name="openedDate"
            placeholder="Select Opened Date"
            fullWidth
            [nbDatepicker]="openedDatePicker"
            #openedDate="ngModel"
            [(ngModel)]="createAlertTicketModel.openDate"
            readonly
            autocomplete="off"
          />
          <nb-datepicker #openedDatePicker></nb-datepicker>
        </div>
        <div class="d-flex justify-content-end mt-3">
          <button
            id="CreateTickets"
            class="linear-mode-button"
            nbButton
            status="primary"
            size="small"
            type="submit"
            [disabled]="!bulkCreateForm?.form?.valid || loading"
          >
            Create Ticket
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<context-menu #actionMenu>
  <ng-template contextMenuItem (execute)="openLink($event.value.data, false)"> Open link in new tab </ng-template>
  <ng-template contextMenuItem (execute)="openLink($event.value.data, true)"> Open link in new window </ng-template>
</context-menu>
