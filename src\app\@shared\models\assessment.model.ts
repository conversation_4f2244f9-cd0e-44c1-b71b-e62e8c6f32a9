import { AppConstants } from '../constants/app.constant';

export class Assessment {
  public id: number;
  public portfolioId?: number;
  public siteId?: number;
  public siteIds?: number[] = [];
  public startYear?: number;
  public endYear?: number;
  public siteVisit: number;
  public electricalIV: number;
  public electricalVOC: number;
  public thermal: number;
  public aerialScan: number;
  public inverterPM: number;
  public mvpm: number;
  public vegetation: number;
  public monitoing: number;
  public performanceReporting: number;
  public jha: number;
  public customerId: number;
  public customerName: string;
  public tpm: number;
  public trq: number;
  public mvth: number;
}

export class AssessmentFilterData {
  public totalAssesment: number;
  public assesments: AssessmentDTO[];
}

export class AssessmentDTO {
  public id: number;
  public portfolioId: number;
  public siteId: number;
  public siteIds: number[];
  public startYear: number;
  public siteVisit: string;
  public electricalIV: string;
  public electricalVOC: string;
  public aerialScan: string;
  public inverterPM: string;
  public mvpm: string;
  public vegetation: string;
  public monitoing: string;
  public monitoingAbb: string;
  public performanceReporting: string;
  public jha: number;
  public portfolioName: string;
  public siteName: string;
  public state: string;
  public customerId: number;
  public customerName: string;
  public tpm: number;
  public trq: number;
  public mvth: number;
  public customerPortfolio: string;
  public thermal: string;
  public zipCode: string;
  public city: string;
  public latitude: number;
  public logitude: number;
  public address: any;
  public siteTypeStr: any;
  public invType: string;
  public xfmr: number;
  public acSize: number;
  public dcSize: number;
  public invNumber: number;
  public panelboards: string;
  public cbPanelboards: string;
}

export class FrequencyList {
  public abbrivation?: string;
  public aerialScan?: boolean;
  public electricalIvCurve?: boolean;
  public electricalVocImp?: boolean;
  public id?: number;
  public inverterPM?: boolean;
  public jha?: boolean;
  public mediumVoltagePM?: boolean;
  public monitoring?: false;
  public name?: string;
  public performanceReporting?: boolean;
  public siteVisit?: boolean;
  public thermal?: boolean;
  public tpm?: boolean;
  public trq?: boolean;
  public mvth?: boolean;
  public vegetation?: boolean;
}

export class Frequency {
  public abbrivation: string;
  public aerialScan: boolean;
  public electricalIvCurve: boolean;
  public electricalVocImp: boolean;
  public id: number;
  public inverterPM: boolean;
  public jha: boolean;
  public mediumVoltagePM: boolean;
  public monitoring: boolean;
  public name: string;
  public performanceReporting: boolean;
  public siteVisit: boolean;
  public thermal: boolean;
  public tpm: boolean;
  public trq: boolean;
  public mvth: boolean;
  public vegetation: boolean;
}

export class DefaultCustomerAssessment {
  public id: number;
  public siteVisit: number;
  public electricalIV: number;
  public electricalVOC: number;
  public thermal: number;
  public aerialScan: number;
  public inverterPM: number;
  public mvpm: number;
  public vegetation: number;
  public monitoing: number;
  public performanceReporting: number;
  public jha: number;
  public customerId: number;
  public tpm: number;
  public trq: number;
  public mvth: number;
}

export class AssessmentFilter {
  public customerId: number;
  public portfolioId: number;
  public startYear: number;
  public search: string;
  public page = 0;
  public sortBy = 'SiteName';
  public direction = 'asc';
  public itemsCount = +AppConstants.rowsPerPage;
}

export const ASSESSMENTFILTERLIST = {
  customer: 'Customer',
  portfolio: 'Portfolio',
  startYear: 'StartYear'
};
