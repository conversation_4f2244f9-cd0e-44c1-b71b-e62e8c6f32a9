import { HttpClient } from '@angular/common/http';
import { Injectable, OnDestroy } from '@angular/core';
import { catchError, EMPTY, map, Observable, Subscription } from 'rxjs';
import { StorageService } from './storage.service';
import { ApiUrl, AppConstants } from '../constants';
import { LoginTokenResponse } from '../../@auth/models/user-authentication.model';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class RefreshTokenService implements OnDestroy {
  private refreshTokenTimeout!: NodeJS.Timeout;
  private subscription: Subscription = new Subscription();
  private isRefreshing = false;

  constructor(private readonly httpClient: HttpClient, private readonly storageService: StorageService, private readonly router: Router) {}

  refreshTokenTimerInit(): void {
    const id_Token = this.storageService.get(AppConstants.authenticationToken) as string;
    const refresh_Token = this.storageService.get(AppConstants.refreshToken) as string;
    if (id_Token && refresh_Token) {
      this.stopRefreshTokenTimer();
      this.startRefreshTokenTimer(id_Token, refresh_Token);
    }
  }

  startRefreshTokenTimer(accessToken: string, refreshToken: string) {
    // parse json object from base64 encoded jwt token
    const jwtToken = JSON.parse(atob(accessToken.split('.')[1]));
    const jwtRefreshToken = JSON.parse(atob(refreshToken.split('.')[1]));

    // set a timeout to refresh the token five minutes before it expires
    const accessExpires = new Date(jwtToken.exp * 1000);
    const refreshExpires = new Date(jwtRefreshToken.exp * 1000);
    const timeout = accessExpires.getTime() - Date.now() - 5 * 60 * 1000;
    const refreshTimeout = refreshExpires.getTime() - Date.now() - 5 * 60 * 1000;
    const currentTime = Date.now();

    if (timeout > 0) {
      this.stopRefreshTokenTimer();
      if (!this.refreshTokenTimeout) {
        this.refreshTokenTimeout = setTimeout(() => this.subscription.add(this.refreshToken().subscribe()), timeout);
      }
    } else {
      if (refreshExpires.getTime() < currentTime) {
        localStorage.clear();
        this.router.navigateByUrl('/auth/login');
      } else if (refreshExpires.getTime() > currentTime && accessExpires.getTime() <= currentTime) {
        if (!this.isRefreshing) {
          this.isRefreshing = true;
          this.subscription.add(this.refreshToken().subscribe(() => (this.isRefreshing = false)));
        }
      }
    }
  }

  stopRefreshTokenTimer() {
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
      this.refreshTokenTimeout = undefined;
    }
  }

  refreshToken(): Observable<LoginTokenResponse | undefined> {
    const id_Token = this.storageService.get(AppConstants.authenticationToken);
    const refresh_Token = this.storageService.get(AppConstants.refreshToken);

    if (id_Token && refresh_Token) {
      return this.httpClient.post<LoginTokenResponse>(ApiUrl.refreshToken, { id_Token, refresh_Token }).pipe(
        map(response => {
          this.stopRefreshTokenTimer();
          this.storeAuthTokens(response);
          this.startRefreshTokenTimer(response.id_Token, response.refresh_Token);
          return response;
        }),
        catchError(() => {
          this.stopRefreshTokenTimer();
          localStorage.clear();
          this.router.navigateByUrl('/auth/login');
          return EMPTY;
        })
      );
    }
  }

  storeAuthTokens(loginTokenResponse: LoginTokenResponse) {
    this.storageService.set(AppConstants.authenticationToken, loginTokenResponse.id_Token);
    this.storageService.set(AppConstants.refreshToken, loginTokenResponse.refresh_Token);
    this.storageService.set(AppConstants.isMFAUserLoggedIn, loginTokenResponse.isMFAUserLoggedIn);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
