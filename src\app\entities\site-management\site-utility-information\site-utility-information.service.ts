import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { EChartsOption } from 'echarts';
import { Observable, of } from 'rxjs';
import { ScopeCommonInfo, ScopeData, ScopeFieldData, UTILITY_STATUS_CONFIG } from './site-utility-information.model';

@Injectable({
  providedIn: 'root'
})
export class SiteUtilityInformationService {
  constructor(private readonly http: HttpClient) {}

  getAllUtilityScopeData(scopeTypeId: number): Observable<ScopeData> {
    switch (scopeTypeId) {
      case 1:
        return of(this.getPVScopeData());
      case 2:
        return of(this.getSubstationScopeData());
      case 3:
        return of(this.getBESSScopeData());
      default:
        return null;
    }
  }

  getPVScopeData(): ScopeData {
    return {
      scopeTypeId: 1,
      scopeTypeName: 'PV',
      scopeCommonInfo: {
        name: '<PERSON><PERSON> Scope Details',
        abbreviation: 'PV',
        statusCounts: {
          pending: 12,
          fieldWorkPartiallyComplete: 18,
          fieldWorkComplete: 27,
          reportStarted: 35,
          reportDrafted: 42,
          reportCompleted: 55,
          notCreated: 9,
          canNotComplete: 6
        },
        totalCount: 204 // 12+18+27+35+42+55+9+6
      },
      fields: [
        {
          id: 1,
          name: 'Site Visit',
          abbreviation: 'SV',
          statusCounts: {
            pending: 8,
            fieldWorkPartiallyComplete: 15,
            fieldWorkComplete: 20,
            reportStarted: 28,
            reportDrafted: 30,
            reportCompleted: 35,
            notCreated: 5,
            canNotComplete: 2
          },
          totalCount: 143
        },
        {
          id: 2,
          name: 'Electrical - IV Curve',
          abbreviation: 'IV',
          statusCounts: {
            pending: 5,
            fieldWorkPartiallyComplete: 0,
            fieldWorkComplete: 19,
            reportStarted: 25,
            reportDrafted: 29,
            reportCompleted: 31,
            notCreated: 7,
            canNotComplete: 3
          },
          totalCount: 119
        },
        {
          id: 3,
          name: 'Electrical - Insulation Resistance',
          abbreviation: 'IR',
          statusCounts: {
            pending: 6,
            fieldWorkPartiallyComplete: 9,
            fieldWorkComplete: 15,
            reportStarted: 18,
            reportDrafted: 22,
            reportCompleted: 28,
            notCreated: 0,
            canNotComplete: 1
          },
          totalCount: 99
        },
        {
          id: 4,
          name: 'Electrical - Ground Fault',
          abbreviation: 'GF',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 13,
            fieldWorkComplete: 19,
            reportStarted: 24,
            reportDrafted: 26,
            reportCompleted: 33,
            notCreated: 8,
            canNotComplete: 2
          },
          totalCount: 125
        },
        {
          id: 5,
          name: 'Electrical - Continuity',
          abbreviation: 'EC',
          statusCounts: {
            pending: 4,
            fieldWorkPartiallyComplete: 0,
            fieldWorkComplete: 16,
            reportStarted: 19,
            reportDrafted: 23,
            reportCompleted: 27,
            notCreated: 6,
            canNotComplete: 3
          },
          totalCount: 98
        },
        {
          id: 6,
          name: 'Mechanical - Torque',
          abbreviation: 'MT',
          statusCounts: {
            pending: 9,
            fieldWorkPartiallyComplete: 14,
            fieldWorkComplete: 0,
            reportStarted: 28,
            reportDrafted: 31,
            reportCompleted: 39,
            notCreated: 5,
            canNotComplete: 4
          },
          totalCount: 130
        },
        {
          id: 7,
          name: 'Mechanical - Tracker',
          abbreviation: 'MT',
          statusCounts: {
            pending: 10,
            fieldWorkPartiallyComplete: 17,
            fieldWorkComplete: 24,
            reportStarted: 0,
            reportDrafted: 33,
            reportCompleted: 41,
            notCreated: 7,
            canNotComplete: 2
          },
          totalCount: 134
        },
        {
          id: 8,
          name: 'Mechanical - Racking',
          abbreviation: 'MR',
          statusCounts: {
            pending: 6,
            fieldWorkPartiallyComplete: 11,
            fieldWorkComplete: 20,
            reportStarted: 23,
            reportDrafted: 27,
            reportCompleted: 35,
            notCreated: 4,
            canNotComplete: 0
          },
          totalCount: 126
        },
        {
          id: 9,
          name: 'Mechanical - Module',
          abbreviation: 'MM',
          statusCounts: {
            pending: 11,
            fieldWorkPartiallyComplete: 15,
            fieldWorkComplete: 19,
            reportStarted: 26,
            reportDrafted: 0,
            reportCompleted: 38,
            notCreated: 5,
            canNotComplete: 1
          },
          totalCount: 115
        },
        {
          id: 10,
          name: 'Mechanical - Grounding',
          abbreviation: 'MG',
          statusCounts: {
            pending: 8,
            fieldWorkPartiallyComplete: 13,
            fieldWorkComplete: 17,
            reportStarted: 21,
            reportDrafted: 28,
            reportCompleted: 0,
            notCreated: 6,
            canNotComplete: 2
          },
          totalCount: 95
        },
        {
          id: 11,
          name: 'Combiner Box PM',
          abbreviation: 'CB',
          statusCounts: {
            pending: 7,
            fieldWorkPartiallyComplete: 12,
            fieldWorkComplete: 20,
            reportStarted: 25,
            reportDrafted: 29,
            reportCompleted: 33,
            notCreated: 4,
            canNotComplete: 3
          },
          totalCount: 133
        },
        {
          id: 12,
          name: 'DC Disconnect PM',
          abbreviation: 'DC',
          statusCounts: {
            pending: 9,
            fieldWorkPartiallyComplete: 14,
            fieldWorkComplete: 0,
            reportStarted: 22,
            reportDrafted: 26,
            reportCompleted: 34,
            notCreated: 7,
            canNotComplete: 2
          },
          totalCount: 114
        },
        {
          id: 13,
          name: 'Inverter PM',
          abbreviation: 'IP',
          statusCounts: {
            pending: 10,
            fieldWorkPartiallyComplete: 15,
            fieldWorkComplete: 23,
            reportStarted: 27,
            reportDrafted: 31,
            reportCompleted: 40,
            notCreated: 8,
            canNotComplete: 3
          },
          totalCount: 157
        }
      ]
    };
  }

  getSubstationScopeData(): ScopeData {
    return {
      scopeTypeId: 2,
      scopeTypeName: 'Substation',
      scopeCommonInfo: {
        name: 'Substation Scope Details',
        abbreviation: 'SUB',
        statusCounts: {
          pending: 12,
          fieldWorkPartiallyComplete: 18,
          fieldWorkComplete: 27,
          reportStarted: 35,
          reportDrafted: 42,
          reportCompleted: 55,
          notCreated: 9,
          canNotComplete: 6
        },
        totalCount: 204
      },
      fields: [
        {
          id: 1,
          name: 'GSU Transformer',
          abbreviation: 'GSU',
          statusCounts: {
            pending: 8,
            fieldWorkPartiallyComplete: 15,
            fieldWorkComplete: 20,
            reportStarted: 28,
            reportDrafted: 30,
            reportCompleted: 35,
            notCreated: 5,
            canNotComplete: 2
          },
          totalCount: 143
        },
        {
          id: 2,
          name: 'Auxiliary Transformer',
          abbreviation: 'AT',
          statusCounts: {
            pending: 5,
            fieldWorkPartiallyComplete: 0,
            fieldWorkComplete: 19,
            reportStarted: 25,
            reportDrafted: 29,
            reportCompleted: 33,
            notCreated: 4,
            canNotComplete: 3
          },
          totalCount: 118
        },
        {
          id: 3,
          name: 'Switchgear',
          abbreviation: 'SG',
          statusCounts: {
            pending: 6,
            fieldWorkPartiallyComplete: 11,
            fieldWorkComplete: 17,
            reportStarted: 23,
            reportDrafted: 27,
            reportCompleted: 35,
            notCreated: 4,
            canNotComplete: 0
          },
          totalCount: 123
        },
        {
          id: 4,
          name: 'Protection & Control',
          abbreviation: 'PC',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 12,
            fieldWorkComplete: 18,
            reportStarted: 24,
            reportDrafted: 28,
            reportCompleted: 36,
            notCreated: 5,
            canNotComplete: 1
          },
          totalCount: 124
        },
        {
          id: 5,
          name: 'SCADA',
          abbreviation: 'SCADA',
          statusCounts: {
            pending: 9,
            fieldWorkPartiallyComplete: 0,
            fieldWorkComplete: 20,
            reportStarted: 25,
            reportDrafted: 29,
            reportCompleted: 37,
            notCreated: 6,
            canNotComplete: 2
          },
          totalCount: 128
        },
        {
          id: 6,
          name: 'Grounding System',
          abbreviation: 'GS',
          statusCounts: {
            pending: 10,
            fieldWorkPartiallyComplete: 15,
            fieldWorkComplete: 21,
            reportStarted: 26,
            reportDrafted: 30,
            reportCompleted: 38,
            notCreated: 0,
            canNotComplete: 3
          },
          totalCount: 143
        },
        {
          id: 14,
          name: 'GSU / Main Power Transformers',
          abbreviation: 'GSU',
          statusCounts: {
            pending: 7,
            fieldWorkPartiallyComplete: 14,
            fieldWorkComplete: 19,
            reportStarted: 22,
            reportDrafted: 0,
            reportCompleted: 33,
            notCreated: 4,
            canNotComplete: 1
          },
          totalCount: 100
        },
        {
          id: 15,
          name: 'HV Circuit Breakers (SF6, vacuum, oil)',
          abbreviation: 'HVCB',
          statusCounts: {
            pending: 5,
            fieldWorkPartiallyComplete: 11,
            fieldWorkComplete: 17,
            reportStarted: 0,
            reportDrafted: 26,
            reportCompleted: 32,
            notCreated: 6,
            canNotComplete: 2
          },
          totalCount: 99
        },
        {
          id: 16,
          name: 'Disconnect / Isolator Switches',
          abbreviation: 'DIS',
          statusCounts: {
            pending: 6,
            fieldWorkPartiallyComplete: 13,
            fieldWorkComplete: 20,
            reportStarted: 23,
            reportDrafted: 25,
            reportCompleted: 30,
            notCreated: 4,
            canNotComplete: 0
          },
          totalCount: 121
        },
        {
          id: 17,
          name: 'Instrument Transformers (CTs, PTs, CVTs)',
          abbreviation: 'IT',
          statusCounts: {
            pending: 8,
            fieldWorkPartiallyComplete: 12,
            fieldWorkComplete: 0,
            reportStarted: 20,
            reportDrafted: 24,
            reportCompleted: 33,
            notCreated: 5,
            canNotComplete: 1
          },
          totalCount: 103
        },
        {
          id: 18,
          name: 'Surge Arresters',
          abbreviation: 'SA',
          statusCounts: {
            pending: 9,
            fieldWorkPartiallyComplete: 15,
            fieldWorkComplete: 22,
            reportStarted: 27,
            reportDrafted: 31,
            reportCompleted: 0,
            notCreated: 7,
            canNotComplete: 2
          },
          totalCount: 113
        },
        {
          id: 19,
          name: 'Buswork & Insulators',
          abbreviation: 'BI',
          statusCounts: {
            pending: 4,
            fieldWorkPartiallyComplete: 10,
            fieldWorkComplete: 18,
            reportStarted: 21,
            reportDrafted: 26,
            reportCompleted: 32,
            notCreated: 5,
            canNotComplete: 1
          },
          totalCount: 117
        },
        {
          id: 20,
          name: 'Neutral Grounding Equipment (NGR, reactors, grounding transformer)',
          abbreviation: 'NGE',
          statusCounts: {
            pending: 7,
            fieldWorkPartiallyComplete: 13,
            fieldWorkComplete: 19,
            reportStarted: 23,
            reportDrafted: 28,
            reportCompleted: 0,
            notCreated: 6,
            canNotComplete: 2
          },
          totalCount: 98
        },
        {
          id: 21,
          name: 'MV Switchgear / Breakers',
          abbreviation: 'MVSG',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 14,
            fieldWorkComplete: 20,
            reportStarted: 25,
            reportDrafted: 30,
            reportCompleted: 37,
            notCreated: 5,
            canNotComplete: 1
          },
          totalCount: 132
        },
        {
          id: 22,
          name: 'MV Disconnect Switches',
          abbreviation: 'MVDIS',
          statusCounts: {
            pending: 8,
            fieldWorkPartiallyComplete: 0,
            fieldWorkComplete: 22,
            reportStarted: 26,
            reportDrafted: 29,
            reportCompleted: 33,
            notCreated: 4,
            canNotComplete: 2
          },
          totalCount: 124
        },
        {
          id: 23,
          name: 'Capacitor / Reactor Banks',
          abbreviation: 'CRB',
          statusCounts: {
            pending: 6,
            fieldWorkPartiallyComplete: 11,
            fieldWorkComplete: 17,
            reportStarted: 21,
            reportDrafted: 25,
            reportCompleted: 30,
            notCreated: 4,
            canNotComplete: 1
          },
          totalCount: 115
        },
        {
          id: 24,
          name: 'Station Batteries (VLA / VRLA)',
          abbreviation: 'SB',
          statusCounts: {
            pending: 7,
            fieldWorkPartiallyComplete: 12,
            fieldWorkComplete: 0,
            reportStarted: 20,
            reportDrafted: 23,
            reportCompleted: 28,
            notCreated: 5,
            canNotComplete: 2
          },
          totalCount: 97
        },
        {
          id: 25,
          name: 'Battery Chargers',
          abbreviation: 'BC',
          statusCounts: {
            pending: 9,
            fieldWorkPartiallyComplete: 15,
            fieldWorkComplete: 0,
            reportStarted: 24,
            reportDrafted: 27,
            reportCompleted: 33,
            notCreated: 6,
            canNotComplete: 2
          },
          totalCount: 116
        },
        {
          id: 26,
          name: 'DC Panels & Ground Detectors',
          abbreviation: 'DCPGD',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 13,
            fieldWorkComplete: 18,
            reportStarted: 22,
            reportDrafted: 26,
            reportCompleted: 30,
            notCreated: 5,
            canNotComplete: 2
          },
          totalCount: 116
        },
        {
          id: 27,
          name: 'Protective Relays',
          abbreviation: 'PR',
          statusCounts: {
            pending: 8,
            fieldWorkPartiallyComplete: 0,
            fieldWorkComplete: 19,
            reportStarted: 25,
            reportDrafted: 29,
            reportCompleted: 36,
            notCreated: 5,
            canNotComplete: 2
          },
          totalCount: 124
        },
        {
          id: 28,
          name: 'Trip/Close Circuits, Trip Coils',
          abbreviation: 'TCC',
          statusCounts: {
            pending: 7,
            fieldWorkPartiallyComplete: 11,
            fieldWorkComplete: 0,
            reportStarted: 19,
            reportDrafted: 22,
            reportCompleted: 28,
            notCreated: 4,
            canNotComplete: 1
          },
          totalCount: 92
        },
        {
          id: 29,
          name: 'Meters & Fault Recorders',
          abbreviation: 'MFR',
          statusCounts: {
            pending: 5,
            fieldWorkPartiallyComplete: 9,
            fieldWorkComplete: 15,
            reportStarted: 18,
            reportDrafted: 22,
            reportCompleted: 26,
            notCreated: 4,
            canNotComplete: 0
          },
          totalCount: 99
        },
        {
          id: 30,
          name: 'RTUs / Data Gateways',
          abbreviation: 'RTU',
          statusCounts: {
            pending: 9,
            fieldWorkPartiallyComplete: 14,
            fieldWorkComplete: 21,
            reportStarted: 26,
            reportDrafted: 30,
            reportCompleted: 0,
            notCreated: 5,
            canNotComplete: 2
          },
          totalCount: 107
        },
        {
          id: 31,
          name: 'Network Gear (switches, firewalls)',
          abbreviation: 'NG',
          statusCounts: {
            pending: 8,
            fieldWorkPartiallyComplete: 13,
            fieldWorkComplete: 0,
            reportStarted: 21,
            reportDrafted: 25,
            reportCompleted: 33,
            notCreated: 6,
            canNotComplete: 2
          },
          totalCount: 108
        },
        {
          id: 32,
          name: 'Time Clocks (GPS / IRIG-B)',
          abbreviation: 'TC',
          statusCounts: {
            pending: 7,
            fieldWorkPartiallyComplete: 0,
            fieldWorkComplete: 19,
            reportStarted: 23,
            reportDrafted: 27,
            reportCompleted: 32,
            notCreated: 4,
            canNotComplete: 1
          },
          totalCount: 113
        },
        {
          id: 33,
          name: 'Backup Generator',
          abbreviation: 'BG',
          statusCounts: {
            pending: 5,
            fieldWorkPartiallyComplete: 9,
            fieldWorkComplete: 14,
            reportStarted: 18,
            reportDrafted: 23,
            reportCompleted: 0,
            notCreated: 4,
            canNotComplete: 1
          },
          totalCount: 74
        },
        {
          id: 34,
          name: 'Automatic Transfer Switch (ATS)',
          abbreviation: 'ATS',
          statusCounts: {
            pending: 8,
            fieldWorkPartiallyComplete: 12,
            fieldWorkComplete: 0,
            reportStarted: 20,
            reportDrafted: 25,
            reportCompleted: 28,
            notCreated: 5,
            canNotComplete: 2
          },
          totalCount: 100
        },
        {
          id: 35,
          name: 'Control Building HVAC',
          abbreviation: 'CBHVAC',
          statusCounts: {
            pending: 6,
            fieldWorkPartiallyComplete: 11,
            fieldWorkComplete: 17,
            reportStarted: 22,
            reportDrafted: 27,
            reportCompleted: 0,
            notCreated: 4,
            canNotComplete: 1
          },
          totalCount: 88
        },
        {
          id: 36,
          name: 'Fire Protection Systems',
          abbreviation: 'FPS',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 13,
            fieldWorkComplete: 18,
            reportStarted: 23,
            reportDrafted: 28,
            reportCompleted: 34,
            notCreated: 5,
            canNotComplete: 2
          },
          totalCount: 123
        },
        {
          id: 37,
          name: 'Security Systems',
          abbreviation: 'SS',
          statusCounts: {
            pending: 9,
            fieldWorkPartiallyComplete: 14,
            fieldWorkComplete: 0,
            reportStarted: 26,
            reportDrafted: 30,
            reportCompleted: 35,
            notCreated: 6,
            canNotComplete: 2
          },
          totalCount: 122
        },
        {
          id: 38,
          name: 'Grounding System',
          abbreviation: 'GS',
          statusCounts: {
            pending: 10,
            fieldWorkPartiallyComplete: 15,
            fieldWorkComplete: 21,
            reportStarted: 26,
            reportDrafted: 30,
            reportCompleted: 38,
            notCreated: 7,
            canNotComplete: 3
          },
          totalCount: 150
        },
        {
          id: 39,
          name: 'Lightning Protection',
          abbreviation: 'LP',
          statusCounts: {
            pending: 8,
            fieldWorkPartiallyComplete: 0,
            fieldWorkComplete: 19,
            reportStarted: 25,
            reportDrafted: 29,
            reportCompleted: 33,
            notCreated: 4,
            canNotComplete: 1
          },
          totalCount: 119
        }
      ]
    };
  }

  getBESSScopeData(): ScopeData {
    return {
      scopeTypeId: 3,
      scopeTypeName: 'BESS',
      scopeCommonInfo: {
        name: 'BESS Scope Details',
        abbreviation: 'BESS',
        statusCounts: {
          pending: 15,
          fieldWorkPartiallyComplete: 22,
          fieldWorkComplete: 30,
          reportStarted: 40,
          reportDrafted: 50,
          reportCompleted: 65,
          notCreated: 10,
          canNotComplete: 5
        },
        totalCount: 237 // sum of above values
      },
      fields: [
        {
          id: 40,
          name: 'Battery Racks / Containers',
          abbreviation: 'BRC',
          statusCounts: {
            pending: 5,
            fieldWorkPartiallyComplete: 8,
            fieldWorkComplete: 10,
            reportStarted: 15,
            reportDrafted: 18,
            reportCompleted: 22,
            notCreated: 3,
            canNotComplete: 2
          },
          totalCount: 83
        },
        {
          id: 41,
          name: 'Battery Management System (BMS)',
          abbreviation: 'BMS',
          statusCounts: {
            pending: 3,
            fieldWorkPartiallyComplete: 6,
            fieldWorkComplete: 9,
            reportStarted: 12,
            reportDrafted: 14,
            reportCompleted: 18,
            notCreated: 2,
            canNotComplete: 1
          },
          totalCount: 65
        },
        {
          id: 42,
          name: 'Battery Cooling Systems (liquid or HVAC)',
          abbreviation: 'BCS',
          statusCounts: {
            pending: 4,
            fieldWorkPartiallyComplete: 7,
            fieldWorkComplete: 11,
            reportStarted: 13,
            reportDrafted: 15,
            reportCompleted: 19,
            notCreated: 2,
            canNotComplete: 1
          },
          totalCount: 72
        },
        {
          id: 43,
          name: 'Inverters / Converters',
          abbreviation: 'IC',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 4,
            fieldWorkComplete: 9,
            reportStarted: 11,
            reportDrafted: 12,
            reportCompleted: 14,
            notCreated: 1,
            canNotComplete: 0
          },
          totalCount: 51
        },
        {
          id: 44,
          name: 'MV Transformers (step-up)',
          abbreviation: 'MVT',
          statusCounts: {
            pending: 2,
            fieldWorkPartiallyComplete: 5,
            fieldWorkComplete: 8,
            reportStarted: 10,
            reportDrafted: 13,
            reportCompleted: 16,
            notCreated: 3,
            canNotComplete: 0
          },
          totalCount: 57
        },
        {
          id: 45,
          name: 'Switchgear / Breakers',
          abbreviation: 'SB',
          statusCounts: {
            pending: 1,
            fieldWorkPartiallyComplete: 3,
            fieldWorkComplete: 6,
            reportStarted: 8,
            reportDrafted: 9,
            reportCompleted: 12,
            notCreated: 2,
            canNotComplete: 0
          },
          totalCount: 41
        },
        {
          id: 46,
          name: 'Protective Relays',
          abbreviation: 'PR',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 4,
            fieldWorkComplete: 5,
            reportStarted: 7,
            reportDrafted: 8,
            reportCompleted: 11,
            notCreated: 1,
            canNotComplete: 0
          },
          totalCount: 36
        },
        {
          id: 47,
          name: 'SCADA / EMS',
          abbreviation: 'SE',
          statusCounts: {
            pending: 2,
            fieldWorkPartiallyComplete: 5,
            fieldWorkComplete: 7,
            reportStarted: 9,
            reportDrafted: 12,
            reportCompleted: 14,
            notCreated: 2,
            canNotComplete: 1
          },
          totalCount: 52
        },
        {
          id: 48,
          name: 'Fire Detection & Suppression',
          abbreviation: 'FDS',
          statusCounts: {
            pending: 3,
            fieldWorkPartiallyComplete: 6,
            fieldWorkComplete: 9,
            reportStarted: 12,
            reportDrafted: 15,
            reportCompleted: 18,
            notCreated: 3,
            canNotComplete: 1
          },
          totalCount: 67
        },
        {
          id: 49,
          name: 'Station Batteries & Chargers',
          abbreviation: 'SBC',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 5,
            fieldWorkComplete: 8,
            reportStarted: 10,
            reportDrafted: 12,
            reportCompleted: 13,
            notCreated: 2,
            canNotComplete: 0
          },
          totalCount: 50
        },
        {
          id: 50,
          name: 'UPS Systems',
          abbreviation: 'UPS',
          statusCounts: {
            pending: 1,
            fieldWorkPartiallyComplete: 3,
            fieldWorkComplete: 5,
            reportStarted: 6,
            reportDrafted: 9,
            reportCompleted: 10,
            notCreated: 2,
            canNotComplete: 0
          },
          totalCount: 36
        },
        {
          id: 51,
          name: 'Auxiliary Transformers',
          abbreviation: 'AT',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 2,
            fieldWorkComplete: 4,
            reportStarted: 6,
            reportDrafted: 7,
            reportCompleted: 9,
            notCreated: 1,
            canNotComplete: 0
          },
          totalCount: 29
        },
        {
          id: 52,
          name: 'Container / Enclosure HVAC',
          abbreviation: 'CEHVAC',
          statusCounts: {
            pending: 3,
            fieldWorkPartiallyComplete: 4,
            fieldWorkComplete: 6,
            reportStarted: 7,
            reportDrafted: 9,
            reportCompleted: 11,
            notCreated: 2,
            canNotComplete: 0
          },
          totalCount: 42
        },
        {
          id: 53,
          name: 'Ventilation / Exhaust Fans',
          abbreviation: 'VEF',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 5,
            fieldWorkComplete: 7,
            reportStarted: 9,
            reportDrafted: 10,
            reportCompleted: 12,
            notCreated: 1,
            canNotComplete: 0
          },
          totalCount: 44
        },
        {
          id: 54,
          name: 'Security Systems',
          abbreviation: 'SS',
          statusCounts: {
            pending: 2,
            fieldWorkPartiallyComplete: 3,
            fieldWorkComplete: 5,
            reportStarted: 7,
            reportDrafted: 8,
            reportCompleted: 9,
            notCreated: 1,
            canNotComplete: 0
          },
          totalCount: 35
        },
        {
          id: 55,
          name: 'Grounding & Lightning Protection',
          abbreviation: 'GLP',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 4,
            fieldWorkComplete: 5,
            reportStarted: 6,
            reportDrafted: 8,
            reportCompleted: 9,
            notCreated: 1,
            canNotComplete: 0
          },
          totalCount: 33
        },
        {
          id: 56,
          name: 'Backup Generator & ATS',
          abbreviation: 'BGA',
          statusCounts: {
            pending: 1,
            fieldWorkPartiallyComplete: 3,
            fieldWorkComplete: 4,
            reportStarted: 5,
            reportDrafted: 6,
            reportCompleted: 8,
            notCreated: 1,
            canNotComplete: 0
          },
          totalCount: 28
        },
        {
          id: 57,
          name: 'Safety Equipment (Eyewash, Spill Kits, PPE)',
          abbreviation: 'SE',
          statusCounts: {
            pending: 0,
            fieldWorkPartiallyComplete: 2,
            fieldWorkComplete: 3,
            reportStarted: 5,
            reportDrafted: 6,
            reportCompleted: 7,
            notCreated: 1,
            canNotComplete: 0
          },
          totalCount: 24
        }
      ]
    };
  }

  // getAllUtilityScopeDatas(filter: SiteUtilityFilter): Observable<UtilityScopeResponse> {
  //   // Mock data - replace with actual API call
  //   const mockData: UtilityScopeResponse = {
  //     pvScope: this.createScopeData(1, 'PV', 'PV Scope Details', 'PV', this.getPVFields()),
  //     substationScope: this.createScopeData(2, 'Substation', 'Substation Scope Details', 'Substation', this.getSubstationFields()),
  //     bessScope: this.createScopeData(3, 'BESS', 'BESS Scope Details', 'BESS', this.getBESSFields())
  //   };

  //   return of(mockData);
  // }

  // private createScopeData(id: number, typeName: string, name: string, abbreviation: string, fieldNames: string[]): ScopeData {
  //   const commonStatusCounts = this.getRandomStatusCounts();
  //   const fields = fieldNames.map((fieldName, index) => this.createFieldData(index + 1, fieldName));

  //   return {
  //     scopeTypeId: id,
  //     scopeTypeName: typeName,
  //     scopeCommonInfo: {
  //       name,
  //       abbreviation,
  //       statusCounts: commonStatusCounts,
  //       totalCount: this.calculateTotalCount(commonStatusCounts)
  //     },
  //     fields
  //   };
  // }

  // private createFieldData(id: number, name: string): ScopeFieldData {
  //   const statusCounts = this.getRandomStatusCounts();
  //   return {
  //     id,
  //     name,
  //     abbreviation: name
  //       .split(' ')
  //       .map(word => word[0])
  //       .join(''),
  //     statusCounts,
  //     totalCount: this.calculateTotalCount(statusCounts)
  //   };
  // }

  // private getPVFields(): string[] {
  //   return [
  //     'Site Visit',
  //     'Electrical - IV Curve',
  //     'Electrical - Insulation Resistance',
  //     'Electrical - Ground Fault',
  //     'Electrical - Continuity',
  //     'Mechanical - Torque',
  //     'Mechanical - Tracker',
  //     'Mechanical - Racking',
  //     'Mechanical - Module',
  //     'Mechanical - Grounding',
  //     'Combiner Box PM',
  //     'DC Disconnect PM',
  //     'Inverter PM'
  //   ];
  // }

  // private getSubstationFields(): string[] {
  //   return [
  //     'GSU Transformer',
  //     'Auxiliary Transformer',
  //     'Switchgear',
  //     'Protection & Control',
  //     'SCADA',
  //     'Grounding System',
  //     'Fire Protection',
  //     'Security System',
  //     'Lighting System',
  //     'HVAC'
  //   ];
  // }

  // private getBESSFields(): string[] {
  //   return [
  //     'Battery Modules',
  //     'Battery Management System',
  //     'Power Conversion System',
  //     'Energy Management System',
  //     'Cooling System',
  //     'Fire Suppression System',
  //     'Electrical Protection',
  //     'Monitoring & Control',
  //     'Safety Equipment'
  //   ];
  // }

  // private getRandomStatusCounts(): { [statusKey: string]: number } {
  //   const statusKeys = Object.keys(UTILITY_STATUS_CONFIG);
  //   const counts: { [statusKey: string]: number } = {};

  //   statusKeys.forEach(key => {
  //     counts[key] = Math.floor(Math.random() * 20) + 1;
  //   });

  //   return counts;
  // }

  // private calculateTotalCount(statusCounts: { [statusKey: string]: number }): number {
  //   return Object.values(statusCounts).reduce((total, count) => total + count, 0);
  // }

  // Generate main chart with abbreviation in center and legend below
  generateMainChart(scopeCommonInfo: ScopeCommonInfo): EChartsOption {
    const data = Object.entries(scopeCommonInfo.statusCounts).map(([statusKey, count]) => ({
      name: UTILITY_STATUS_CONFIG[statusKey].name,
      value: count,
      itemStyle: {
        color: UTILITY_STATUS_CONFIG[statusKey].color
      }
    }));

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      // legend: {
      //   orient: 'vertical',
      //   left: 'left',
      //   // top: '95%',
      //   top: 'bottom',
      //   // textStyle: {
      //   //   fontSize: 11
      //   // },
      //   // itemWidth: 12,
      //   // itemHeight: 12,
      //   // padding: [0, 0, 0, 0]
      //   textStyle: {
      //     color: '#fff'
      //   }
      // },
      series: [
        {
          name: scopeCommonInfo.name,
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          avoidLabelOverlap: true,
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'none'
            }
          },
          label: {
            show: true,
            position: 'center',
            fontSize: 18,
            fontWeight: 'bold',
            formatter: scopeCommonInfo.abbreviation
          },
          labelLine: {
            show: false
          }
        }
      ]
    };
  }

  // Generate field chart with only tooltip on hover
  generateFieldChart(field: ScopeFieldData): EChartsOption {
    const data = Object.entries(field.statusCounts).map(([statusKey, count]) => ({
      name: UTILITY_STATUS_CONFIG[statusKey].name,
      value: count,
      itemStyle: {
        color: UTILITY_STATUS_CONFIG[statusKey].color
      }
    }));

    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: field.name,
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          avoidLabelOverlap: true,
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: false
          },
          labelLine: {
            show: false
          }
        }
      ]
    };
  }
}
