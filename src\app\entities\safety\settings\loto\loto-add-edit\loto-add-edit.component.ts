import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { LOTO, LOTOBarrier, LOTOItemEmitResponse, SetLotoWorkStepName } from '../../settings.model';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { SettingService } from '../../settings.service';
import { Subscription } from 'rxjs';
import { AlertService } from '../../../../../@shared/services';

@Component({
  selector: 'sfl-loto-add-edit',
  templateUrl: './loto-add-edit.component.html',
  styleUrls: ['./loto-add-edit.component.scss']
})
export class LotoAddEditComponent implements OnInit, OnDestroy {
  @Input() fromJhaWorkStep = false;
  @Input() lotoId = 0;
  @Input() lotoItem: LOTO = null;
  @Input() lotoBarrier: LOTOBarrier = null;
  @Input() lotoList: LOTO[] = [];
  @Output() onLotoItemEmit: EventEmitter<LOTOItemEmitResponse> = new EventEmitter<LOTOItemEmitResponse>();

  private subscription: Subscription = new Subscription();

  lotoLoader = false;
  isEditMode = false;
  isViewMode = false;
  lotoForm: FormGroup;

  constructor(
    private readonly _bsModalRef: BsModalRef,
    private readonly fb: FormBuilder,
    private readonly settingService: SettingService,
    private readonly alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.initializeFormGroup();
    if (this.fromJhaWorkStep) {
      if (this.lotoId || this.lotoItem) {
        this.isEditMode = true;
        if (!this.lotoItem && this.lotoId) {
          this.getLOTOById(this.lotoId);
        } else {
          this.lotoForm.patchValue(this.lotoItem);
        }
      } else {
        this.isEditMode = false;
        this.isViewMode = false;
      }
    }
  }

  initializeFormGroup(): void {
    this.lotoForm = this.fb.group({
      id: new FormControl(0),
      name: new FormControl('LOTO'),
      displayName: new FormControl(),
      equipmentType: new FormControl('', Validators.compose([Validators.required])),
      equipmentModel: new FormControl('', Validators.compose([Validators.required])),
      asBuildPageNumber: new FormControl(null, Validators.compose([Validators.required]))
    });

    this.subscription.add(
      this.lotoForm.valueChanges.subscribe(() => {
        const { equipmentType, equipmentModel, asBuildPageNumber } = this.lotoForm.value;
        const lotoDisplayNameControl = this.lotoForm.get('displayName');
        const lotoDisplayName = SetLotoWorkStepName.createLotoWorkStepName(equipmentType, equipmentModel, asBuildPageNumber);
        lotoDisplayNameControl.setValue(lotoDisplayName, { emitEvent: false });
      })
    );
  }

  onBack(lotoItem: LOTO, fromBackButton = true): void {
    if (!fromBackButton && lotoItem) {
      const lotoItemEmitResponse = new LOTOItemEmitResponse(lotoItem, this.lotoBarrier);
      this.onLotoItemEmit.emit(lotoItemEmitResponse);
    }
    this._bsModalRef.hide();
  }

  getLOTOById(lotoId: number): void {
    this.lotoLoader = true;
    this.subscription.add(
      this.settingService.getLOTOById(lotoId).subscribe({
        next: res => {
          this.lotoForm.patchValue(res);
          this.lotoLoader = false;
        },
        error: err => {
          this.lotoLoader = false;
        }
      })
    );
  }

  createUpdateLOTOByApi(lotoItem: LOTO): void {
    this.lotoLoader = true;
    this.subscription.add(
      this.settingService.createUpdateLOTO(lotoItem).subscribe({
        next: res => {
          this.onBack(lotoItem, false);
          this.lotoLoader = false;
        },
        error: err => {
          this.lotoLoader = false;
        }
      })
    );
  }

  createUpdateLOTO(lotoItem: LOTO): void {
    const trimmedDisplayName = lotoItem?.displayName?.trim()?.toLowerCase();
    const isDuplicate = this.lotoList?.some(
      item => item?.randomGuid !== this.lotoItem?.randomGuid && item?.displayName?.trim()?.toLowerCase() === trimmedDisplayName
    );

    if (isDuplicate) {
      this.alertService.showErrorToast('This LOTO already exists.');
    } else {
      this.onBack(lotoItem, false);
    }
  }

  onSubmit(): void {
    if (this.lotoForm.valid) {
      this.createUpdateLOTO(this.lotoForm.value);
    } else {
      this.lotoForm.markAllAsTouched();
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
