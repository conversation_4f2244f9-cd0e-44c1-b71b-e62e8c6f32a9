import { Component, OnInit } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { checkAuthorisations } from '../../../@shared/utils';
import { FILTER_PAGE_NAME, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { NotificationAddEditComponent } from './notification-add-edit/notification-add-edit.component';
import { NbDialogService } from '@nebular/theme';
import { NotificationService } from './notification.service';
import { StorageService } from '../../../@shared/services/storage.service';
import { NotificationItem, NotificationListResponse } from './notification.model';

@Component({
  selector: 'sfl-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss']
})
export class NotificationsComponent implements OnInit {
  loading = false;
  filterDetails: FilterDetails = new FilterDetails();
  isFilterDisplay = false;
  viewFilterSection = 'notificationsFilterSection';
  pageSize = AppConstants.rowsPerPage;
  subscription: Subscription = new Subscription();
  modalRef: BsModalRef;
  currentPage = 1;
  filterModel: CommonFilter = new CommonFilter();
  viewPage = FILTER_PAGE_NAME.SAFETY_NOTIFICATIONS;
  total: number;
  sortOptionList = {
    customerPortfolio: 'asc',
    Site: 'asc'
  };
  notificationList: NotificationItem[] = [];

  constructor(
    private readonly modalService: BsModalService,
    private readonly notificationService: NotificationService,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.initFilterDetails();
    this.getAllNotificationList();
  }

  sort(sortBy: string, changeSort: string): void {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.sortOptionList[sortBy] = changeSort;
    this.filterModel.sortBy = sortBy;
    this.filterModel.direction = changeSort;
    this.getAllNotificationList();
  }

  refreshList(filterParams: CommonFilter): void {
    this.currentPage = filterParams.page;
    this.getAllNotificationList(true, filterParams);
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.CUSTOMER.show = true;
    filterItem.CUSTOMER.multi = true;
    filterItem.PORTFOLIO.show = true;
    filterItem.PORTFOLIO.multi = true;
    filterItem.SITE.show = true;
    filterItem.SITE.multi = true;
    filterItem.IS_ACTIVE_NOTIFICATION.show = true;
    filterItem.IS_INACTIVE_MISSING_CONFIG_NOTIFICATION.show = true;
    this.filterModel.isActive = true;
    this.filterDetails.filter_item = filterItem;
  }

  openAddEditNotification(notificationId?: number): void {
    if (notificationId) {
      this.loading = true;
      this.notificationService.getNotificationById(notificationId).subscribe({
        next: notificationData => {
          this.openAddEditNotificationModal(notificationData.data, !!this.filterModel.isActive);
          this.loading = false;
        },
        error: err => {
          console.log('Error fetching notification', err);
          this.loading = false;
        }
      });
    } else {
      this.openAddEditNotificationModal(null, !!this.filterModel.isActive);
    }
  }

  openAddEditNotificationModal(notificationData: NotificationItem, isShowActiveToggle: boolean): void {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg',
      initialState: {
        notification: notificationData,
        isShowActiveToggle: isShowActiveToggle
      }
    };
    this.modalRef = this.modalService.show(NotificationAddEditComponent, ngModalOptions);
    this.modalRef.content.event.subscribe(res => {
      if (res) {
        this.loading = false;
        this.getAllNotificationList();
      }
    });
  }

  getAllNotificationList(saveFilter = true, filterParams?: CommonFilter): void {
    this.loading = true;
    if (filterParams) {
      this.filterModel = filterParams;
    }
    if (saveFilter) {
      this.storageService.set(this.viewPage, this.filterModel);
    }
    this.subscription.add(
      this.notificationService.getAllNotificationByFilter(this.filterModel).subscribe({
        next: (data: NotificationListResponse) => {
          this.allNotificationList(data);
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  allNotificationList(data: NotificationListResponse): void {
    this.notificationList = data.items;
    this.total = data.totalCount;
    this.loading = false;
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    this.modalRef?.content?.event?.unsubscribe();
  }
}
