::ng-deep {
  .ticketDetailSpinner {
    .ticket-note {
      nb-spinner {
        align-items: center !important;
      }
    }
  }
}

.input-error {
  color: #ff0035;
  float: left;
  font-size: 13px;
}
.min-w-900 {
  min-width: 900px;
}

.ticket-detail {
  label {
    display: block;
  }

  .borderRight {
    border-right: 1px solid #151a30;
  }

  .borderBottom {
    border-bottom: 1px solid #151a30;

    .isNoImages {
      cursor: not-allowed;
      color: #58625f;
    }

    .mt_1 {
      margin-top: 1px;
    }
  }

  .borderTop {
    border-top: 1px solid #151a30;
  }

  .activeRMA {
    background-color: #4ea66f !important;
    border-color: #4ea66f !important;
  }

  .ticketStatus {
    color: #3366ff;
    font-weight: bold;
  }

  .ticketLowPriority {
    color: #58625f;
    font-weight: bold;
  }

  .ticketHighPriority {
    color: #fe1f00;
    font-weight: bold;
  }

  .ticketMediumPriority {
    color: #ff9f00;
    font-weight: bold;
  }

  .worklog-section {
    width: 100% !important;

    label {
      margin-bottom: 0px !important;
    }
  }

  .disableInput {
    cursor: not-allowed;
  }

  .file-img {
    max-width: -webkit-fill-available;
    padding: 1rem;
  }

  .imageFilename {
    max-width: 70% !important;
    word-wrap: break-word;
    min-width: 70% !important;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .image-ticket-page {
    min-height: 140px !important;
    max-height: 140px !important;
    max-width: 100%;
    border-radius: 10px;
  }

  .no-bottom-label {
    margin-bottom: 0px;
    min-width: max-content;
  }

  .w-available {
    width: -webkit-fill-available;
  }

  .est-file-zone {
    .h-100 {
      height: 100%;
    }

    .dropZone {
      height: 100% !important;
      em {
        font-size: 30px !important;
      }
      h5 {
        font-size: 18px !important;
        margin-bottom: 0px !important;
      }
      label {
        font-size: 15px !important;
        margin-bottom: 0px !important;
      }
    }

    .image-ticket-page {
      max-width: 160px;
      min-height: 110px !important;
      max-height: 110px !important;
      padding: 0.25rem !important;
    }
  }

  .p-l-2 {
    padding-top: 2px;
  }

  .pointer {
    cursor: pointer;
  }

  .max-w-50 {
    max-width: 50% !important;
  }

  .w-fill-available {
    width: -webkit-fill-available;
  }

  @media (max-width: 1030px) {
    .button_list {
      button {
        padding: 0.5rem;
      }
    }
  }

  @media (max-width: 460px) {
    .button_list {
      button {
        padding: 0.25rem;
      }
    }
  }

  .m-w-550 {
    min-width: 500px;
  }

  .m-w-700 {
    min-width: 500px;
  }

  .m-w-1000 {
    min-width: 1000px;
  }
}

::ng-deep nb-card-header,
::ng-deep nb-accordion-item-header {
  padding: 0.75rem 1.25rem !important;
}

::ng-deep nb-tabset {
  .tabset {
    li {
      width: -webkit-fill-available !important;
      .tab-link {
        justify-content: center !important;
      }
    }
  }
  .comment-box {
    min-height: 80px !important;
  }

  .comment-section {
    width: 100% !important;
  }

  .history-section {
    width: 100% !important;

    ul {
      list-style-type: none;
      padding-left: 0px;

      .history-list {
        border-bottom: 1px solid #151a30;
      }
      .history-list:last-child {
        border-bottom: none;
      }
    }
  }
}

::ng-deep modal-container .img-view-section {
  .btnClose {
    top: 0 !important;
  }
  .image-viwer {
    overflow: hidden;
    div {
      overflow: hidden;
    }
  }
  max-width: 60% !important;
  .carousel-inner {
    overflow: hidden !important;
    .item {
      text-align: center;
      .img-slide {
        width: unset;
        max-width: 100%;
        vertical-align: middle;
        max-height: 95vh;
      }
    }
  }
}

::ng-deep nb-tag {
  text-transform: none !important;
}

::ng-deep .ticketDetailSpinner nb-spinner {
  align-items: flex-start !important;
  padding: 3% !important;
}

::ng-deep slide {
  .img-slide {
    max-width: -webkit-fill-available;
    max-height: 560px;
  }
}

input:disabled {
  cursor: not-allowed;
}

.pointerTicketNumberLink {
  color: rgb(89, 139, 255) !important;
  cursor: pointer !important;
  text-decoration-line: underline;
}
.max-lines {
  text-overflow: ellipsis;
  overflow: hidden;
  line-height: 25px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  font-size: 14px !important;
}
.flex-none {
  flex: none;
}
.m-l-10 {
  margin-left: 10px;
}
.w-30 {
  width: 30%;
}
.addStyle {
  margin: auto;
  margin-right: 30px;
}
.my-custom-scrollbar {
  position: relative;
  height: 200px;
  overflow: auto;
}
.table-wrapper-scroll-y {
  display: block;
}
.estimate-info-input {
  height: 1.8rem;
  width: 5rem;
  color: white;
}
.myclass {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}
::ng-deep .estimation-table {
  table thead tr th {
    border: none;
    border-width: 0;
    text-align: center;
  }
  .table tbody tr td {
    border: none;
    text-align: center;
    border-width: 0;
  }
  table {
    border: none;
    background-color: transparent !important;
  }
  .nb-theme-dark table thead {
    background-color: transparent !important;
  }
}

.attachment_section {
  .file-upload-btn {
    background-color: #36f;
    border-color: #36f;
    color: #fff;
    padding: 0.48rem 0.875rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    line-height: 1rem;
    font-weight: bold;
  }

  .tag-info-badge {
    color: #ffffff;
    background-color: #0f172c;
    border-radius: 5px;
    margin: 0 8px 10px 0;
    font-size: 14px;
  }
  .img-preview {
    width: 30px;
    height: 30px;
    border-radius: 5px;
    margin-right: 5px;
  }
  .pdf-icon {
    background: grey;
    padding: 7px;
    border-radius: 5px;
  }

  .fs-14 {
    font-size: 14px;
  }
}

.fault-code-tag-list {
  max-height: 72px;
  width: 95% !important;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-wrap: wrap;
  padding-inline: 0.5rem !important;

  nb-tag {
    padding-left: 0.25rem !important;
    border-radius: 2px !important;
  }
}
