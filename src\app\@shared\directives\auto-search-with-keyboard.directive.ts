import { Directive, ElementRef, EventEmitter, Host, HostListener, Input, Optional, Output } from '@angular/core';
import { NbDatepickerComponent, NbRangepickerComponent } from '@nebular/theme';
import { NgSelectComponent } from '@ng-select/ng-select';
import { AlertService } from '../services';
import { SflAutoSearchWithKeyboardResponse } from '../components/filter/common-filter.model';
import { AppMessages } from '../constants';

@Directive({
  selector: '[sflAutoSearchWithKeyboard]'
})
export class AutoSearchWithKeyboardDirective {
  @Input('sflAutoSearchWithKeyboard') canApplyFilterOnEnter: boolean = false;
  @Input() ngModel: any;
  @Input() nbDateRangePickerEl: NbRangepickerComponent<Date> | NbDatepickerComponent<Date> = null;
  @Input() sflAutoSearchWithKeyboardName: string = null;

  @Output() onEnterKeyDown = new EventEmitter<SflAutoSearchWithKeyboardResponse>();

  constructor(
    private el: ElementRef,
    @Host() @Optional() private ngSelect: NgSelectComponent,
    private readonly toastService: AlertService
  ) {}

  get isInValidFilter() {
    return this.ngModel === '' || this.ngModel === null || this.ngModel === undefined || this.ngModel.length === 0;
  }

  get ngSelectFilteredItems() {
    return (
      (this.ngSelect?.searchTerm &&
        this.ngSelect?.multiple &&
        this.ngSelect?.itemsList?.['_filteredItems']
          ?.map(item => item?.value?.[this.ngSelect?.bindValue])
          ?.filter(item => !this.ngModel?.includes(item))) ??
      []
    );
  }

  @HostListener('keydown', ['$event'])
  @HostListener('keyup', ['$event'])
  @HostListener('keypress', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    const keyboardEventKey = event.key;
    // to be used
    // const isInValidFilter = this.ngModel === '' || this.ngModel === null || this.ngModel === undefined || this.ngModel.length === 0;
    event.stopImmediatePropagation();

    if (this.canApplyFilterOnEnter) {
      if (this.ngSelect) {
        const filteredItem = this.ngSelectFilteredItems;
        this.ngSelect.keyDownFn = () => keyboardEventKey !== 'Enter';
        this.ngSelect.selectOnTab = false;
        this.ngSelect.markFirst = false;
        this.ngSelect.config.openOnEnter = false;
        this.ngSelect.openOnEnter = false;
        if (keyboardEventKey === 'Enter') {
          if (this.ngSelect.isOpen) {
            this.ngSelect.close();
          }
          this.ngSelect.blur();
          this.ngSelect.isOpen = false;
          filteredItem?.length ? (this.ngModel = [...(this.ngModel || []), ...filteredItem]) : null;
          this.ngSelect.writeValue(this.ngModel);
          this.onEnterKeyDown.emit(new SflAutoSearchWithKeyboardResponse(!this.isInValidFilter, this.ngModel));
          if (this.isInValidFilter && this.sflAutoSearchWithKeyboardName) {
            this.toastService.showErrorToast(
              AppMessages.InValidValueAutoSearchWithKeyboardMessage.replace('${filterLabel}', this.sflAutoSearchWithKeyboardName)
            );
          }
          return;
        }
      } else {
        if (keyboardEventKey === 'Enter') {
          if (this.nbDateRangePickerEl) {
            this.nbDateRangePickerEl.hide();
          }
          this.el.nativeElement.blur();
          this.onEnterKeyDown.emit(new SflAutoSearchWithKeyboardResponse(!this.isInValidFilter, this.ngModel));
          if (this.isInValidFilter && this.sflAutoSearchWithKeyboardName) {
            this.toastService.showErrorToast(
              AppMessages.InValidValueAutoSearchWithKeyboardMessage.replace('${filterLabel}', this.sflAutoSearchWithKeyboardName)
            );
          }
          return;
        }
      }
    }
  }
}
