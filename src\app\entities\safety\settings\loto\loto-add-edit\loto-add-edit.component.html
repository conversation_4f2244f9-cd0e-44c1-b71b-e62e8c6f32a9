<div [nbSpinner]="lotoLoader" nbSpinnerStatus="primary" nbSpinnerSize="medium">
  <div class="modal-header">
    <h5 class="modal-title">{{ isViewMode ? 'View' : isEditMode ? 'Edit' : 'Add' }} Equipment LOTO</h5>
    <button type="button" class="close" aria-label="Close" (click)="onBack(null)">
      <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
    </button>
  </div>
  <form [formGroup]="lotoForm" aria-labelledby="title" autocomplete="off" (ngSubmit)="onSubmit()">
    <div class="modal-body">
      <div class="row mt-2">
        <div class="col-12">
          <label class="label" for="input-equipment-type">Equipment Type<span class="ms-1 text-danger">*</span></label>
          <input
            nbInput
            formControlName="equipmentType"
            placeholder="Add Equipment Type"
            fullWidth
            required
            autocomplete="off"
            [disabled]="isViewMode"
          />
          <sfl-error-msg [control]="lotoForm?.controls?.equipmentType" fieldName="Equipment Type"></sfl-error-msg>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-12">
          <label class="label" for="input-equipment-model">Equipment Model<span class="ms-1 text-danger">*</span></label>
          <input
            nbInput
            formControlName="equipmentModel"
            placeholder="Add Equipment Model"
            fullWidth
            required
            autocomplete="off"
            [disabled]="isViewMode"
          />
          <sfl-error-msg [control]="lotoForm?.controls?.equipmentModel" fieldName="Equipment Model"></sfl-error-msg>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-12">
          <label class="label" for="input-as-built-page-number">As-Built Page Number<span class="ms-1 text-danger">*</span></label>
          <input
            nbInput
            formControlName="asBuildPageNumber"
            placeholder="Add As-Built Page Number"
            fullWidth
            required
            OnlyNumber
            autocomplete="off"
            [disabled]="isViewMode"
          />
          <sfl-error-msg [control]="lotoForm?.controls?.asBuildPageNumber" fieldName="As-Built Page Number"></sfl-error-msg>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button nbButton status="primary" type="submit" class="ms-2 btn btn-primary" [disabled]="lotoForm.invalid || isViewMode">
        Submit
      </button>
    </div>
  </form>
</div>
