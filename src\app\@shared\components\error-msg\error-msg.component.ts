import { Component, Input } from '@angular/core';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'sfl-error-msg',
  templateUrl: './error-msg.component.html',
  styleUrls: ['./error-msg.component.scss']
})
export class ErrorMsgComponent {
  @Input() control: FormControl;
  @Input() fieldName: FormControl;
  @Input() isFormSubmitted: boolean;
  @Input() minErrorMsg: string;
  @Input() phoneCheck = false;
  @Input() fromDiffValueFieldName: FormControl;
  @Input() fromSameValueFieldName: FormControl;
  @Input() minDateTime: FormControl;
  @Input() maxDateTime: FormControl;
  isInvalidMsg = ' is invalid';

  get errorMessage() {
    // we want to check if the field is a phone then it should not allowed only 0 as value.
    if (this.phoneCheck) {
      const phoneValue = String(this.control.value);
      // Check if the phone field is filled with a single digit '0'
      if (
        phoneValue &&
        (phoneValue?.trim() === '0' || phoneValue?.trim().length !== 10) &&
        (this.control.touched || this.control.dirty || this.isFormSubmitted)
      ) {
        return 'Invalid phone number.';
      }
    }
    for (const propertyName in this.control.errors) {
      if (this.control.errors.hasOwnProperty(propertyName) && (this.control.touched || this.control.dirty || this.isFormSubmitted)) {
        if (this.isFormSubmitted) {
          this.setTouchedControl(this.control);
        }
        return this.getValidatorErrorMessage(propertyName, this.control.errors[propertyName]);
      }
    }
    return null;
  }

  getValidatorErrorMessage(validatorName: string, validatorValue?: any) {
    const config = {
      required: `${this.fieldName} is required`,
      appPhoneValidate: `${this.fieldName}${this.isInvalidMsg}`,
      appEmailValidate: `${this.fieldName}${this.isInvalidMsg}`,
      appPasswordValidate: `${this.fieldName} must contain 8 characters, capital letters, lowercase, numbers and special character.`,
      maxlength: `Maximum length ${validatorValue.requiredLength}`,
      minlength: `Minimum length ${validatorValue.requiredLength}`,
      min: this.minErrorMsg ? this.minErrorMsg : `Minimum value is ${validatorValue.min}`,
      max: `Maximum value is ${validatorValue.max}`,
      mask: `Required format is ${validatorValue.requiredMask}`,
      matchPassword: `${this.fieldName} is mismatched`,
      appEqualvalidate: `${this.fieldName} is mismatched`,
      appWebValidate: `${this.fieldName}${this.isInvalidMsg}`,
      appTimeCheckValidate: `${this.fieldName}${this.isInvalidMsg}`,
      owlDateTimeMin: `${this.fieldName} should be greater than start time`,
      appAlphaNumeric: 'Only characters and numbers allowed.',
      nbDatepickerMin: 'Date is required',
      nbDatepickerFilter: 'Date is required',
      incorrect: `Please enter valid values`,
      whitespace: `White spaces are not allowed`,
      needDifferentValue: `Please enter different value from ${this.fromDiffValueFieldName}`,
      needSameValue: `Please enter same value from ${this.fromSameValueFieldName}`,
      minDateTimeError: `${this.fieldName} should be greater or same as ${this.minDateTime}`,
      maxDateTimeError: `${this.fieldName} should be less or same as ${this.maxDateTime}`,
      qeServiceTypeValidation: `${validatorValue} is required for ${this.fieldName}`
    };

    return config[validatorName];
  }

  setTouchedControl(control) {
    control.control.markAsTouched();
  }
}
