export class PowerCardsFilter {
  public customerId: number;
  public portfolioIds: number[];
  public siteIds: number[] = []; // Changed to array like power chart
  public sortBy: number;
  public startDate?: Date | string;
  public endDate?: Date | string;
  public hideHealthySites?: boolean = false;
  public hideTicketedDevices?: boolean = false;
}

export interface PowerCardItem extends PowerCardsResponse {
  isLoading: boolean;
}

export class PowerCardsResponse {
  public siteId: number;
  public siteName: string;
  public acSize: number;
  public dcSize: number;
  public deviceData: DeviceRecords[];
  public alertDetails: AlertDetails[];
  public alertCount: number = 0;
}

export class DeviceRecords {
  public deviceId: number;
  public deviceName: string;
  public deviceTypeId: number;
  public binData: number;
  public binDataSum: number;
  public isDeviceAlert: boolean;
  public binDateTime: string;
  public ticketsData: TicketDetails[];
}

export class AlertDetails {
  public deviceName: string;
  public binData: number;
  public binDateTime: string;
  public deviceId: number;
  public ticketsData: TicketDetails[];
  public multipleAlertDate?: string[] = [];
  public isPowerChart: boolean = false;
}

export class TicketDetails {
  public ticketId: number = 0;
  public siteDeviceId: number = 0;
  public ticketNumber: string = '';
  public isProductionLoss: boolean = false;
}

export class CreatePowerAlertTickets {
  public siteDeviceIds: number[];
  public siteId: number;
  public ticketIssue: string;
  public openDate: string | Date;
}

export class CreatePowerAlertTicketsResponse {
  ticketId: number = 0;
  ticketNumber: string = '';
  siteDeviceIds: number[] = [];
}
