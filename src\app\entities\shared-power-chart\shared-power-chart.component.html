<div class="shared-power-chart-container" *ngIf="isValid">
  <nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large" class="h-100">
    <nb-card-header>
      <div class="d-flex flex-column">
        <h5 class="mb-0">{{ powerChartData.siteName || 'Power Chart' }}</h5>
        <h6 class="text-secondary">{{ powerChartData.customerName }} • {{ powerChartData.portfolioName }}</h6>
      </div>
    </nb-card-header>

    <nb-card-body>
      <div class="h-100 w-100" *ngIf="selectedSiteChart">
        <div echarts [options]="selectedSiteChart" [theme]="currentTheme" class="h-100 w-100"></div>
      </div>
    </nb-card-body>
  </nb-card>
</div>

<div class="error-message-container" *ngIf="!loading && !isValid">
  <nb-card>
    <nb-card-body>
      <div class="d-flex flex-column align-items-center jusify-content-center">
        <h4 class="title text-center w-75 mb-3">{{ message }}</h4>
        <button
          *ngIf="errorCode !== errorCodeEnum.MISSING_PERFORMANCE_INFO"
          nbButton
          (click)="goToHome()"
          type="button"
          class="home-button"
        >
          Take me home
        </button>
        <button
          *ngIf="errorCode === errorCodeEnum.MISSING_PERFORMANCE_INFO"
          nbButton
          (click)="goToSiteInfo()"
          type="button"
          class="home-button"
        >
          Take me to Site Info
        </button>
      </div>
    </nb-card-body>
  </nb-card>
</div>
