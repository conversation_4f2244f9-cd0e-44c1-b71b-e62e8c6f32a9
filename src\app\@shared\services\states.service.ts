import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiUrl } from '../constants';

@Injectable({
  providedIn: 'root'
})
export class StateService {
  constructor(private readonly http: HttpClient) {}

  getStates(forFilter = true): Observable<any> {
    return this.http.get(`${ApiUrl.GET_STATES}?isSiteAddUpdate=${forFilter}`);
  }
}
