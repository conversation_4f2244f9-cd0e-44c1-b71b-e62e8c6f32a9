import { Component, EventEmitter, Input, OnInit } from '@angular/core';
import { Dropdown } from '../../../../@shared/models/dropdown.model';
import { Subject, Subscription } from 'rxjs';
import { NotificationService } from '../notification.service';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { NotificationItem } from '../notification.model';
import { AlertService } from '../../../../@shared/services';

@Component({
  selector: 'sfl-notification-add-edit',
  templateUrl: './notification-add-edit.component.html',
  styleUrls: ['./notification-add-edit.component.scss']
})
export class NotificationAddEditComponent implements OnInit {
  @Input() notification: NotificationItem;
  @Input() isShowActiveToggle = false;
  loading = false;
  onClose: Subject<boolean> = new Subject();
  customerList: Array<Dropdown> = new Array<Dropdown>();
  portfolioList: Array<Dropdown> = new Array<Dropdown>();
  siteList: Array<Dropdown> = new Array<Dropdown>();
  includePhotos = false;
  isActive = true;
  isEdit = false;
  subscription: Subscription = new Subscription();
  event = new EventEmitter<any>();
  notificationTypeList: Array<Dropdown> = new Array<Dropdown>();
  globalOptions: any;

  constructor(
    private readonly notificationService: NotificationService,
    public readonly _bsModalRef: BsModalRef,
    private readonly alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.getNotificationTypes();
    this.getDropdownOptions();
    if (this.notification) {
      this.notification.includePhotos = this.notification.isIncludePhotos;
      this.isActive = this.notification.isActive;
      this.isEdit = true;
    } else {
      this.notification = new NotificationItem();
    }
  }

  getNotificationTypes(): void {
    this.loading = true;
    this.subscription.add(
      this.notificationService.getNotificationTypes().subscribe({
        next: (types: Dropdown[]) => {
          this.notificationTypeList = types;
          this.loading = false;
        },
        error: err => {
          this.loading = false;
        }
      })
    );
  }

  getDropdownOptions(): void {
    this.loading = true;
    this.subscription.add(
      this.notificationService.getDropdownOptions().subscribe({
        next: (options: any) => {
          this.globalOptions = options;
          this.customerList = options.customers;
          if (this.isEdit) {
            this.onCustomerSelectDeSelect();
          }
          this.loading = false;
        },
        error: err => {
          this.loading = false;
        }
      })
    );
  }

  onCustomerSelectDeSelect(): void {
    this.portfolioList = this.globalOptions.portfolios.filter(portfolio => portfolio.customerId === this.notification.customerId);
    const validPortfolioIds = this.portfolioList.map(p => +p.id);
    this.notification.portfolioIds = (this.notification.portfolioIds || []).filter(id => validPortfolioIds.includes(+id));
    this.onPortfolioSelectDeSelect();
  }

  onClearCustomer(): void {
    this.notification.portfolioIds = [];
    this.notification.siteIds = [];
    this.portfolioList = [];
    this.siteList = [];
  }

  onPortfolioSelectDeSelect(): void {
    if (this.notification.portfolioIds.length === 0 || !this.notification.portfolioIds) {
      this.siteList = [];
      this.notification.siteIds = null;
    }
    this.siteList = this.globalOptions.sites.filter(site => site.customerId === this.notification.customerId);
    this.siteList = this.siteList.filter(site => this.notification.portfolioIds.includes(+site.portfolioId));
    const validSiteIds = this.siteList.map(site => +site.id);
    this.notification.siteIds = (this.notification.siteIds || []).filter(id => validSiteIds.includes(+id));
  }

  onClearPortfolio(): void {
    this.notification.siteIds = [];
    this.siteList = [];
  }

  createNotification(notification: NotificationItem): void {
    this.loading = true;
    this.notification.isActive = true;
    this.subscription.add(
      this.notificationService.createNotification(notification).subscribe({
        next: res => {
          this.alertService.showSuccessToast(res.message);
          this.loading = false;
          this.onCancel();
          this.event.emit('refresh');
        },
        error: err => {
          this.loading = false;
        }
      })
    );
  }

  updateNotification(notification: NotificationItem): void {
    this.loading = true;
    if (!this.isShowActiveToggle) {
      this.notification.isActive = true;
    }
    this.subscription.add(
      this.notificationService.updateNotification(notification).subscribe({
        next: res => {
          this.alertService.showSuccessToast(res.message);
          this.loading = false;
          this.onCancel();
          this.event.emit('refresh');
        },
        error: err => {
          this.loading = false;
        }
      })
    );
  }

  onSubmit(): void {
    this.notification.isActive = this.isActive;
    delete this.notification.isIncludePhotos;
    if (this.isEdit) {
      this.updateNotification(this.notification);
    } else {
      this.createNotification(this.notification);
    }
  }

  onCancel(): void {
    this.onClose.next(false);
    this._bsModalRef.hide();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
