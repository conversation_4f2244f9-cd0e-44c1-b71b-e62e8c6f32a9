import { Location } from '@angular/common';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot } from '@angular/router';
import { AppConstants } from '../constants';
import { StorageService } from './storage.service';
import { AUTHORITY_ROLE_STRING, ROLE_TYPE } from '../enums';

@Injectable({
  providedIn: 'root'
})
/* SCENARIOS:
    if user is other than customer and accessing view mode allowed
    if user is other than customer and accessing edit mode allowed
    if user is customer and accessing view mode allowed
    if user is customer and accessing edit mode redirect
*/
export class UpdateResourcesGuard implements CanActivate {
  allowed: boolean = true;
  constructor(private readonly storageService: StorageService, private readonly location: Location) {}
  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    const token = this.storageService.get(AppConstants.authenticationToken),
      tokenData = token.split('.')[1],
      decodedTokenJSONData = window.atob(tokenData),
      decodedTokenData = JSON.parse(decodedTokenJSONData),
      currentAccessMode = next.params.mode;

    if (
      decodedTokenData.role === AUTHORITY_ROLE_STRING[ROLE_TYPE.CUSTOMER] &&
      (currentAccessMode === 'edit' || state.url.includes('edit'))
    ) {
      this.location.back();
      this.allowed = false;
    }
    return this.allowed;
  }
}
