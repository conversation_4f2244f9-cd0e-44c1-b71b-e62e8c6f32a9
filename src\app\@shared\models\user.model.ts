import { AppConstants } from '../constants';
import { AUTHORITY_ROLE_STRING, ROLE_TYPE } from '../enums';
import { Dropdown } from './dropdown.model';

export class User {
  public id: number;
  public firstName: string;
  public lastName: string;
  public email: string;
  public userName?: string;
  public phoneNo?: string;
  public roles: string[];
  public isActive = true;
  public Role: string;
  public portfolioAccess: number[] = [];
  public assignment: number[] = [];
  public siteAccess: number[] = [];
  public appSettings: AppSettings;
  public portfolioNames: string[] = [];
  public portfolioCount: number;
  public userTimeZone = 0;
  public state: string;
  public zipCode: string;
  public fullName: string;
  public lastLogin: string;
  public isAllPortfolioSite: boolean;
  public isAllPortfolio: boolean;
  public allowNotification: boolean;
  public profileImage: string;
  public profileBackgroundColor: string;
  public authorities: string[];
  public authorisationIds: ROLE_TYPE[];
  public userId: number;
  public checkedInSiteId: number;
  public userCheckedInId: string;
  public twoFactorEnabled: boolean = false;
  public isRequiredSecurityQuestion: boolean;
  public reason: string = '';
  public MFAPassword: string = '';
  public companyName: string;
  public jobTitle: string;
  public departmentName: string;
  public userCertificates: UserCertificate[] = [];
}

export class UserAuthorisation {
  public authorities: string[];
  public authorisationIds: ROLE_TYPE[];

  constructor(authorities: string[]) {
    this.authorities = authorities;
    this.authorisationIds = authorities.map(item => {
      const key = Object.keys(AUTHORITY_ROLE_STRING).find(key => AUTHORITY_ROLE_STRING[key] === item);
      return Number(key);
    });
  }
}

export class UserCertificate {
  public id: number = 0;
  public certificateName: string = '';
  public isDeleted: boolean = false;
  public isUpdated: boolean = false;
}

export class SecurityQuestionAnswer {
  public questionID: number;
  public question: string;
  public questionAnswer: string;
  public questionAnswerStr: string;
  public questionAnswerId: number;
  public userId: number;
  public hideInputValues: boolean;
  public needToResetAnswer: boolean;

  constructor(securityQuestionAnswer: Partial<SecurityQuestionAnswer>) {
    this.questionID = securityQuestionAnswer?.questionID ?? null;
    this.question = securityQuestionAnswer?.question ?? '';
    this.questionAnswer = securityQuestionAnswer?.questionAnswer ?? '';
    this.questionAnswerStr = securityQuestionAnswer?.questionAnswerStr ?? '';
    this.questionAnswerId = securityQuestionAnswer?.questionAnswerId ?? 0;
    this.userId = securityQuestionAnswer?.userId ?? null;
    this.hideInputValues = securityQuestionAnswer?.hideInputValues ?? true;
    this.needToResetAnswer = securityQuestionAnswer?.needToResetAnswer ?? false;
  }
}

export class SecurityQuestionItem {
  public questionID: number = null;
  public question: string = '';
}

export class SiteDropdown {
  public isActive: boolean;
  public ids: number[];
}

export class userAvatarParams {
  public id: number;
  public profileFileImage = null;
  public profileImage: string = '';
  public profileBackgroundColor: string = '';
}

export class DataSourceDetail {
  public id: number;
  public partnerNumber: string;
  public partnerName: string;
  public automationDataSourceId: number;
  public dataSourceName: string;
  public siteCount: number;
  public deviceCount: number;
}
export class UserFilterData {
  public totalUsers: number;
  public twoFactorEnabledForAllUser: boolean;
  public users: User[];
}

export class NewPassword {
  public userId: number;
  public newPassword: string;
  public confirmPassword: string;
}

export class AppSettings {
  public alertMessage: string;
  public alertMessageVersion: string;
  public androidVersion: string;
  public androidVersionDetail: number;
  public iosVersion: string;
  public iosVersionDetail: string;
  public mobileApiVersion: string;
  public webApiVersion: string;
  public webPortalVersion: string;
}

export class UserFilter {
  public search: string;
  public page = 0;
  public sortBy = 'FirstName';
  public direction = 'asc';
  public itemsCount = +AppConstants.rowsPerPage;
}

export class UserDefaultFilter {
  public id: number;
  public portfolioIds: number[] = [];
  public siteIds: number[] = [];
  public regionIds: number[] = [];
  public subRegionIds: number[] = [];
  public stateIds: number[] = [];
  public states: string[] = [];
  public userId: number;
  public isAllSiteSelected: boolean = true;
}

export const USERFILTERLIST = {
  search: 'Search'
};

export class TimeZone {
  public id: number;
  public name: string;
  public offset: string;
  public isActive: boolean;
  public abbreviation: string;
  public abbr: string;
  public text: string;
  public value: string;
}

export class UserNotificationPreferences {
  userNotificationPreferenceId: null = null;
  notificationPreference: boolean = false;
  isAllSiteSelected: boolean = false;
  isAllowTicketNotification: boolean = false;
  isAllowWorkOrderNotification: boolean = false;
  isAllowAlertNotification: boolean = false;
  isAllowSiteCheckInNotification: boolean = false;
  listOfPortfolioId: number[] = [];
  listOfSiteId: number[] = [];
}

export class AllNotificationModal {
  public notificationId: number;
  public userNotificationId: number;
  public entityId: string;
  public userId: number;
  public portfolioId: number;
  public siteId: number;
  public customerId: number;
  public triggerId: number;
  public auditActionId: number;
  public title: string;
  public description: string;
  public ticketNumber: string;
  public assessmentType: string;
  public frequencyType: string;
  public trigger: string;
  public notificationDate: string;
  public isRead: boolean;
  public readDate: string;
  public user: string;
  public isUnread?: boolean;
  public isDeleted: boolean;
  public bgColor?: string;
  public isMoreDetails?: boolean;
  public profileBackgroundColor?: string;
}
export class AllowNotificationParams {
  public id: number;
  public allowNotification: boolean;
}

export class EnableDisabledTwoFactorParams {
  public id: number;
  public twoFactorEnabled: boolean;
  public isBulkApply: boolean;
  public reason: string;
  public MFAPassword: string;
}

export class ActionSummaryValues {
  public New: string;
  public Old: string;
}

export class NotificationActionSummary {
  public Label: string;
  public Order: number;
  public IsAssignToMe: boolean;
  public Href: string;
  public Value: ActionSummaryValues;
}
export const appInitialsBgColors = [
  {
    bgColor: '#0052CC',
    isSelected: true,
    color: '#fff'
  },
  {
    bgColor: '#00A3BF',
    isSelected: false,
    color: '#172B4D'
  },
  {
    bgColor: '#00875A',
    isSelected: false,
    color: '#fff'
  },
  {
    bgColor: '#FF991F',
    isSelected: false,
    color: '#172B4D'
  },
  {
    bgColor: '#DE350B',
    isSelected: false,
    color: '#fff'
  },
  {
    bgColor: '#5243AA',
    isSelected: false,
    color: '#fff'
  }
];

export class UserAuthenticationAuditLogDetails {
  fieldName: string;
  oldValue: string;
  newValue: string;
}

export class UserAuthenticationAuditLog {
  auditId: number;
  customerId: number;
  portfolioId: number;
  siteId: number;
  entityId: number;
  parentId: number;
  action: string;
  userName: string;
  logDate: string;
  auditLogDetails: UserAuthenticationAuditLogDetails[];
}

export interface UserFiltersResponse {
  lstUserNames: string[];
  lstEmails: string[];
  lstCompanyNames: string[];
  lstUserTypes: Dropdown[];
}

export class UserFilters {
  userNames: Dropdown[];
  userEmails: Dropdown[];
  companyNames: Dropdown[];
  userTypes: Dropdown[];

  private addIncrementalIndex = (filterData: string[] | Dropdown[]) => {
    return filterData.map((item, index) => ({
      id: item.id ?? index + 1,
      name: item.name ?? item
    }));
  };

  constructor(userNames: string[] = [], userEmails: string[] = [], companyNames: string[] = [], userTypes: Dropdown[] = []) {
    this.userNames = this.addIncrementalIndex(userNames);
    this.userEmails = this.addIncrementalIndex(userEmails);
    this.companyNames = this.addIncrementalIndex(companyNames);
    this.userTypes = this.addIncrementalIndex(userTypes);
  }
}

export enum USER_STATUS_ENUM {
  ACTIVE_AND_INACTIVE = 3,
  ACTIVE = 1,
  INACTIVE = 2
}

const USER_STATUS_LABEL_ENUM = {
  [USER_STATUS_ENUM.ACTIVE_AND_INACTIVE]: 'All',
  [USER_STATUS_ENUM.ACTIVE]: 'Active',
  [USER_STATUS_ENUM.INACTIVE]: 'In-Active'
};

export const USER_STATUS_LIST = [
  {
    id: USER_STATUS_ENUM.ACTIVE_AND_INACTIVE,
    name: USER_STATUS_LABEL_ENUM[USER_STATUS_ENUM.ACTIVE_AND_INACTIVE]
  },
  {
    id: USER_STATUS_ENUM.ACTIVE,
    name: USER_STATUS_LABEL_ENUM[USER_STATUS_ENUM.ACTIVE]
  },
  {
    id: USER_STATUS_ENUM.INACTIVE,
    name: USER_STATUS_LABEL_ENUM[USER_STATUS_ENUM.INACTIVE]
  }
];
