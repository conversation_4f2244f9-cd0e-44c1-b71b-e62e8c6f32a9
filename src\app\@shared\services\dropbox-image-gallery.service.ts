import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ImageGalleryListResponse, TagListResponseModel, UploadImageReqParams } from '../components/image-dropbox-gallery/drop-box.model';
import { ApiUrl } from '../constants';
import { MessageVM } from '../models/messageVM.model';

@Injectable({
  providedIn: 'root'
})
export class DropboxImageGalleryService {
  constructor(private readonly http: HttpClient) {}

  getGalleryImageFiles(modal, isTruckRollGallery = false): Observable<ImageGalleryListResponse> {
    return this.http.post<ImageGalleryListResponse>(isTruckRollGallery ? ApiUrl.TRUCK_ROLL_GALLERY : ApiUrl.GET_GALLERY_IMAGE_FILES, modal);
  }

  getDevicesTagList(): Observable<TagListResponseModel[]> {
    return this.http.get<TagListResponseModel[]>(`${ApiUrl.GET_DEVICES_TAGS_LIST}`);
  }

  getConditionalTagList(): Observable<TagListResponseModel[]> {
    return this.http.get<TagListResponseModel[]>(`${ApiUrl.GET_CONDITIONAL_TAGS_LIST}`);
  }

  uploadFilesToGallery(reqParams: FormData): Observable<UploadImageReqParams> {
    return this.http.post<UploadImageReqParams>(ApiUrl.UPLOAD_FILES_TO_GALLERY, reqParams);
  }

  uploadVideoGalleryFiles(reqParams: FormData): Observable<UploadImageReqParams> {
    return this.http.post<UploadImageReqParams>(ApiUrl.UPLOAD_GALLERY_VIDEOS, reqParams);
  }

  applyTagsToImages(tagParams: any): Observable<any> {
    return this.http.post<any>(ApiUrl.APPLY_IMAGE_TAGS, tagParams);
  }

  downloadPreviewedImage(imageId): Observable<Blob> {
    return this.http.get<Blob>(`${ApiUrl.DOWNLOAD_PREVIEW_IMAGE}/${imageId}`, { responseType: 'blob' as 'json' });
  }

  downloadAsFolder(params): Observable<Blob> {
    return this.http.post<Blob>(`${ApiUrl.DOWNLOAD_AS_FOLDER}`, params, { responseType: 'blob' as 'json' });
  }

  downloadAsAllGalleryImages(params): Observable<any> {
    return this.http.post<Blob>(`${ApiUrl.DOWNLOAD_ALL_GALLERY_IMAGES}`, params);
  }

  deleteImageGalleryFiles(fileId: number): Observable<MessageVM> {
    return this.http.delete<MessageVM>(`${ApiUrl.DELETE_GALLERY_FILES}?fileId=${fileId}`);
  }

  deleteMultipleImageGalleryFiles(params): Observable<MessageVM> {
    return this.http.post<MessageVM>(`${ApiUrl.DELETE_MULTIPLE_IMAGES_FILES}`, params);
  }

  getFileTagList(): Observable<TagListResponseModel[]> {
    return this.http.get<TagListResponseModel[]>(`${ApiUrl.GET_FILES_TAGS_LIST}`);
  }

  updateDocumentsFilesTags(reqParams: any): Observable<any> {
    return this.http.post<any>(ApiUrl.UPDATE_FILES_ATTACHMENTS, reqParams);
  }

  applyTagsToFiles(tagParams: any): Observable<any> {
    return this.http.post<any>(ApiUrl.UPDATE_BULK_FILES_TAGS, tagParams);
  }

  uploadChunk(
    chunk: Blob,
    fileName: string,
    isChunkUpload: boolean,
    totalChunk: number,
    currentPart: number,
    fileUploadTimeStamp: number
  ): Observable<any> {
    const formData = new FormData();
    if (isChunkUpload) {
      formData.append('file', chunk, fileName);
      formData.append('fileName', fileName);
      formData.append('IsLargeFile', `${isChunkUpload}`);
      formData.append('totalPart', `${totalChunk}`);
      formData.append('currentPart', `${currentPart}`);
      formData.append('fileUploadTimeStamp', `${fileUploadTimeStamp}`);
    } else {
      formData.append('file', chunk, fileName);
      formData.append('fileName', fileName);
    }

    return this.http.post<any>(ApiUrl.FILE_UPLOAD_CHUNK, formData);
  }
}
