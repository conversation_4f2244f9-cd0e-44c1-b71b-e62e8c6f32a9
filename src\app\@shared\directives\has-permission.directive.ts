import { ChangeDetectorRef, Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { checkAuthorisations } from '../utils';
import { ROLE_TYPE } from '../enums';

@Directive({
  selector: '[appHasPermission]'
})
export class HasPermissionDirective {
  constructor(private templateRef: TemplateRef<any>, private viewContainerRef: ViewContainerRef, private cdf: ChangeDetectorRef) {}

  @Input()
  set appHasPermission(allowedRoles: ROLE_TYPE | ROLE_TYPE[]) {
    const allowedRoleNameStrs = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
    const isAuthorised = checkAuthorisations(allowedRoleNameStrs);
    if (isAuthorised) {
      this.viewContainerRef.createEmbeddedView(this.templateRef);
      return;
    }
    this.viewContainerRef.clear();
  }
}
