export class AppConstants {
  public static readonly authenticationToken = 'authenticationToken';
  public static readonly refreshToken = 'refreshToken';
  public static readonly isMFAUserLoggedIn = 'isMFAUserLoggedIn';
  public static readonly qeAnalyticsPasswordAuthKey = 'qeAnalyticsPasswordAuthKey';
  public static readonly qeAnalyticsSessionIdKey = 'qeAnalyticsSessionIdKey';
  public static readonly qeMenuSubmenuItemListKey = 'qeMenuSubmenuItemListKey';
  public static readonly qeMenuEventTypeItemListKey = 'qeMenuEventTypeEnumListKey';
  public static readonly SHARED_FILTER_KEY = 'SharedFilterData';
  public static readonly userKey = 'user';
  public static readonly userAuthorisations = 'userAuthorisations';
  public static readonly userPermissions = 'userPermissions';
  public static readonly isForcedToChangePasswordKey = 'isForcedToChangePassword';
  public static readonly rowsPerPage = 100;
  public static readonly phoneNumberMask = '(*************';
  public static readonly ASC = 'asc';
  public static readonly DESC = 'desc';
  public static readonly pointRadius = 38;
  public static readonly piNumber = 2;
  public static readonly addAction = 2;
  public static readonly deleteAction = 0;
  public static readonly closeAction = 1;
  public static readonly xCoordinate = 0;
  public static readonly yCoordinate = 2;
  public static readonly fillStyleCircle = '#FF9800';
  public static readonly fillStyleText = 'black';
  public static readonly ctxDimension = '2d';
  public static readonly strokeWidth = 4;
  public static readonly ctxFont = 26;
  public static readonly ctxTextAlign = 'center';
  public static readonly appSettings = 'appSettings';
  public static readonly jhaDatedMultiple = 6;
  public static readonly pointRectRadius = 7;
  public static readonly ctxTextBaseline = 'middle';
  public static readonly setLinedash = 16;
  public static readonly setLinegap = 6;
  public static readonly lineWidth = 7;
  public static readonly strokeLineColor = '#FF9800';
  public static readonly fillMarkStyleCircle = '#FF9800';
  public static readonly momentDateFormat = 'MM/DD/YYYY';
  public static readonly momentDateTimeFormat = 'MM/DD/YYYY, h:mm a';
  public static readonly dateTimeFormat = 'MM/dd/yyyy, h:mm a';
  public static readonly powerChartSiteDateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  public static readonly powerChartSiteTimeFormat = 'h:mm a';
  public static readonly fullDateFormat = 'MM/dd/yyyy';
  public static readonly isPerviousPageTicketCreate = 'isPerviousPageTicketCreate';
  public static readonly skeletonLoaderImage = 'assets/images/skeleton-loader-images.gif';
  public static readonly previewExpandIcon = 'assets/images/preview-expand-icon.png';
  public static readonly allowedDocumentsType = ['doc', 'docx', 'pdf', 'txt', 'xls', 'xlsx', 'csv', 'ppt', 'pptx', 'pvapx'];
  public static readonly allowedDropboxVideos = ['mp4', 'mov', 'avi', 'mkv', 'WEBM', 'FLV', 'WMV', 'ats', 'pvapx'];
  public static readonly allowedDropboxImages = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
    'tif',
    'avif',
    'svg',
    'heic/heics',
    'heic',
    'heics',
    'heif',
    'bmp',
    'tiff',
    'psd',
    'ai',
    'indd',
    'raw',
    'RW2'
  ];
  public static readonly regex =
    'https?://(?:www.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9].[^s]{2,}|www.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9].[^s]{2,}|https?://(?:www.|(?!www))[a-zA-Z0-9]+.[^s]{2,}|www.[a-zA-Z0-9]+.[^s]{2,}';

  public static readonly FILTERS = {
    CUSTOMER: {
      name: 'Customer',
      multi: false,
      show: false
    },
    PORTFOLIO: {
      name: 'Portfolio',
      multi: false,
      show: false
    },
    SITE: {
      name: 'Site',
      multi: false,
      show: false
    },
    CREW_LEAD: {
      name: 'Crew Lead',
      multi: false,
      show: false
    },
    CREW_MEMBER: {
      name: 'Crew Member',
      multi: false,
      show: false
    },
    PRIORITY: {
      name: 'Priority',
      multi: false,
      show: false
    },
    STATE: {
      name: 'State',
      multi: false,
      show: false
    },
    STATUS: {
      name: 'Status',
      multi: false,
      show: false
    },
    OPEN_DATE: {
      name: 'Open',
      multi: false,
      show: false
    },
    CLOSE_DATE: {
      name: 'Closed',
      multi: false,
      show: false
    },
    EXCLUSION_TO: {
      name: 'Exclusion To',
      multi: false,
      show: false
    },
    EXCLUSION_FROM: {
      name: 'Exclusion From',
      multi: false,
      show: false
    },
    REPORT_TITLE: {
      name: 'Report Title',
      multi: false,
      show: false
    },
    MINIMUM_AFFECTED_KW: {
      name: 'Minimum Affected Kw',
      multi: false,
      show: false
    },
    QE_TECH: {
      name: 'QE Tech',
      multi: false,
      show: false
    },
    REGION: {
      name: 'Region',
      multi: false,
      show: false
    },
    SUB_REGION: {
      name: 'Subregion',
      multi: false,
      show: false
    },
    USER: {
      name: 'User',
      multi: false,
      show: false
    },
    SITE_AUDIT_REPORT_CUSTOMER: {
      name: 'Customer',
      multi: false,
      show: false
    },
    SITE_AUDIT_REPORT_PORTFOLIO: {
      name: 'Portfolio',
      multi: false,
      show: false
    },
    SITE_AUDIT_REPORT_SITE: {
      name: 'Site',
      multi: false,
      show: false
    },
    SITE_AUDIT_CUSTOMER: {
      name: 'Customer',
      multi: false,
      show: false
    },
    SITE_AUDIT_PORTFOLIO: {
      name: 'Portfolio',
      multi: false,
      show: false
    },
    SITE_AUDIT_SITE: {
      name: 'Site',
      multi: false,
      show: false
    },

    SITE_AUDIT_CREW_MEMBER: {
      name: 'Crew Lead/ Crew Member',
      multi: false,
      show: false
    },
    START_YEAR: {
      name: 'Year',
      multi: false,
      show: false
    },
    ASSESSMENT_TYPE: {
      name: 'Assessment Type',
      multi: false,
      show: false
    },
    REPORT_STATUS: {
      name: 'Report Status',
      multi: false,
      show: false
    },
    WORKORDER_STATUS: {
      name: 'Workorder Status',
      multi: false,
      show: false
    },
    FREQUENCY_TYPE: {
      name: 'Frequency Type',
      multi: false,
      show: false
    },
    ARRAY_TYPE: {
      name: 'Array Type',
      multi: false,
      show: false
    },
    SEARCH_BOX: {
      name: 'Search',
      multi: false,
      show: false
    },
    WORK_ORDER: {
      name: 'Work Order',
      multi: false,
      show: false
    },
    LINKED_TICKET: {
      name: 'Linked Ticket',
      multi: false,
      show: false
    },
    DEVICE_TYPE: {
      name: 'Device Type',
      multi: false,
      show: false
    },
    MFG: {
      name: 'Mfg',
      multi: true,
      show: false
    },
    MODEL: {
      name: 'Model',
      multi: true,
      show: false
    },
    DEVICE: {
      name: 'Device',
      multi: false,
      show: false
    },
    SHOW_DELETED: {
      name: 'Show deleted',
      multi: false,
      show: false
    },
    IS_LINK: {
      name: 'Linked',
      multi: false,
      show: false
    },
    SHOW_ARCHIVED: {
      name: 'Show archived',
      multi: false,
      show: false
    },
    TRUCK_ROLL: {
      name: 'Truck Roll',
      multi: false,
      show: false
    },
    RESOLVED: {
      name: 'Resolved',
      multi: false,
      show: false
    },
    RMA_COMPLETE: {
      name: 'Complete',
      multi: false,
      show: false
    },
    RMA_TRACKING: {
      name: 'Tracking',
      multi: false,
      show: false
    },
    RMA_RETURN_REQUIRED: {
      name: 'Tracking',
      multi: false,
      show: false
    },
    ACTIVITY_RANGE: {
      name: 'Activity Range',
      multi: false,
      show: false
    },
    ACTIVITY_START: {
      name: 'Activity Range Start',
      multi: false,
      show: false
    },
    DATE: {
      name: 'Date',
      multi: false,
      show: false
    },
    ACTIVITY_END: {
      name: 'Activity Range End',
      multi: false,
      show: false
    },
    SHOW_STATUS: {
      name: 'Status',
      multi: false,
      show: false
    },
    SHOW_NERC: {
      name: 'NERC ',
      multi: false,
      show: false
    },
    SHOW_NERC_SITE_TYPE: {
      name: 'NERC',
      multi: false,
      show: false
    },
    AUTOMATION_DATA_SOURCE: {
      name: 'Data Source',
      multi: true,
      show: false
    },
    AUTOMATION_SITE: {
      name: 'Automation Site',
      multi: true,
      show: false
    },
    CHECK_IN_ONLY: {
      name: 'onlyCheckIn',
      multi: true,
      show: false
    },
    IS_RESCHEDULED: {
      name: 'Rescheduled',
      multi: false,
      show: false
    },
    IS_UNSCHEDULED: {
      name: 'Unscheduled',
      multi: false,
      show: false
    },
    IS_TENTATIVE_MONTH: {
      name: 'Tentative Month',
      multi: false,
      show: false
    },
    FIELD_TECH_IDS: {
      name: 'Field Tech',
      multi: true,
      show: false
    },
    TICKET_ESTIMATION_STATUS: {
      name: 'Estimation Status',
      multi: false,
      show: false
    },
    START_DATE: {
      name: 'Start Date',
      multi: false,
      show: false
    },
    END_DATE: {
      name: 'End Date',
      multi: false,
      show: false
    },
    TEMPLATE_TYPE_IDS: {
      name: 'Template type',
      multi: false,
      show: false
    },
    TEMPLATE_TYPE_FORM_LIST_IDS: {
      name: 'Template Type',
      multi: false,
      show: false
    },
    EQUIPMENT_LIST: {
      name: 'Equipment',
      multi: false,
      show: false
    },
    BILLING_STATUS: {
      name: 'Billing Status'
    },
    CONTROL_TYPE_IDS: {
      name: 'Control type',
      multi: false,
      show: false
    },
    CONTROL_DATA_TYPE_IDS: {
      name: 'Tag type',
      multi: false,
      show: false
    },
    TICKET_TYPE: {
      name: 'Ticket Type',
      multi: false,
      show: false
    },
    COSTS_TYPE: {
      name: 'Costs type',
      multi: false,
      show: false
    },
    SITE_AUDIT_REPORT_STATUS: {
      name: 'Status ',
      multi: false,
      show: false
    },
    QE_SERVICE_TYPE: {
      name: 'Service Type',
      multi: false,
      show: false
    },
    USER_TYPE: {
      name: 'User Type',
      multi: false,
      show: false
    },
    QE_MODULES: {
      name: 'Modules',
      multi: false,
      show: false
    },
    QE_DATE_DURATION: {
      name: 'Duration',
      multi: false,
      show: false
    },
    USER_NAME: {
      name: 'Name',
      multi: false,
      show: false
    },
    USER_EMAIL: {
      name: 'Email',
      multi: false,
      show: false
    },
    USER_COMPANY: {
      name: 'Company',
      multi: false,
      show: false
    },
    USER_STATUS: {
      name: 'User Status',
      multi: false,
      show: false
    },
    IS_ACTIVE_NOTIFICATION: {
      name: 'Active',
      multi: false,
      show: false
    },
    IS_INACTIVE_MISSING_CONFIG_NOTIFICATION: {
      name: 'Missing Config/Inactive',
      multi: false,
      show: false
    }
  };

  public static readonly FILTER_TITLES = {
    CUSTOMER: 'Customer',
    PORTFOLIO: 'Portfolio',
    SITE: 'Site',
    SITE_AUDIT_REPORT_CUSTOMER: 'Site Audit Report Customer',
    SITE_AUDIT_REPORT_PORTFOLIO: 'Site Audit Report Portfolio',
    SITE_AUDIT_REPORT_SITE: 'Site Audit Report Site',
    CREW_LEAD: 'Crew Lead',
    CREW_MEMBER: 'Crew Member',
    TICKET_NUMBER: 'Number',
    PRIORITY: 'Priority',
    STATE: 'State',
    STATUS: 'Status',
    OPEN_DATE: 'Opened',
    CLOSE_DATE: 'Closed',
    EXCLUSION_TO: 'Exclusion To',
    EXCLUSION_FROM: 'Exclusion From',
    MINIMUM_AFFECTED_KW: 'Minimum Affected Kw',
    QE_TECH: 'QE Tech',
    REGION: 'Region',
    SUB_REGION: 'Subregion',
    USER: 'User',
    START_YEAR: 'Year',
    ASSESSMENT_TYPE: 'Assessment Type',
    REPORT_STATUS: 'Report Status',
    WORKORDER_STATUS: 'Workorder Status',
    FREQUENCY_TYPE: 'Frequency Type',
    ARRAY_TYPE: 'Array Type',
    SEARCH_BOX: 'Search',
    WORK_ORDER: 'Work Order',
    LINKED_TICKET: 'Linked Ticket',
    DEVICE_TYPE: 'Device Type',
    MFG: 'Mfg',
    MODEL: 'Model',
    DEVICE: 'Device',
    SHOW_DELETED: 'Show Deleted',
    IS_LINK: 'Linked',
    CHECK_IN_ONLY: 'onlyCheckIn',
    SHOW_ARCHIVED: 'Show Archived',
    ACTIVITY_START: 'Activity Range Start',
    ACTIVITY_RANGE: 'Activity Range',
    DATE: 'Date',
    ACTIVITY_END: 'Activity Range End',
    TRUCK_ROLL: 'Truck Roll',
    RESOLVED: 'Resolved',
    RMA_COMPLETE: 'Complete',
    RMA_TRACKING: 'Tracking',
    RMA_RETURN_REQUIRED: 'Tracking',
    SHOW_STATUS: 'Show Status',
    SHOW_NERC: 'NERC ',
    SHOW_NERC_SITE_TYPE: 'NERC',
    SITE_AUDIT_CUSTOMER: 'Site Audit Customer',
    SITE_AUDIT_PORTFOLIO: 'Site Audit Portfolio',
    SITE_AUDIT_SITE: 'Site Audit Site',
    SITE_AUDIT_CREW_MEMBER: 'Site Audit Crew Member',
    AUTOMATION_DATA_SOURCE: 'Data Source',
    AUTOMATION_SITE: 'Automation Site',
    IS_INCLUSIVE_SEARCH: 'Inclusive Search',
    IS_EXCLUSIVE_SEARCH: 'Exclusive Search',
    IS_RESCHEDULED: 'Rescheduled',
    IS_UNSCHEDULED: 'Unscheduled',
    IS_TENTATIVE_MONTH: 'Tentative Month',
    FIELD_TECH_IDS: 'Field Tech',
    TICKET_ESTIMATION_STATUS: 'Estimation Status',
    START_DATE: 'Start Date',
    END_DATE: 'End Date',
    TEMPLATE_TYPE_IDS: 'Template type',
    TEMPLATE_TYPE_FORM_LIST_IDS: 'Template Type',
    EQUIPMENT_LIST: 'Equipment',
    BILLING_STATUS: 'Billing Status',
    CONTROL_TYPE_IDS: 'Control type',
    CONTROL_DATA_TYPE_IDS: 'Tag type',
    TICKET_TYPE: 'Ticket Type',
    COSTS_TYPE: 'Costs type',
    SITE_AUDIT_REPORT_STATUS: 'Site Audit Report Status',
    QE_SERVICE_TYPE: 'Service Type',
    USER_TYPE: 'User Type',
    QE_MODULES: 'Modules',
    QE_DATE_DURATION: 'Duration',
    USER_NAME: 'Name',
    USER_EMAIL: 'Email',
    USER_COMPANY: 'Company',
    USER_STATUS: 'User Status',
    IS_ACTIVE_NOTIFICATION: 'Active',
    IS_INACTIVE_MISSING_CONFIG_NOTIFICATION: 'Missing Config/Inactive'
  };
  public static readonly encryptUserId = 'encryptUserId';
}
