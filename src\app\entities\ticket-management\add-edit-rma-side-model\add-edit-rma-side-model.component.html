<div class="alert-box">
  <form [formGroup]="addUpdateRMAForm" aria-labelledby="title" autocomplete="off">
    <div class="modal-header">
      <div class="d-flex align-items-center w-100">
        <h6>{{ mode === 'create' ? 'Add RMA' : 'Edit RMA' }}</h6>
        <div class="ms-auto d-flex align-items-center">
          <div class="me-3">
            <nb-toggle status="primary" formControlName="isActive" labelPosition="start" (checkedChange)="onRMAToggleChange($event)">
            </nb-toggle>
          </div>
          <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
            <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="modal-body ModalBody rma-detail-modal-body"
      [nbSpinner]="loading"
      nbSpinnerStatus="primary"
      nbSpinnerSize="large"
      [ngClass]="{ 'disable-field': !isActive }"
    >
      <div>
        <div class="row">
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-exclusionFrom">Device</label>
            <ng-select
              name="device"
              [items]="deviceArray"
              bindLabel="name"
              bindValue="name"
              formControlName="deviceName"
              notFoundText="No Device Found"
              placeholder="Select Device"
              (change)="getManufacturerName($event.siteDeviceId)"
              [closeOnSelect]="true"
            >
            </ng-select>
            <sfl-error-msg [control]="addUpdateRMAForm?.controls?.deviceName" fieldName="Device Name"></sfl-error-msg>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-exclusionFrom">Manufacturer Name</label>
            <input nbInput type="text" fullWidth formControlName="deviceMFG" class="form-control mfg-name" disabled />
            <sfl-error-msg [control]="addUpdateRMAForm.controls.deviceMFG" fieldName="Manufacturer Name"></sfl-error-msg>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-Portfolio">Is Equipment Return Required?</label>
            <div>
              <nb-radio-group class="d-flex" name="EquipmentReturnRequired" formControlName="isReturnRequired">
                <nb-radio [value]="1">Yes</nb-radio>
                <nb-radio [value]="0">No</nb-radio>
              </nb-radio-group>
            </div>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <div class="form-control-group">
              <label class="label">RMA Number</label>
              <input nbInput type="text" formControlName="rmaNumber" class="form-control" fullWidth />
              <sfl-error-msg [control]="addUpdateRMAForm?.controls?.rmaNumber" fieldName="RMA Number"></sfl-error-msg>
            </div>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-name"> RMA Start Date</label>
            <input
              nbInput
              placeholder="Start Date"
              autocomplete="off"
              formControlName="startDate"
              class="form-control"
              fullWidth
              [nbDatepicker]="dateTimePicker"
            />
            <nb-datepicker #dateTimePicker [max]="addUpdateRMAForm.get('completeDate').value"></nb-datepicker>
            <sfl-error-msg [control]="addUpdateRMAForm.controls.startDate" fieldName="RMA Start Date"></sfl-error-msg>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="label" for="input-name"> RMA Complete Date </label>
            <input
              nbInput
              placeholder="Complete Date"
              autocomplete="off"
              formControlName="completeDate"
              fullWidth
              class="form-control"
              [nbDatepicker]="dateTimePicker1"
            />
            <nb-datepicker #dateTimePicker1 [min]="addUpdateRMAForm.get('startDate').value"></nb-datepicker>
            <sfl-error-msg [control]="addUpdateRMAForm.controls.completeDate" fieldName="RMA Complete Date"></sfl-error-msg>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <nb-toggle status="primary" formControlName="isRMAComplete" labelPosition="start"
              ><label class="label">RMA Complete? </label></nb-toggle
            >
          </div>
        </div>
        <div class="row border-top">
          <div class="col-12 col-md-6 mb-3 pt-3">
            <div>
              <label class="label">Device Serial Number</label>
              <input nbInput type="text" formControlName="deviceSerialNumber" class="form-control" fullWidth />
            </div>
          </div>
          <div class="col-12 col-md-6 mb-3 pt-3">
            <div class="dropZone" ngFileDragDrop (fileDropped)="getUploadedFiles($event, true)">
              <input type="file" #file (change)="getUploadedFiles($event.target.files, true)" [disabled]="!isActive" />
              <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
              <h5 class="fw-bold">Drag & Drop file to attach</h5>
              <label style="text-transform: none" class="fw-bold">or Browse to choose a file </label>
            </div>
          </div>
          <div class="col-12 mb-3 pt-3">
            <div class="row">
              <div class="col-12 col-md-4">
                <span class="d-flex align-items-center justify-content-center">Photos</span>
                <div class="image-container" *ngIf="deviceSerialImageFiles && !deviceSerialImageFiles.isDeleted">
                  <img [src]="deviceSerialImageFiles.documentUrl" (click)="imagePopup(deviceSerialImageFiles)" />
                  <div class="d-flex mt-2" *ngIf="deviceSerialImageFiles">
                    <label
                      class="imageFilename cursor-pointer"
                      nbTooltip="{{ deviceSerialImageFiles?.fileName }}"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      >{{ deviceSerialImageFiles?.fileName }}</label
                    >
                    <span class="ms-auto cursor-pointer">
                      <em
                        *ngIf="mode !== 'create'"
                        nbtooltip="Download"
                        nbtooltipplacement="top"
                        nbtooltipstatus="text-primary"
                        aria-hidden="true"
                        class="fa fa-download text-primary"
                        (click)="
                          downloadAttachment(
                            deviceSerialImageFiles.documentUrl,
                            deviceSerialImageFiles.fileName,
                            deviceSerialImageFiles.rmaDocId
                          )
                        "
                      ></em>
                      <em
                        (click)="deleteUploadedAttachment(deviceSerialImageFiles?.id, true, true)"
                        nbtooltip="Delete"
                        nbtooltipplacement="top"
                        nbtooltipstatus="text-danger"
                        aria-hidden="true"
                        class="fa fa-times-circle text-danger ps-2"
                      ></em>
                    </span>
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-8 border-left">
                <span class="d-flex align-items-center justify-content-center">Files</span>
                <ng-container *ngIf="deviceSerialFiles && deviceSerialFiles.length">
                  <div class="d-flex mt-2 image-container" *ngFor="let files of deviceSerialFiles; let i = index">
                    <ng-container *ngIf="!files.isDeleted && files.fileType !== 'image'">
                      <label
                        class="imageFilename cursor-pointer"
                        nbTooltip="{{ files?.fileName }}"
                        nbTooltipPlacement="top"
                        nbTooltipStatus="primary"
                      >
                        <a *ngIf="files?.documentUrl" [href]="files.documentUrl" target="_blank" [innerHTML]="files?.fileName"></a>
                        <span *ngIf="!files?.documentUrl">{{ files?.fileName }}</span>
                      </label>
                      <span class="ms-auto cursor-pointer">
                        <em
                          *ngIf="mode !== 'create'"
                          nbtooltip="Download"
                          nbtooltipplacement="top"
                          nbtooltipstatus="text-primary"
                          aria-hidden="true"
                          class="fa fa-download text-primary"
                          (click)="downloadAttachment(files.documentUrl, files.fileName, files.rmaDocId)"
                        ></em>
                        <em
                          (click)="deleteUploadedAttachment(i, true, false)"
                          nbtooltip="Delete"
                          nbtooltipplacement="top"
                          nbtooltipstatus="text-danger"
                          aria-hidden="true"
                          class="fa fa-times-circle text-danger ps-2"
                        ></em>
                      </span>
                    </ng-container>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
        <div class="row border-top">
          <div class="col-12 col-md-6 mb-3 pt-3">
            <div>
              <label class="label">Return Tracking Number</label>
              <input nbInput type="text" fullWidth formControlName="returnTrackingNumber" class="form-control" />
            </div>
          </div>
          <div class="col-12 col-md-6 mb-3 pt-3">
            <div class="dropZone" ngFileDragDrop (fileDropped)="getUploadedFiles($event, false)">
              <input type="file" #file (change)="getUploadedFiles($event.target.files, false)" [disabled]="!isActive" />
              <em class="fa fa-cloud-upload-alt text-primary iconimageupload" aria-hidden="true"></em><br />
              <h5 class="fw-bold">Drag & Drop file to attach</h5>
              <label style="text-transform: none" class="fw-bold">or Browse to choose a file </label>
            </div>
          </div>
          <div class="col-12 mb-3 pt-3">
            <div class="row">
              <div class="col-12 col-md-4">
                <span class="d-flex align-items-center justify-content-center">Photos</span>
                <div class="image-container" *ngIf="returnTrackingImageFiles && !returnTrackingImageFiles.isDeleted">
                  <img [src]="returnTrackingImageFiles.documentUrl" (click)="imagePopup(returnTrackingImageFiles)" />
                  <div class="d-flex mt-2" *ngIf="returnTrackingImageFiles">
                    <label
                      class="imageFilename cursor-pointer"
                      nbTooltip="{{ returnTrackingImageFiles?.fileName }}"
                      nbTooltipPlacement="top"
                      nbTooltipStatus="primary"
                      >{{ returnTrackingImageFiles?.fileName }}</label
                    >
                    <span class="ms-auto cursor-pointer">
                      <em
                        *ngIf="mode !== 'create'"
                        nbtooltip="Download"
                        nbtooltipplacement="top"
                        nbtooltipstatus="text-primary"
                        aria-hidden="true"
                        class="fa fa-download text-primary"
                        (click)="
                          downloadAttachment(
                            returnTrackingImageFiles.documentUrl,
                            returnTrackingImageFiles.fileName,
                            returnTrackingImageFiles.rmaDocId
                          )
                        "
                      ></em>
                      <em
                        (click)="deleteUploadedAttachment(returnTrackingImageFiles?.id, false, true)"
                        nbtooltip="Delete"
                        nbtooltipplacement="top"
                        nbtooltipstatus="text-danger"
                        aria-hidden="true"
                        class="fa fa-times-circle text-danger ps-2"
                      ></em>
                    </span>
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-8 border-left">
                <span class="d-flex align-items-center justify-content-center">Files</span>
                <ng-container *ngIf="returnTrackingFiles && returnTrackingFiles.length">
                  <div class="d-flex mt-2 image-container" *ngFor="let files of returnTrackingFiles; let i = index">
                    <ng-container *ngIf="!files.isDeleted && files.fileType !== 'image'">
                      <label
                        class="imageFilename cursor-pointer"
                        nbTooltip="{{ files?.fileName }}"
                        nbTooltipPlacement="top"
                        nbTooltipStatus="primary"
                      >
                        <a *ngIf="files?.documentUrl" [href]="files.documentUrl" target="_blank" [innerHTML]="files?.fileName"></a>
                        <span *ngIf="!files?.documentUrl">{{ files?.fileName }}</span>
                      </label>
                      <span class="ms-auto cursor-pointer">
                        <em
                          nbtooltip="Download"
                          nbtooltipplacement="top"
                          nbtooltipstatus="text-primary"
                          aria-hidden="true"
                          class="fa fa-download text-primary"
                          (click)="downloadAttachment(files.documentUrl, files.fileName, files.rmaDocId)"
                        ></em>
                        <em
                          (click)="deleteUploadedAttachment(i, false, false)"
                          nbtooltip="Delete"
                          nbtooltipplacement="top"
                          nbtooltipstatus="text-danger"
                          aria-hidden="true"
                          class="fa fa-times-circle text-danger ps-2"
                        ></em>
                      </span>
                    </ng-container>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
  <div class="modal-footer ModalFooter">
    <button nbButton status="basic" size="medium" (click)="onCancel()" type="button">Cancel</button>
    <button nbButton status="primary" (click)="onRmaForDevice()" size="medium" id="deviceSubmit" type="submit">
      {{ mode === 'create' ? 'Save' : 'Update' }}
    </button>
  </div>
</div>
