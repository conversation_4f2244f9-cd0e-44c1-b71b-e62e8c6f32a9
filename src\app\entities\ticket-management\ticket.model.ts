import { AppConstants } from '../../@shared/constants';
import { Dropdown } from '../../@shared/models/dropdown.model';

export class Tickets {
  public id: number;
  public ticketNumber: string;
  public priority: 0;
  public priorityStr: string;
  public customerName: string;
  public portfolioName: string;
  public siteName: string;
  public deviceType: string;
  public issue: string;
  public open: string;
  public close: string;
  public status: number;
  public statusStr: string;
  public isClose = true;
  public deviceModel: string;
  public deviceLabel: string;
  public customerPortfolio: string;
  public fullActivityLog: string;
  public truckRoll: number;
  public affectedkWac: number;
  public productionLossStr: boolean;
  public show? = false;
  public ticketDevices: TicketDevices;
  public isResolve: boolean;
  public isSelected? = false;
  public truckRollNumbers: string[];
  public ticketTypeId: number;
  public ticketTypeName: string;
  public fullNotes: string;
  public estNumber: number;
  public additionalHours: number;
  public estimateTotal: number;
  public estimateStatus: number;
  public customerPO: string;
  public approvedByName: string;
  public approvedOn: string;
  public isBankofHours: boolean;
  public isContractedHours: boolean = false;
  public contractedHours: number;
  public remainingContractHours: number;
}

export class TicketDevices {
  public alertStatus: string;
  public deviceId: number;
  public deviceName: string;
  public isResolve: boolean;
}

export class Resolutions {
  public date: string;
  public id: number;
  public isDeleted: false;
  public resolution: string;
  public ticketId: number;
}

export class TicketsList {
  public ticketLists: Tickets[];
  public totalTickets: number;
  public assignedTicketCount: number;
  public openTicketCount: number;
  public reportedByMeTicketCount: number;
  public closeTicketCount: number;
  public pendingTicketCount: number;
}

export class TicketFilter {
  public customerId: number[] = [];
  public portfolioId: number[] = [];
  public siteId: number[] = [];
  public priority: number[] = [];
  public state: string[] = [];
  public status: number[] = [];
  public page = 0;
  public sortBy = 'Open';
  public direction = 'desc';
  public itemsCount = +AppConstants.rowsPerPage;
  public isExport = false;
  public searchBy = 'All';
  public searchValue = '';
}

export class TicketsFilter extends TicketFilter {
  public openDate: { start: Date; end: Date };
  public closeDate: { start: Date; end: Date };
}

export class TicketFilterCopy extends TicketFilter {
  public openDate: { start: string; end: string };
  public closeDate: { start: string; end: string };
}

export const TICKETSFILTERLIST = {
  customer: 'Customer',
  portfolio: 'Portfolio',
  site: 'Site',
  ticketNumber: 'Number',
  priority: 'Priority',
  state: 'State',
  status: 'Status',
  openDate: 'Opened',
  closeDate: 'Closed'
};

export class TicketAdd {
  public id;
  public customerId = null;
  public portfolioId = null;
  public siteId = null;
  public siteNumber: string;
  public qeSiteId: string;

  public open: Date | string = new Date();
  public close: Date | string = null;
  public status = 1;
  public priority = 2;
  public issue = null;
  public rmaNumber = null;
  public returnRmaTracking = null;
  public productionLoss = 2;
  public affectedkWac = null;
  public truckRole = null;
  public impact = null;
  public dropBoxLink = null;
  public ticketNumber = null;
  public customerContact: CustomerContact[] = [];
  public ticketDeviceMaps: TicketDeviceMaps[] = [];
  public ticketAudits: TicketAudit[] = [];
  public ticketAttachments: TicketAttachment[] = [];
  public ticketEstimateAttachments: TicketAttachment[] = [];
  public ticketExclusions: TicketExclusions[] = [];
  public totalTimeSpent: number;
  public createdBy: string;
  public hasExclusion = false;
  public estNumber: number = null;
  public additionalHours: number = null;
  public estFileUrl: number = null;
  public estKWhLoss: number = null;
  public lossType: number = null;
  public lossTypeStr: string = null;
  public oemCaseNumber: string = null;
  public customerName: string;
  public portfolioName: string;
  public siteName: string;
  public deviceTypeName: string;
  public reletedTickets: ReletedTicketsMapDto[] = [];
  public ticketRMAs: TicketRMAs[] = [];
  public reletedTicketsDetails: ReletedTicketsDetailsMapDto[] = [];
  public ticketEstimates: TicketEstimates[] = [];
  public listOfTicketEstimateApproval: TicketEstimatesApproval[] = [];
  public ticketBillingStatusID: number;
  public ticketBillingStatusDescription: string;
  public ticketTypeId = 1;
  public ticketTypeName = TicketTypeMapping.find(item => item.id === this.ticketTypeId).name;
  public exclusionTypeId: number = null;
  public isContractedHours: boolean = false;
  public isBankofHours: boolean = false;
  public isGADSReporting: boolean;
  public contractedHours: number;
  public remainingContractHours: number;
}

export class TicketRMAs {
  public rmaId = 0;
  public ticketId = 0;
  public siteDeviceId: number;
  public deviceName: string;
  public deviceSerialNumber: string;
  public returnTrackingNumber: string;
  public deviceMFG: string;
  public startDate: string;
  public completeDate: string;
  public isReturnRequired: boolean = false;
  public isRMAComplete: boolean = false;
  public rmaNumber: number;
  public isDeleted = false;
  public isActive: boolean = false;
  public deviceAttachments: RmaAttachments[];
  public returnAttachments: RmaAttachments[];
}

export class RmaAttachments {
  public rmaDocId = 0;
  public rmaId = 0;
  public ticketId = 0;
  public siteDeviceId = 0;
  public documentUrl: string;
  public fileName: string;
  public fileExtension: string;
  public fileType: string;
  public docType: string;
  public isDeleted = false;
  public attachFile = null;
}
export class CustomerContact {
  public id = 0;
  public ticketId = 0;
  public customerEmail = null;
  public isDeleted = false;
}

export class TicketAudit {
  public id = 0;
  public ticketId = 0;
}

export class TicketAction {
  public id = 0;
  public ticketId = 0;
  public ticketAuditId = 0;
  public action = null;
  public actionSummary: string;
  public actionSummaryJson = [];
  public userName = null;
}
export class TicketAttachment {
  public id = 0;
  public ticketId = 0;
  public documentUrl = null;
  public isDeleted = false;
  public fileExtension: string;
  public fileType: string;
  public fileName: string;
  public ticketEstimateId: number;
  public ticketEstId: number;
  public size: string;
  public file: any;
}

export class TicketComment {
  public id = 0;
  public ticketId = 0;
  public userId = null;
  public comment = null;
  public userName = null;
  public isDeleted = false;
  public isEdited = false;
}

export class TicketActivityLogs {
  public id = 0;
  public date: Date = new Date();
  public description = null;
  public totalMaterialCost = 0;
  public truckRole: boolean;
  public truckRoleCount: number;
  public ticketId = 0;
  public totalHours = null;
  public createdBy: 0;
  public createdDate: string;
  public createUser: string;
  public isDeleted = false;
  public isEdited = false;
  public ticketFieldTechDto: FieldTechs[] = [];
  public ticketActivityMaterials: TicketMaterials[] = [];
  public jhaMapPost: number[] = [];
  public jhaMap: number[] = [];
  public isResolve: boolean;
  public truckRollNumber = null;
  public truckRollId = null;
  public truckRollType = null;
  public imgCount: number = 0;
  public isActivityContractedHours: boolean = false;
  public isADeviceOutage: boolean = null;
  public ticketSiteDeviceOutage: TicketSiteDeviceOutage[] = [];
  public ticketActivityFaultCodeDeviceMaps: TicketActivityFaultCodeDeviceMap[] = [];
}

export class FieldTechs {
  public id = 0;
  public ticketActivityId = 0;
  public userId = null;
  public userName: string;
  public operationService: string;
  public operationServiceId: number;
  public hours = 0;
  public isDeleted = false;
}

export class TicketMaterials {
  public id = 0;
  public ticketActivityId = 0;
  public materialCost = 0;
  public material = '';
  public costTypeID = null;
  public costTypeName = '';
  public isDeleted = false;
}

export class TicketActivityDevice {
  public siteDeviceId: number;
  public siteDeviceName: string;
  public groupName?: string;
}

export class TicketActivityFaultCodeDeviceMap {
  public id = 0;
  public ticketActivityId = 0;
  public faultCodes: string[] = [];
  public faultCodeDevices: TicketActivityDevice[] = [];
  public selectedDeviceIds: number[] = [];
  public isDeleted = false;
}

export class DeviceListForTicketActivity {
  public deviceId: number = null;
  public deviceName: string = '';
  public startDateTimeUTC: string | null = null;
  public minStartDateTimeUTCDate: Date | null = null;
  public maxStartDateTimeUTCDate: Date | null = null;
  public maxEndDateTimeUTCDate: Date | null = null;
  public startDateTimeUTCDate: Date | null = null;
  public endDateTimeUTC: string | null = null;
  public endDateTimeUTCDate: Date | null = null;
  public minEndDateTimeUTCDate: Date | null = null;
  public overrideLosesKW: number | null = null;
  public isPlannedDowntime: boolean = false;
  public refTicketNumber: string | null = null;
  public acNameplateKW: number | null = null;
}

export class TicketSiteDeviceOutage extends DeviceListForTicketActivity {
  public id: number = 0;
  public ticketActivityId: number = 0;
  public comments: string = '';
  public isDeleted: boolean = false;
}

export class TicketCostType {
  public costTypeID: number;
  public costTypeName: string;
}

export class TicketDeviceTypeList {
  public id: number;
  public name: string;
  public siteDevices: TicketDeviceModelList[] = [];
}

export class TicketDeviceModelList {
  public id: number;
  public name: string;
  public mfg: string;
  public label: string;
  public size: string;
  public model: string;
  public deviceTypeName: string;
  public deviceTypeId: number;
  public alertStatus?: string;
  public acNameplateKW: number | null = null;
  public comments: string | null = null;
  public deviceId: number = 0;
  public deviceName: string | null = null;
  public deviceOutageId: number = 0;
  public endDateTimeUTC: string | null = null;
  public isADeviceOutage: boolean;
  public isPlannedDowntime: boolean;
  public isSelected: boolean;
  public overrideLosesKW: number | null = null;
  public refTicketNumber: string | null = null;
  public serialNumber: string = '';
  public startDateTimeUTC: string | null = null;
  public startDateTimeUTCDate: Date | null = null;
  public minStartDateTimeUTCDate: Date | null = null;
  public maxStartDateTimeUTCDate: Date | null = null;
  public maxEndDateTimeUTCDate: Date | null = null;
  public endDateTimeUTCDate: Date | null = null;
  public minEndDateTimeUTCDate: Date | null = null;
}
export class CloseTicketDto {
  public id: number;
  public productionLoss = null;
  public affectedkW: number;
  public affectedhour: number;
  public affectedkWh: number;
  public timeSpent: number;
  public date: string;
  public materials: string;
  public description: string;
  public resolution: string;
  public comments: string;
  public isAddedResolution = false;
  public isAddedComment = false;
  public status: number;
  public truckRole = 0;
}

export class OpenTicketDto {
  public id: number;
  public timeSpent: number;
  public date: string;
  public status: number;
  public materials: string;
  public description: string;
  public resolution: string;
  public comments: string;
  public isAddedResolution = false;
  public isAddedComment = false;
}

export class TicketCreated {
  public entryid: number;
  public id: number;
  public message: string;
  public status: number;
}

export class TicketExclusions {
  public id = 0;
  public ticketId = 0;
  public notes: string;
  public to: string | Date;
  public from: string | Date;
  public isDeleted = false;
}

export class TicketDeviceMaps {
  public id = 0;
  public ticketId = 0;
  public deviceTypeId = null;
  public deviceTypeName = null;
  public siteDeviceId = null;
  public label = null;
  public manufacturer = null;
  public siteDeviceName: string;
  public size = 0;
  public alertStatus: string;
  public comments: string;
  public deviceOutageId: number;
  public isADeviceOutage: boolean;
  public isDeleted: boolean;
  public isPlannedDowntime: boolean;
  public deviceId: number = null;
  public deviceName: string = '';
  public startDateTimeUTC: string | null = null;
  public startDateTimeUTCDate: Date | null = null;
  public minStartDateTimeUTCDate: Date | null = null;
  public maxStartDateTimeUTCDate: Date | null = null;
  public maxEndDateTimeUTCDate: Date | null = null;
  public endDateTimeUTC: string | null = null;
  public endDateTimeUTCDate: Date | null = null;
  public minEndDateTimeUTCDate: Date | null = null;
  public overrideLosesKW: number | null = null;
  public refTicketNumber: string | null = null;
  public acNameplateKW: number | null = null;
  public faultCode: string[] = [];
}

export class DeviceInformation {
  public id = 0;
  public ticketId = 0;
  public deviceTypeId = null;
  public siteDeviceId = null;
  public label = null;
  public manufacturer = null;
  public siteDeviceName: string;
  public size = 0;
}

export class CustomerDropdown {
  public id: number;
  public name: string;
  public email: string;
}

export class ContactDropdown {
  public id: number;
  public name: string;
  public contactEmails: CustomerContactList[];
}
export class CustomerContactList {
  public id = 0;
  public contactName: string;
  public customerTitle: string;
  public email: string;
  public phoneNumber: number;
  public useAsJHAContact: boolean;
  public useAsTicketContact: boolean;
  public siteId: number;
}

export class ReletedTicketsMapDto {
  public id = 0;
  public ticketId: number;
  public type = 7;
  public ticketNumber = '';
  public ticketStatus = '';
  public ticketPriority = '';
  public relatedTicketId: number = null;
  public customerId: number;
}

export class ReletedTicketsDetailsMapDto {
  public groupName: string;
  public tickets: ReletedTicketsMapDto[] = [];
}

export const TicketStatusMapping = [
  { id: 1, name: 'Open' },
  { id: 2, name: 'Pending' },
  { id: 3, name: 'Closed' }
];

export const TicketTypeMapping = [
  { id: 1, name: 'CM' },
  { id: 2, name: 'Snow' },
  { id: 3, name: 'Vegetation' },
  { id: 4, name: 'Non-conformance' },
  { id: 5, name: 'Project' },
  { id: 6, name: 'Monitoring (DG)' }
];

export const TicketPriorityMapping = [
  { id: 1, name: 'Low' },
  { id: 2, name: 'Medium' },
  { id: 3, name: 'High' },
  { id: 4, name: 'Safety' }
];

export const TicketCommentType = [
  { id: 1, value: 'Private' },
  { id: 0, value: 'Public' }
];

export const ProductionLossList = [
  { id: 1, value: 'No' },
  { id: 0, value: 'Yes' },
  { id: 2, value: '---' }
];

export const HoursList: Dropdown[] = [
  { id: 0, name: '0' },
  { id: 0.5, name: '0.5' },
  { id: 1, name: '1' },
  { id: 1.5, name: '1.5' },
  { id: 2, name: '2' },
  { id: 2.5, name: '2.5' },
  { id: 3, name: '3' },
  { id: 3.5, name: '3.5' },
  { id: 4, name: '4' },
  { id: 4.5, name: '4.5' },
  { id: 5, name: '5' },
  { id: 5.5, name: '5.5' },
  { id: 6, name: '6' },
  { id: 6.5, name: '6.5' },
  { id: 7, name: '7' },
  { id: 7.5, name: '7.5' },
  { id: 8, name: '8' },
  { id: 8.5, name: '8.5' },
  { id: 9, name: '9' },
  { id: 9.5, name: '9.5' },
  { id: 10, name: '10' },
  { id: 10.5, name: '10.5' },
  { id: 11, name: '11' },
  { id: 11.5, name: '11.5' },
  { id: 12, name: '12' }
];

export const TicketColumnFilter = [
  { id: 'All', name: 'All' },
  { id: 'Number', name: 'Number' },
  { id: 'ESTNumber', name: 'Estimate Number' },
  { id: 'Customer', name: 'Customer' },
  { id: 'Portfolio', name: 'Portfolio' },
  { id: 'Site', name: 'Site' },
  { id: 'Device Label', name: 'Device' },
  { id: 'Issue', name: 'Issue' },
  { id: 'Status', name: 'Status' },
  { id: 'OEM', name: 'OEM Case Number' },
  { id: 'RMA', name: 'RMA Number' },
  { id: 'ReturnRMA', name: 'Return RMA Tracking' },
  { id: 'Activity', name: 'Activity Logs' }
];

export class jhaDetail {
  id?: number;
  name?: string;
  url?: string;
}

export enum JhaThreeClickItem {
  CREATE_NEW_JHA_BTN,
  JHA_SELECTION_DROPDOWN_OPEN
}
export class JhaThreeEventDataClass {
  clickedItem: JhaThreeClickItem;
  isClicked: boolean;

  constructor(clickedItem: JhaThreeClickItem, isClicked: boolean = true) {
    this.clickedItem = clickedItem;
    this.isClicked = isClicked;
  }
}

export interface TicketAlerts {
  outageId: number;
  deviceId: number;
  hardwareId: string;
  startDate: string;
  duration: number;
  deviceName: string;
  deviceType: number;
  isSelected?: boolean;
}

export interface TicketEstimates {
  ticketId: number;
  ticketEstId: number;
  estNumber: number;
  additionalHours: number;
  estimateTotal: number;
  ticketEstimateAttachments: TicketEstimateAttachments[];
  randomGUID: string;
  isDeleted?: boolean;
  ticketEstimateId: number;
  estimateStatus: number;
  customerPO: string;
  approvedBy: string;
  approvedByName: string;
  approvedOn: string;
}
export interface TicketEstimateAttachments {
  id: number;
  ticketEstimateId: number;
  documentUrl: string;
  isDeleted: boolean;
  size: string;
  fileName: string;
  fileExtension: string;
  fileType: string;
  ticketId: number;
  ticketEstId: number;
  file: any;
}

export enum NavigationBack {
  TICKETS = 'TICKETS',
  BILLING = 'BILLING',
  RMA_REPORT = 'RMA_REPORT',
  AUDIT_DISPATCH = 'AUDIT_DISPATCH'
}

export interface TruckRollDropDown {
  truckRollId: number;
  truckRollNumber: string;
  truckRollType: number;
  truckRollTypeName: string;
}
export interface GetTruckRollDropDownParams {
  truckRollId: number;
  siteId: number;
  ticketActivityId: number;
  activityDate: string;
}

export class ServicesRateDropDownList {
  public id: number | null = null;
  public name: string = '';
  public isActive: boolean;
  public abbreviation: string = '';
  public siteNumber: string = '';
  public customerId: number | null = null;
  public portfolioId: number | null = null;
  public isAutomationSite: boolean;
  public isArchive: boolean;
}

export const statusDropDownList = [
  {
    id: 1,
    name: 'Pending Approval',
    disabled: false
  },
  {
    id: 2,
    name: 'Declined',
    disabled: false
  },
  {
    id: 3,
    name: 'Approved',
    disabled: false
  },
  {
    id: 4,
    name: 'Pending Billing',
    disabled: false
  },
  {
    id: 5,
    name: 'Billing',
    disabled: false
  },
  {
    id: 6,
    name: 'Billed',
    disabled: false
  }
];

export enum ReopenedDirectionType {
  None = 0,
  ReopenedFrom = 1,
  ReopenedAs = 2
}

export class FileListPaginationParams {
  public currentPage: number = 1;
  public itemsCount: number = 10;
  public pageSize: number = 10;
}
export class AttachmentListResponse {
  public totalCount: number;
  public fileGallery: CommonAttachmentsList[];
}

export class CommonAttachmentsList {
  public id: number;
  public fileUrl: string;
  public thumbnailUrl: string;
  public customerId: number = null;
  public portfolioId: number = null;
  public siteId: number = null;
  public entityId: number = null;
  public entityNumber: string = '';
  public fileName: string = '';
  public conditionalTag: number[] = [];
  public conditionalTagTxt: string[] = [];
  public deviceTag: number[] = [];
  public deviceTagTxt: string[] = [];
  public fileTag: number[] = [];
  public fileTagTxt: string[] = [];
  public notes: string;
  public fileType: string = '';
  public createdBy: string = '';
  public createdDate: string = '';
  public isCustomerFacing: boolean;
  public isSelectedForPreview: boolean;
}

export interface TruckRollData {
  truckRollId: number;
  truckRollCount: number;
  truckRollNumber: string;
  customerName: string;
  customerId: number;
  portfolioName: string;
  portfolioId: number;
  siteName: string;
  siteId: number;
  dispatchDate: string;
  totalMaterialCost: number;
  totalHours: number;
  galleryTotalCount: number;
  ticketList: TicketList[];
  imageGalleryList: CommonAttachmentsList[];
}

export interface TicketList {
  ticketNumber: string;
  status: number;
  statusStr: string;
  isResolve: boolean;
  fieldTechs: string;
  materialCost: number;
  hours: number;
}

export interface TicketBillingStatusesResponse {
  ticketBillingStatusID: number;
  description: string;
}
export class TicketTypeDropdownDto {
  ticketTypeId: number;
  ticketTypeName: string;
}
export class TicketBulkEdit {
  public ticketIds: number[] = [];
  public ticketStatus: number | null = null;
  public closedDate: string | null = null;
  public billingStatus: number | null = null;
  public priority: number | null = null;
  public issueAction: number | null = null; // 0 -- Append, 1 -- overWrite
  public issueTxt: string = '';
  public productionLoss: number | null = null;
  public affectedKWAC: number | null = null;
  public lossType: number | null = null;
  public estKWHLoss: number | null = null;
  public comment: string = '';
  public isSendEmail: boolean = false;
  public activity: ActivityDetails;
  public activityDate: string | null = null;
  public description: string = '';
  public isResolved: boolean = false;
  public materials: string = '';
  public materialCost: number | null = null;
}

export class ActivityDetails {
  public activityDate: string | null = null;
  public description: string = '';
  public isResolved: boolean = false;
  public materials: string = '';
  public materialCost: number | null = null;
}

export const fieldNameMap: { [key: string]: string } = {
  ticketIds: 'Ticket IDs',
  ticketStatus: 'Ticket Status',
  closedDate: 'Closed Date',
  billingStatus: 'Billing Status',
  priority: 'Priority',
  issueAction: 'Issue Action',
  issueTxt: 'Issue',
  productionLoss: 'Production Loss',
  affectedKWAC: 'Affected KWAC',
  lossType: 'Loss Type',
  estKWHLoss: 'Est. KWh Loss',
  comment: 'Comment',
  activityDate: 'Activity Date',
  description: 'Description',
  isResolved: 'Resolved',
  materials: 'Materials',
  materialCost: 'Material Cost',
  isSendEmail: 'Send Email',
  openedDate: 'Opened',
  isLinkTicket: 'Is Link Ticket',
  newTicketIssueTxt: 'New Ticket Issue Text',
  issueActionClose: 'Issue Action'
};

export const idMappings: { [key: string]: { [id: number | string]: string } } = {
  ticketStatus: {
    1: 'Open',
    2: 'Pending',
    3: 'Closed'
  },
  priority: {
    1: 'Low',
    2: 'Medium',
    3: 'High',
    4: 'Safety'
  },
  issueAction: {
    1: 'Append',
    2: 'OverWrite'
  },
  productionLoss: {
    1: 'No',
    0: 'Yes',
    2: '---'
  },
  isResolved: {
    true: 'Yes',
    false: 'No'
  },
  issueActionClose: {
    1: 'Use Closed Ticket Issue',
    2: 'Use Closed Ticket Issue and Append',
    3: 'Overwrite/New Ticket Issue'
  }
};

export class TicketBulkCreation {
  public entityIds: number[] = [];
  public isSiteIds: boolean = false;
  public openedDate: string | Date | null = new Date();
  public priority = 2;
  public issueTxt: string;
  public productionLoss = 2;
  public estKWHLoss: number = null;
  public lossType: number | null = null;
  public isSendEmail: boolean = false;
  public affectedkWac = null;
}

export class TicketBulkCLoseReOpen {
  public ticketIds: number[] = [];
  public closedDate: string | null = null;
  public billingStatus: number | null = 1;
  public issueTxt: string = '';
  public activity: ActivityDetails;
  public activityDate: string | null = null;
  public description: string = '';
  public isResolved: boolean = false;
  public materials: string = '';
  public materialCost: number | null = null;
  public comment: string = '';
  public isLinkTicket: boolean = false;
  public openedDate: string | Date | null = new Date();
  public issueAction: number | null = null;
  public issueActionClose: number | null = null;
  public newTicketIssueTxt: string = '';
  public isSendEmail: boolean = false;
}

export const closeTicketTableFields = [
  'ticketIds',
  'isSendEmail',
  'isLinkTicket',
  'openedDate',
  'issueAction',
  'issueActionClose',
  'newTicketIssueTxt'
];
export const openTicketTableFields = [
  'ticketIds',
  'isSendEmail',
  'closedDate',
  'billingStatus',
  'issueTxt',
  'comment',
  'activity',
  'activityDate',
  'description',
  'isResolved',
  'materials',
  'materialCost'
];

export class BulkActionResponse {
  public entryid: number;
  public id: number;
  public message: string;
  public status: number;
  public entityIds = [];
}

export const cmTicketPageFilterKeys = [
  'customerIds',
  'portfolioIds',
  'siteIds',
  'priorityIds',
  'states',
  'statusIds',
  'userIds',
  'regionIds',
  'subregionIds',
  'openDate',
  'closeDate',
  'activityRange',
  'isResolve',
  'truckRoll',
  'ticketEstimateStatusIds',
  'ticketTypeIds',
  'TicketBillingStatusIds',
  'costTypeIds',
  'searchValue'
];

export class ContactedHoursToggleResponse {
  public success: boolean;
  public message: string = '';
  public contractedHours: number;
}

export class CheckDeviceOutageForSelectedTicketsResponse {
  public message: string;
  public ticketIds = [];
  public suggestedCloseDate: string | null = null;
}

export enum DeviceOutageForSelectedTicketsModalClickEnum {
  CONTINUE = 'continue',
  CANCEL = 'cancel',
  CLOSE = 'close',
  CHANGE_CLOSE_DATE = 'change_close_date'
}

export class TicketEstimatesApproval {
  public id: number;
  public ticketId: number;
  public ticketEstimateApprovalStatusID: number = 1;
  public ticketEstimateApprovalStatusName: string;
  public notes: string;
  public recipients: string;
  public actionReason: string;
  public actionBy: number;
  public actionByName: string;
  public actionOn: string;
  public createdBy: number;
  public createdDate: string;
  public createdByName: string;
  public isDeleted?: boolean;
}
export class TicketEstimatesApprovalStatus {
  ticketEstimateApprovalStatusID: number;
  name: string;
}

export enum ApprovalStatus {
  Pending = 1,
  Denied = 2,
  Approved = 3
}

export const ApprovalStatusName: Record<ApprovalStatus, string> = {
  [ApprovalStatus.Pending]: 'Pending Approval',
  [ApprovalStatus.Denied]: 'Denied',
  [ApprovalStatus.Approved]: 'Approved'
};
