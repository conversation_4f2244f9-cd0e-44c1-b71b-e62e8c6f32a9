export class NotificationListResponse {
  totalCount: number;
  items: NotificationItem[];
}

export class NotificationItem {
  id?: number;
  customerId: number;
  customerName: string;
  portfolioIds: number[];
  siteIds: number[];
  portfolioNameStr: string;
  siteNameStr: string;
  notificationTypeNameStr: string;
  notificationTypeIds: number[];
  isIncludePhotos: boolean;
  includePhotos: boolean;
  isActive: boolean;
  isMissingConfig: boolean;
  isInactive: boolean;
  status: string;
}

export class GetNotificationByIdResponse {
  data: NotificationItem;
  message: string;
  success: boolean;
}

export class NotificationType {
  id: number;
  name: string;
}
