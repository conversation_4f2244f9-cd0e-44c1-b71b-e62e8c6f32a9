<nb-card class="utility-assessment-add-edit-spinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <h6>{{ isEdit ? 'Edit' : 'Add' }} Assessment</h6>
  </nb-card-header>
  <nb-card-body>
    <form name="utilityAssessmentForm" #utilityAssessmentForm="ngForm" aria-labelledby="title" autocomplete="off">
      <div class="form-group row my-1">
        <div class="col-md-6">
          <label class="label" for="input-customerId">Customer<span class="ms-1 text-danger">*</span></label>
          <ng-select
            appendTo="body"
            name="customerId"
            #customerId="ngModel"
            [items]="customerList"
            (change)="
              onCustomerSelect(); utilityAssessment.portfolioId = null; utilityAssessment.siteIds = []; utilityAssessment.siteId = null
            "
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="utilityAssessment.customerId"
            notFoundText="No Customer Found"
            placeholder="Select Customer"
            [clearable]="false"
            [required]="true"
          >
          </ng-select>
          <sfl-error-msg [control]="customerId" [isFormSubmitted]="utilityAssessmentForm?.submitted" fieldName="Customer"></sfl-error-msg>
        </div>
        <div class="col-md-6">
          <label class="label" for="input-portfolioId">Portfolio<span class="ms-1 text-danger">*</span></label>
          <ng-select
            appendTo="body"
            name="portfolioId"
            #portfolioId="ngModel"
            [items]="portfolioList"
            (change)="onPortfolioSelect(); utilityAssessment.siteIds = []; utilityAssessment.siteId = null"
            (clear)="utilityAssessment.siteIds = []; utilityAssessment.siteId = null"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="utilityAssessment.portfolioId"
            notFoundText="No Portfolio Found"
            placeholder="Select Portfolio"
            [clearable]="false"
            [required]="true"
          >
          </ng-select>
          <sfl-error-msg [control]="portfolioId" [isFormSubmitted]="utilityAssessmentForm?.submitted" fieldName="Portfolio"></sfl-error-msg>
        </div>
      </div>
      <div class="form-group row my-1">
        <div class="col-md-6">
          <ng-container *ngIf="!isEdit; else createMode">
            <label class="label" for="input-siteIds">Site<span class="ms-1 text-danger">*</span></label>
            <ng-select
              appendTo="body"
              name="siteIds"
              #siteIds="ngModel"
              [multiple]="true"
              [items]="siteList"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="utilityAssessment.siteIds"
              notFoundText="No Site Found"
              placeholder="Select Site"
              [closeOnSelect]="false"
              [clearable]="false"
              (search)="onSiteSearchFilter($event)"
              (close)="filteredSiteIds = []"
              [required]="true"
            >
              <ng-template ng-header-tmp *ngIf="siteList && siteList.length">
                <button type="button" (click)="selectAndDeselectAllSite(true)" class="btn btn-sm btn-primary">Select all</button>
                <button type="button" (click)="selectAndDeselectAllSite(false)" class="btn btn-sm btn-primary ms-1">Unselect all</button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" name="item-{{ index }}" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
            <sfl-error-msg [control]="siteIds" [isFormSubmitted]="utilityAssessmentForm?.submitted" fieldName="Site"></sfl-error-msg>
          </ng-container>
          <ng-template #createMode>
            <label class="label" for="input-siteId">Site<span class="ms-1 text-danger">*</span></label>
            <ng-select
              appendTo="body"
              name="siteId"
              #siteId="ngModel"
              [multiple]="false"
              [items]="siteList"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="utilityAssessment.siteId"
              notFoundText="No Site Found"
              placeholder="Select Site"
              [closeOnSelect]="false"
              [clearable]="false"
              [required]="true"
            >
            </ng-select>
            <sfl-error-msg [control]="siteId" [isFormSubmitted]="utilityAssessmentForm?.submitted" fieldName="Site"></sfl-error-msg>
          </ng-template>
        </div>
        <div class="col-md-3">
          <label class="label" for="input-startYear">Start Year<span class="ms-1 text-danger">*</span></label>
          <ng-select
            appendTo="body"
            name="startYear"
            #startYear="ngModel"
            [items]="startYearList"
            [(ngModel)]="utilityAssessment.startYear"
            notFoundText="No Year Found"
            placeholder="Select Year"
            [clearable]="false"
            [required]="true"
            (ngModelChange)="onChangeStartYear(utilityAssessment.startYear)"
          >
          </ng-select>
          <sfl-error-msg [control]="startYear" [isFormSubmitted]="utilityAssessmentForm?.submitted" fieldName="Start Year"></sfl-error-msg>
        </div>
        <div class="col-md-3">
          <label class="label" for="input-endYear">End Year<span class="ms-1 text-danger">*</span></label>
          <ng-select
            appendTo="body"
            name="endYear"
            #endYear="ngModel"
            [items]="endYearList"
            [(ngModel)]="utilityAssessment.endYear"
            notFoundText="No Year Found"
            placeholder="Select Year"
            [clearable]="false"
            [required]="true"
            (change)="onChangeEndYear(utilityAssessment.startYear, utilityAssessment.endYear, endYear)"
          >
          </ng-select>
          <sfl-error-msg [control]="endYear" [isFormSubmitted]="utilityAssessmentForm?.submitted" fieldName="End Year"></sfl-error-msg>
        </div>
      </div>

      <nb-tabset fullWidth class="row my-1" #siteAddEditTabSet>
        <nb-tab
          *ngFor="let scopeWithFrequencyItem of utilityAssessment.scopeDetails"
          [tabTitle]="scopeWithFrequencyItem.scopeTypeName + ' Scope Details'"
          [badgeText]="getErrorNumber(utilityAssessmentForm, 'tab-' + scopeWithFrequencyItem.scopeTypeId)"
          [active]="true"
          badgeStatus="danger"
          [id]="'tab-' + scopeWithFrequencyItem.scopeTypeId"
        >
          <div class="form-group row">
            <ng-container *ngIf="scopeWithFrequencyItem.scopeList as scopeDetailOptions">
              <ng-container *ngFor="let scopeDetailOptionItem of scopeDetailOptions">
                <div class="col-lg-6 my-1">
                  <div class="row">
                    <label class="label" for="input-{{ scopeDetailOptionItem.valueKey }}"
                      >{{ scopeDetailOptionItem.name
                      }}<span class="ms-1 text-danger" *ngIf="scopeDetailOptionItem.isRequired">*</span></label
                    >
                  </div>
                  <div class="row g-2">
                    <div class="col-8">
                      <ng-container></ng-container>
                      <ng-select
                        appendTo="body"
                        [name]="scopeDetailOptionItem.valueKey"
                        [id]="scopeDetailOptionItem.valueKey"
                        #{{scopeDetailOptionItem.valueKey}}="ngModel"
                        [multiple]="scopeDetailOptionItem.isMultiple"
                        [items]="scopeDetailOptionItem.frequency"
                        bindLabel="name"
                        bindValue="id"
                        [(ngModel)]="scopeDetailOptionItem.values[scopeDetailOptionItem.valueKey]"
                        notFoundText="No Frequency Found"
                        placeholder="Select Frequency"
                        [closeOnSelect]="false"
                        [clearable]="false"
                        [required]="scopeDetailOptionItem.isRequired"
                      >
                      </ng-select>
                      <ng-container *ngIf="getFormControl(utilityAssessmentForm, scopeDetailOptionItem.valueKey) as scopeDetailFormControl">
                        <sfl-error-msg
                          [control]="scopeDetailFormControl"
                          [isFormSubmitted]="utilityAssessmentForm?.submitted"
                          [fieldName]="scopeDetailOptionItem.name"
                        ></sfl-error-msg>
                      </ng-container>
                    </div>
                    <div class="col-4">
                      <ng-select
                        appendTo="body"
                        [name]="scopeDetailOptionItem.valueKey + '-deviceType'"
                        [id]="scopeDetailOptionItem.valueKey + '-deviceType'"
                        #deviceTypeIds="ngModel"
                        [multiple]="scopeDetailOptionItem.deviceTypeSelection.isMultiple"
                        [items]="scopeDetailOptionItem.deviceTypes"
                        bindLabel="name"
                        bindValue="id"
                        [(ngModel)]="scopeDetailOptionItem.values.deviceTypeIds"
                        notFoundText="No Device Type Found"
                        placeholder="Select Device Type"
                        [closeOnSelect]="false"
                        [clearable]="true"
                        [required]="scopeDetailOptionItem.deviceTypeSelection.isRequired"
                        [disabled]="
                          scopeDetailOptionItem.values[scopeDetailOptionItem.valueKey] === 10 ||
                          scopeDetailOptionItem.values[scopeDetailOptionItem.valueKey] === null
                        "
                      >
                        <ng-template
                          ng-option-tmp
                          let-item="item"
                          let-item$="item$"
                          let-index="index"
                          *ngIf="scopeDetailOptionItem.deviceTypeSelection.isMultiple"
                        >
                          <input id="item-{{ index }}" type="checkbox" name="item-{{ index }}" [ngModel]="item$.selected" />
                          {{ item.name }}
                        </ng-template>
                        <ng-template
                          ng-multi-label-tmp
                          let-items="items"
                          let-clear="clear"
                          *ngIf="scopeDetailOptionItem.deviceTypeSelection.isMultiple"
                        >
                          <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                            <span class="ng-value-label">{{ item.name }}</span>
                            <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                          </div>
                          <div class="ng-value" *ngIf="items.length > 1">
                            <span class="ng-value-label">+{{ items.length - 1 }} </span>
                          </div>
                        </ng-template>
                      </ng-select>
                      <ng-container
                        *ngIf="
                          getFormControl(utilityAssessmentForm, scopeDetailOptionItem.valueKey + '-deviceType') as deviceTypeFormControl
                        "
                      >
                        <sfl-error-msg
                          [control]="deviceTypeFormControl"
                          [isFormSubmitted]="utilityAssessmentForm?.submitted"
                          [fieldName]="'Device Type for ' + scopeDetailOptionItem.name"
                        ></sfl-error-msg>
                      </ng-container>
                    </div>
                  </div>
                </div>
              </ng-container>
            </ng-container>
          </div>
        </nb-tab>
      </nb-tabset>

      <div class="form-group row my-1">
        <div class="col-md-12 text-end">
          <button
            nbButton
            status="primary"
            size="medium"
            type="submit"
            id="utilityAssessmentSubmit"
            (click)="onSubmitUtilityAssessmentForm(utilityAssessmentForm, existingAssessmentModalTemplate)"
            [disabled]="!utilityAssessmentForm.valid && userEnableToUpdateCreate"
            class="float-end m-1"
          >
            Save
          </button>
          <button nbButton status="basic" type="button" routerLink="/entities/utility/assessments" size="medium" class="float-end m-1">
            cancel
          </button>
        </div>
      </div>
    </form>
  </nb-card-body>
</nb-card>

<ng-template #existingAssessmentModalTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">The Scope already exists, please select another year.</h4>
    <button type="button" class="btn-close close pull-right" aria-label="Close" (click)="modalRef?.hide()">
      <span class="d-inline-block scale-2">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <p>The following sites are already having their Scope created:</p>
    <div class="container">
      <div class="table-responsive">
        <table class="table year-site-table">
          <thead>
            <tr>
              <ng-container *ngFor="let tableHeadItem of existingUtilityAssessment.tableHead">
                <th>{{ tableHeadItem }}</th>
              </ng-container>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let tableBodyItem of existingUtilityAssessment.tableBody">
              <tr>
                <ng-container *ngFor="let item of tableBodyItem">
                  <td>{{ item }}</td>
                </ng-container>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</ng-template>
