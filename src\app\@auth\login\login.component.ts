import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as CryptoJS from 'crypto-js';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import {
  SflOtpInputComponentOptions,
  SflOtpInputComponentOptionsClass
} from '../../@shared/components/otp-input/models/otp-input-default.config';
import { OtpInputComponent } from '../../@shared/components/otp-input/otp-input.component';
import { APP_ROUTES, AppConstants } from '../../@shared/constants';
import { SecurityQuestionItem, User } from '../../@shared/models/user.model';
import { AlertService } from '../../@shared/services';
import { CommonService } from '../../@shared/services/common.service';
import { StorageService } from '../../@shared/services/storage.service';
import { CustomerService } from '../../entities/customer-management/customer.service';
import { PortfolioService } from '../../entities/portfolio-management/portfolio.service';
import { QEAnalyticsModuleAndEnumList } from '../../entities/qe-analytics/models/qe-analytics.model';
import { QEAnalyticsService } from '../../entities/qe-analytics/services/qe-analytics.service';
import { SiteService } from '../../entities/site-management/site.service';
import { AuthService } from '../index';
import { UserAuthenticationReq, UserAuthenticationRes } from '../models/user-authentication.model';
import { QEAnalyticsGatheringService } from '../../entities/qe-analytics/services/qe-analytics-gathering.service';
import { AUTHORITY_ROLE_STRING, QE_MENU_MODULE_ENUM, QE_MENU_MODULE_USER_PERMISSION_ENUM, ROLE_TYPE } from '../../@shared/enums';

@Component({
  selector: 'qesolar-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class LoginComponent implements OnInit, OnDestroy {
  @ViewChild('codeEntryFailedButton') codeEntryFailedButton;
  @ViewChild('codeExpiredButton') codeExpiredButton;
  @ViewChild('accountLockedButton') accountLockedButton;
  @ViewChild('sflOtpInput') sflOtpInput: OtpInputComponent;

  user: any = {};
  userAuthenticationReqObj: UserAuthenticationReq = new UserAuthenticationReq();
  submitted = false;
  rememberMe = false;
  returnUrl: string;
  readonly ROUTES = APP_ROUTES;
  subscription: Subscription = new Subscription();
  loading = false;
  emailPassScreenHidden = false;
  userAuthenticationResObj: UserAuthenticationRes = new UserAuthenticationRes({});
  sflOtpInputOptions: SflOtpInputComponentOptions = new SflOtpInputComponentOptionsClass({
    hideInputValues: true,
    showInputValuesBtn: true,
    ariaLabels: ['*', '*', '*', '*', '*', '*']
  });
  securityQuestionList: SecurityQuestionItem[] = [];
  modalRef: BsModalRef;

  constructor(
    private readonly router: Router,
    private readonly authService: AuthService,
    private readonly route: ActivatedRoute,
    private readonly customerService: CustomerService,
    private readonly portfolioService: PortfolioService,
    private readonly siteService: SiteService,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService,
    private readonly modalService: BsModalService,
    private readonly qeAnalyticsService: QEAnalyticsService,
    private readonly alertService: AlertService,
    private readonly qeAnalyticsGatheringService: QEAnalyticsGatheringService
  ) {}

  ngOnInit() {
    this.subscription.add(
      this.commonService.autoLogout.subscribe({
        next: response => {
          if (response) {
            this.qeAnalyticsGatheringService.onQEUserLogoutSessionExpiredEmit().finally(() => {
              localStorage.clear();
              this.customerService.clearCustomer();
              this.portfolioService.clearPortfolio();
              this.siteService.clearSite();
            });
          }
        }
      })
    );
    this.storageService.clear(AppConstants.authenticationToken);
    this.storageService.clear(AppConstants.refreshToken);
    this.storageService.clear(AppConstants.isMFAUserLoggedIn);
    this.storageService.clear(AppConstants.qeAnalyticsSessionIdKey);
  }

  login(callback?) {
    this.loading = true;
    this.submitted = true;
    const cb = callback || function () {};
    return new Promise((resolve, reject) => {
      const userDetail = JSON.parse(JSON.stringify(this.userAuthenticationReqObj));
      if (this.userAuthenticationReqObj.password) {
        userDetail.password = this.setEncryption('sflEncryptedPassword', this.userAuthenticationReqObj.password);
      }
      this.subscription.add(
        this.authService.login(userDetail).subscribe({
          next: async (res: UserAuthenticationRes) => {
            this.submitted = false;
            if (res.isAuthenticated && res.id_Token && res.refresh_Token) {
              if (res.sessionId) {
                await this.isAuthenticatedUser();
              } else {
                setTimeout(() => {
                  this.qeAnalyticsGatheringService.onQEUserLogoutSessionExpiredEmit().finally(() => {
                    this.storageService.clearAll();
                    window.location.reload();
                  });
                }, 100);
              }
            } else {
              this.manageUserAuthentication(res);
            }
            this.loading = false;
            return cb();
          },
          error: err => {
            this.submitted = false;
            this.reset();
            reject(err);
            this.loading = false;
            return cb(err);
          }
        })
      );
    });
  }

  async isAuthenticatedUser(): Promise<void> {
    this.setPermission();
    try {
      await Promise.all([this.getUserAccount(), this.getQEMenuModuleList()]);
      this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || APP_ROUTES.DASHBOARD;
      this.router.navigateByUrl(this.returnUrl);
    } catch (error) {
      this.alertService.showErrorToast('Something went wrong. Please login again.');
      setTimeout(() => {
        this.router.navigate(['auth/login']);
        this.authService.clearCustomerCache().subscribe(res => {
          this.qeAnalyticsGatheringService.onQEUserLogoutSessionExpiredEmit().finally(() => {
            this.storageService.clearAll();
            window.location.reload();
          });
        });
      }, 100);
    }
  }

  setEncryption(keys, value) {
    const keySize = 256;
    const salt = CryptoJS.lib.WordArray.random(16);
    const key = CryptoJS.PBKDF2(keys, salt, {
      keySize: keySize / 32,
      iterations: 100
    });
    const iv = CryptoJS.lib.WordArray.random(128 / 8);
    const encrypted = CryptoJS.AES.encrypt(value, key, {
      iv: iv,
      padding: CryptoJS.pad.Pkcs7,
      mode: CryptoJS.mode.CBC
    });
    const result = CryptoJS.enc.Base64.stringify(salt.concat(iv).concat(encrypted.ciphertext));
    return result;
  }

  setPermission() {
    const token = this.storageService.get(AppConstants.authenticationToken),
      tokenData = token.split('.')[1],
      decodedTokenJSONData = window.atob(tokenData),
      decodedTokenData = JSON.parse(decodedTokenJSONData);

    this.storageService.set('userID', decodedTokenData.UserID);
    switch (decodedTokenData.role) {
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.ADMIN]:
        this.addAdminPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.PORTFOLIOMANAGER]:
        this.addPortfolioManagerPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.MANAGER]:
        this.addManagerPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.FIELDTECH]:
        this.addFieldTechPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.CUSTOMER]:
        this.addCustomerPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.ANALYST]:
        this.addAnalystPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.SUPPORT]:
        this.addSupportPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.DIRECTOR]:
        this.addDirectorPermission();
        break;
    }
  }

  addAdminPermission(): void {
    const adminPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: true,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: true,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: true,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: true
    };
    this.storageService.set(AppConstants.userPermissions, adminPermission);
  }

  addPortfolioManagerPermission(): void {
    const portfolioManagerPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: false,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: false,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };

    this.storageService.set(AppConstants.userPermissions, portfolioManagerPermission);
  }

  addAnalystPermission(): void {
    const analystPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: false,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: false,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: false,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };

    this.storageService.set(AppConstants.userPermissions, analystPermission);
  }

  addManagerPermission(): void {
    const managerPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: true,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: true,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };

    this.storageService.set(AppConstants.userPermissions, managerPermission);
  }

  addSupportPermission(): void {
    const supportPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: true,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: true,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };

    this.storageService.set(AppConstants.userPermissions, supportPermission);
  }

  addFieldTechPermission(): void {
    const fieldTechPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: false,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: true,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };
    this.storageService.set(AppConstants.userPermissions, fieldTechPermission);
  }

  addCustomerPermission(): void {
    const customerPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: false,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: false,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: false,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: false,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: false,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: false,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: false,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };
    this.storageService.set(AppConstants.userPermissions, customerPermission);
  }

  addDirectorPermission(): void {
    const directorPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: true,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: true,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: true,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: true
    };
    this.storageService.set(AppConstants.userPermissions, directorPermission);
  }

  getUserAccount(): Promise<void> {
    return new Promise<void>(resolve => {
      this.authService.getAccount().subscribe({
        next: (users: User) => {
          this.user = users;
          this.storageService.set(AppConstants.userKey, this.user);
          this.storageService.set('userDefaultFilter', this.user.userFilterSelection);
          resolve();
        }
      });
    });
  }

  getQEMenuModuleList(): Promise<void> {
    return new Promise<void>(resolve => {
      this.qeAnalyticsService.getQEMenuModuleList().subscribe({
        next: (res: QEAnalyticsModuleAndEnumList) => {
          resolve();
        },
        error: () => {
          resolve();
        }
      });
    });
  }

  getSecurityQuestionList(): void {
    this.loading = true;
    this.subscription.add(
      this.authService.getSecurityQuestionList().subscribe({
        next: (res: SecurityQuestionItem[]) => {
          this.securityQuestionList = res;
          this.loading = false;
        },
        error: () => {
          this.loading = false;
        }
      })
    );
  }

  reset(): void {
    if (this.sflOtpInput) {
      this.sflOtpInput.reset();
      this.userAuthenticationReqObj.mfaCode = null;
      this.userAuthenticationReqObj.questionID = null;
      this.userAuthenticationReqObj.question = '';
      this.userAuthenticationReqObj.questionAnswer = '';
    }
  }

  manageUserAuthentication(res: UserAuthenticationRes): void {
    this.userAuthenticationResObj = new UserAuthenticationRes(res);

    const removableKeys = ['isAuthenticated', 'id_Token', 'refresh_Token', 'isMFAUserLoggedIn', 'isForcedToChangePassword', 'sessionId'];

    removableKeys.forEach(key => delete res[key]);

    this.emailPassScreenHidden = Object.values(res).some(value => typeof value === 'boolean' && value);

    const actionMap: { [key: string]: () => void } = {
      isMFARequired: () => {},
      isWrongMFACode: () =>
        setTimeout(() => {
          if (!this.userAuthenticationResObj.isAccountLocked) {
            this.codeEntryFailedButton.nativeElement.click();
          }
        }, 1),
      isMFACodeExpire: () =>
        setTimeout(() => {
          if (!this.userAuthenticationResObj.isAccountLocked) {
            this.codeExpiredButton.nativeElement.click();
          }
        }, 1),
      isAccountLocked: () => setTimeout(() => this.accountLockedButton.nativeElement.click(), 1),
      isShowSecurityQuestion: () => {
        this.reset();
        this.getSecurityQuestionList();
      }
    };

    Object.entries(res).forEach(([key, value]) => {
      if (typeof value === 'boolean') {
        this.userAuthenticationResObj[key] = value;
      }
      if (value && actionMap[key]) {
        actionMap[key]();
      }
    });
  }

  openModal(template: TemplateRef<any>) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      ignoreBackdropClick: false
    };
    this.modalRef = this.modalService.show(template, ngModalOptions);
  }

  reloadWindow(): void {
    window.location.reload();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
