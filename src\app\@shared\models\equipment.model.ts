import { AppConstants } from '../constants/app.constant';

export class Equipment {
  public id: number;
  public equipmentModel: string;
  public deviceTypeId: number;
  public deviceTypeName: string;
  public mfg: string;
  public size: string;
  public deviceManualUrl: string;
  public deviceManualFileName: string;
  public deviceCount: number;
}

export class EquipmentFilterData {
  public equipmentCount: number;
  public equipments: Equipment[];
}

export class EquipmentFilter {
  public deviceTypeId: number;
  public search: string;
  public page = 0;
  public sortBy = 'DeviceModel';
  public direction = 'asc';
  public itemsCount = +AppConstants.rowsPerPage;
}

export const EQIPMENTFILTERLIST = {
  deviceType: 'Device Type',
  search: 'Mfg'
};
